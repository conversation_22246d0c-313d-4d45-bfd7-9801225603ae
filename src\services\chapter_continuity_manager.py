"""
九猫系统章节连贯性管理器
解决章节写作"断片"问题，同时控制成本指数级增长

核心策略：
1. 智能摘要压缩：将前序章节压缩为核心要点
2. 滑动窗口：只保留最近N章的详细信息
3. 分层存储：重要信息保留更久，细节信息快速淘汰
4. 成本控制：严格控制累积信息的总量
"""

import logging
from typing import Dict, List, Any, Optional
import json
import re
from datetime import datetime

logger = logging.getLogger(__name__)

class ChapterContinuityManager:
    """章节连贯性管理器"""

    # 配置参数
    DEFAULT_CONFIG = {
        "max_recent_chapters": 3,      # 保留最近3章的详细信息
        "max_summary_chapters": 10,    # 保留最近10章的摘要信息
        "max_summary_length": 300,     # 每章摘要最大长度
        "max_total_context_length": 2000,  # 总上下文最大长度
        "compression_ratio": 0.1,      # 压缩比例（10%）
        "enable_cost_control": True    # 启用成本控制
    }

    SIMPLIFIED_CONFIG = {
        "max_recent_chapters": 2,      # 精简版：只保留最近2章
        "max_summary_chapters": 5,     # 精简版：只保留5章摘要
        "max_summary_length": 200,     # 更短的摘要
        "max_total_context_length": 1000,  # 更严格的长度限制
        "compression_ratio": 0.05,     # 更高的压缩比例（5%）
        "enable_cost_control": True
    }

    # 新增：增强累积版配置
    ENHANCED_CUMULATIVE_CONFIG = {
        "max_recent_chapters": 5,      # 增强版：保留最近5章详细信息
        "max_summary_chapters": 20,    # 增强版：保留20章摘要
        "max_summary_length": 150,     # 超压缩摘要
        "max_total_context_length": 3000,  # 适度增加上下文长度
        "compression_ratio": 0.03,     # 超高压缩比例（3%）
        "enable_cost_control": True,
        "enable_cumulative_context": True,  # 启用累积上下文
        "cumulative_compression_factor": 0.8,  # 累积压缩因子
        "enable_rag": True,            # 启用RAG技术
        "rag_retrieval_limit": 3       # RAG检索限制
    }

    def __init__(self, config_type: str = "default"):
        """
        初始化连贯性管理器

        Args:
            config_type: 配置类型 ("default", "simplified", "enhanced_cumulative")
        """
        if config_type == "simplified":
            self.config = self.SIMPLIFIED_CONFIG.copy()
        elif config_type == "enhanced_cumulative":
            self.config = self.ENHANCED_CUMULATIVE_CONFIG.copy()
        else:
            self.config = self.DEFAULT_CONFIG.copy()

        # 存储章节连贯性数据
        self.chapter_data = {}  # {chapter_number: continuity_data}
        self.chapter_summaries = {}  # {chapter_number: compressed_summary}

        # 新增：累积上下文存储
        self.cumulative_context = {}  # {chapter_number: cumulative_summary}

        # 新增：RAG知识库
        self.rag_knowledge_base = []  # 存储关键写作知识

        logger.info(f"章节连贯性管理器初始化完成，配置类型: {config_type}")

        # 如果启用RAG，初始化知识库
        if self.config.get("enable_rag", False):
            self._initialize_rag_knowledge_base()

    def add_chapter_data(self, chapter_number: int, content: str,
                        continuity_data: Dict = None) -> None:
        """
        添加章节数据

        Args:
            chapter_number: 章节编号
            content: 章节内容
            continuity_data: 连贯性数据（可选）
        """
        try:
            # 如果没有提供连贯性数据，则提取
            if continuity_data is None:
                continuity_data = self._extract_continuity_data(content, chapter_number)

            # 存储完整的连贯性数据
            self.chapter_data[chapter_number] = continuity_data

            # 生成并存储压缩摘要
            summary = self._generate_compressed_summary(content, continuity_data)
            self.chapter_summaries[chapter_number] = summary

            # 新增：如果启用累积上下文，生成累积摘要
            if self.config.get("enable_cumulative_context", False):
                cumulative_summary = self._generate_cumulative_context(chapter_number, content, continuity_data)
                self.cumulative_context[chapter_number] = cumulative_summary

            # 新增：如果启用RAG，更新知识库
            if self.config.get("enable_rag", False):
                self._update_rag_knowledge_base(chapter_number, content, continuity_data)

            # 执行清理策略，控制数据量
            self._cleanup_old_data(chapter_number)

            logger.info(f"章节 {chapter_number} 数据添加完成，当前存储章节数: {len(self.chapter_data)}")

        except Exception as e:
            logger.error(f"添加章节 {chapter_number} 数据时出错: {str(e)}")

    def get_writing_context(self, current_chapter_number: int) -> Dict[str, str]:
        """
        获取写作所需的上下文信息

        Args:
            current_chapter_number: 当前章节编号

        Returns:
            写作上下文信息
        """
        try:
            if current_chapter_number <= 1:
                return {
                    "plot_continuity": "",
                    "character_consistency": "",
                    "style_inheritance": "",
                    "emotional_flow": "",
                    "recent_events": ""
                }

            # 获取前序章节信息
            previous_chapters = [i for i in range(1, current_chapter_number)
                               if i in self.chapter_data or i in self.chapter_summaries]

            if not previous_chapters:
                return self._empty_context()

            # 构建分层上下文
            if self.config.get("enable_cumulative_context", False):
                # 使用增强累积上下文
                context = self._build_enhanced_cumulative_context(previous_chapters, current_chapter_number)
            else:
                # 使用标准分层上下文
                context = self._build_layered_context(previous_chapters, current_chapter_number)

            # 新增：如果启用RAG，增强上下文
            if self.config.get("enable_rag", False):
                rag_context = self._get_rag_enhanced_context(current_chapter_number, context)
                context.update(rag_context)

            # 应用长度控制
            context = self._apply_length_control(context)

            logger.info(f"为第 {current_chapter_number} 章生成写作上下文，"
                       f"剧情连贯性: {len(context['plot_continuity'])}字符，"
                       f"人物一致性: {len(context['character_consistency'])}字符，"
                       f"累积上下文: {'启用' if self.config.get('enable_cumulative_context') else '禁用'}，"
                       f"RAG增强: {'启用' if self.config.get('enable_rag') else '禁用'}")

            return context

        except Exception as e:
            logger.error(f"获取第 {current_chapter_number} 章写作上下文时出错: {str(e)}")
            return self._empty_context()

    def _extract_continuity_data(self, content: str, chapter_number: int) -> Dict:
        """提取连贯性数据"""
        # 这里可以调用现有的提取方法
        # 为了简化，这里提供一个基础实现
        return {
            "chapter_number": chapter_number,
            "characters": self._extract_characters(content),
            "key_events": self._extract_key_events(content),
            "emotional_tone": self._extract_emotional_tone(content),
            "chapter_ending": content[-200:] if len(content) > 200 else content,
            "dialogue_style": self._extract_dialogue_style(content)
        }

    def _generate_compressed_summary(self, content: str, continuity_data: Dict) -> str:
        """生成压缩摘要"""
        if not content:
            return ""

        # 提取关键句子
        sentences = re.split(r'[。！？]', content)
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 5]

        # 重要性评分
        important_sentences = []
        keywords = ["决定", "发现", "突然", "终于", "原来", "开始", "结束", "说", "道"]

        for sentence in sentences:
            score = sum(1 for keyword in keywords if keyword in sentence)
            if '"' in sentence:
                score += 2
            if score > 0:
                important_sentences.append((sentence, score))

        # 选择重要句子
        important_sentences.sort(key=lambda x: x[1], reverse=True)
        selected = [s for s, _ in important_sentences[:5]]  # 最多5句

        summary = "。".join(selected)
        max_length = self.config["max_summary_length"]

        if len(summary) > max_length:
            summary = summary[:max_length-3] + "..."

        return summary

    def _cleanup_old_data(self, current_chapter: int) -> None:
        """清理旧数据，控制内存和成本"""
        try:
            # 清理详细数据：只保留最近的章节
            max_recent = self.config["max_recent_chapters"]
            chapters_to_keep = list(range(max(1, current_chapter - max_recent), current_chapter))

            # 移除过旧的详细数据
            chapters_to_remove = [ch for ch in self.chapter_data.keys()
                                if ch not in chapters_to_keep]
            for ch in chapters_to_remove:
                del self.chapter_data[ch]
                logger.debug(f"清理章节 {ch} 的详细数据")

            # 清理摘要数据：保留更多但也有限制
            max_summary = self.config["max_summary_chapters"]
            summary_chapters_to_keep = list(range(max(1, current_chapter - max_summary), current_chapter))

            summary_chapters_to_remove = [ch for ch in self.chapter_summaries.keys()
                                        if ch not in summary_chapters_to_keep]
            for ch in summary_chapters_to_remove:
                del self.chapter_summaries[ch]
                logger.debug(f"清理章节 {ch} 的摘要数据")

            logger.info(f"数据清理完成，保留详细数据: {len(self.chapter_data)}章，"
                       f"保留摘要数据: {len(self.chapter_summaries)}章")

        except Exception as e:
            logger.error(f"清理旧数据时出错: {str(e)}")

    def _build_layered_context(self, previous_chapters: List[int],
                             current_chapter: int) -> Dict[str, str]:
        """构建分层上下文"""
        context = {
            "plot_continuity": "",
            "character_consistency": "",
            "style_inheritance": "",
            "emotional_flow": "",
            "recent_events": ""
        }

        # 最近章节的详细信息
        recent_chapters = [ch for ch in previous_chapters
                          if ch in self.chapter_data][-self.config["max_recent_chapters"]:]

        # 构建剧情连贯性
        plot_parts = []
        for ch in recent_chapters:
            data = self.chapter_data[ch]
            if data.get("key_events"):
                events = "，".join(data["key_events"][:2])  # 最多2个事件
                plot_parts.append(f"第{ch}章：{events}")

        context["plot_continuity"] = "；".join(plot_parts)

        # 构建人物一致性
        all_characters = set()
        for ch in recent_chapters:
            data = self.chapter_data[ch]
            if data.get("characters"):
                all_characters.update(data["characters"][:3])  # 每章最多3个人物

        context["character_consistency"] = f"主要人物：{', '.join(list(all_characters)[:5])}"

        # 构建情感流向
        emotional_flow = []
        for ch in recent_chapters:
            data = self.chapter_data[ch]
            if data.get("emotional_tone"):
                emotional_flow.append(f"第{ch}章：{data['emotional_tone']}")

        context["emotional_flow"] = "；".join(emotional_flow)

        # 最近事件（从摘要中提取）
        recent_summaries = []
        for ch in previous_chapters[-2:]:  # 最近2章
            if ch in self.chapter_summaries:
                recent_summaries.append(f"第{ch}章：{self.chapter_summaries[ch][:100]}")

        context["recent_events"] = "\n".join(recent_summaries)

        return context

    def _apply_length_control(self, context: Dict[str, str]) -> Dict[str, str]:
        """应用长度控制"""
        max_total = self.config["max_total_context_length"]

        # 计算当前总长度
        total_length = sum(len(v) for v in context.values())

        if total_length <= max_total:
            return context

        # 需要压缩，按优先级压缩
        priority_order = ["plot_continuity", "character_consistency",
                         "recent_events", "emotional_flow", "style_inheritance"]

        # 为每个字段分配长度限制
        field_limits = {
            "plot_continuity": max_total * 0.4,      # 40%
            "character_consistency": max_total * 0.2, # 20%
            "recent_events": max_total * 0.25,       # 25%
            "emotional_flow": max_total * 0.1,       # 10%
            "style_inheritance": max_total * 0.05    # 5%
        }

        for field in priority_order:
            if field in context:
                limit = int(field_limits[field])
                if len(context[field]) > limit:
                    context[field] = context[field][:limit-3] + "..."

        return context

    def _empty_context(self) -> Dict[str, str]:
        """返回空的上下文"""
        return {
            "plot_continuity": "",
            "character_consistency": "",
            "style_inheritance": "",
            "emotional_flow": "",
            "recent_events": ""
        }

    # 辅助方法
    def _extract_characters(self, content: str) -> List[str]:
        """提取人物名称"""
        patterns = [
            r'[\u4e00-\u9fa5]{2,4}(?=说|道|想|看|听|感到|觉得|认为|发现|意识到)',
            r'(?<=")[\u4e00-\u9fa5]{2,4}(?=")',
            r'[\u4e00-\u9fa5]{2,4}(?=的|地|得)',  # 修饰语前的人名
            r'(?<=叫|是|名叫)[\u4e00-\u9fa5]{2,4}',  # 介绍性语句中的人名
        ]
        characters = []
        for pattern in patterns:
            characters.extend(re.findall(pattern, content))

        # 去重并过滤常见词汇
        filtered_characters = []
        common_words = {'这个', '那个', '什么', '怎么', '为什么', '因为', '所以', '但是', '然后', '现在', '刚才', '马上', '立刻', '突然', '慢慢', '轻轻', '静静'}

        for char in set(characters):
            if len(char) >= 2 and char not in common_words:
                filtered_characters.append(char)

        return filtered_characters[:5]  # 最多5个

    def check_character_consistency(self, current_chapter_number: int, new_content: str) -> Dict[str, Any]:
        """
        检查人物名称一致性

        Args:
            current_chapter_number: 当前章节号
            new_content: 新章节内容

        Returns:
            一致性检查结果
        """
        try:
            # 提取新章节中的人物名称
            new_characters = self._extract_characters(new_content)

            # 获取之前章节的人物名称
            previous_characters = set()
            for ch in range(1, current_chapter_number):
                if ch in self.chapter_data:
                    previous_characters.update(self.chapter_data[ch].get("characters", []))

            # 检查一致性
            consistency_issues = []
            name_variations = {}

            # 检查是否有相似但不完全相同的名字（可能是一致性问题）
            for new_char in new_characters:
                for prev_char in previous_characters:
                    if new_char != prev_char:
                        # 检查是否是同一个人的不同称呼
                        if self._is_likely_same_character(new_char, prev_char):
                            if prev_char not in name_variations:
                                name_variations[prev_char] = []
                            name_variations[prev_char].append(new_char)

            # 生成一致性报告
            if name_variations:
                for original_name, variations in name_variations.items():
                    consistency_issues.append(f"人物 '{original_name}' 在新章节中出现了变体: {', '.join(variations)}")

            return {
                "is_consistent": len(consistency_issues) == 0,
                "issues": consistency_issues,
                "new_characters": new_characters,
                "previous_characters": list(previous_characters),
                "name_variations": name_variations
            }

        except Exception as e:
            logger.error(f"检查人物名称一致性时出错: {str(e)}")
            return {
                "is_consistent": True,  # 出错时默认认为一致
                "issues": [],
                "new_characters": [],
                "previous_characters": [],
                "name_variations": {}
            }

    def _is_likely_same_character(self, name1: str, name2: str) -> bool:
        """
        判断两个名字是否可能是同一个人物

        Args:
            name1: 名字1
            name2: 名字2

        Returns:
            是否可能是同一人物
        """
        # 如果有一个名字包含另一个名字，可能是同一人物
        if name1 in name2 or name2 in name1:
            return True

        # 如果名字有共同字符且长度相近，可能是同一人物
        common_chars = set(name1) & set(name2)
        if len(common_chars) >= 1 and abs(len(name1) - len(name2)) <= 1:
            return True

        return False

    def _extract_key_events(self, content: str) -> List[str]:
        """提取关键事件"""
        keywords = ["发生", "出现", "决定", "发现", "遇到", "开始", "结束"]
        sentences = re.split(r'[。！？]', content)
        events = []
        for sentence in sentences:
            if any(keyword in sentence for keyword in keywords) and len(sentence.strip()) > 10:
                events.append(sentence.strip())
                if len(events) >= 3:
                    break
        return events

    def _extract_emotional_tone(self, content: str) -> str:
        """提取情感基调"""
        emotions = {
            "紧张": ["紧张", "焦虑", "担心"],
            "轻松": ["轻松", "愉快", "开心"],
            "严肃": ["严肃", "认真", "庄重"],
            "幽默": ["幽默", "搞笑", "有趣"]
        }
        for emotion, keywords in emotions.items():
            if any(keyword in content for keyword in keywords):
                return emotion
        return "平静"

    def _extract_dialogue_style(self, content: str) -> Dict:
        """提取对话风格"""
        dialogues = re.findall(r'"([^"]+)"', content)
        if not dialogues:
            return {"style": "少对话", "average_length": 0}

        avg_length = sum(len(d) for d in dialogues) / len(dialogues)
        style = "简洁" if avg_length < 20 else "详细"
        return {"style": style, "average_length": avg_length}

    def get_cost_statistics(self) -> Dict[str, Any]:
        """获取成本统计信息"""
        total_detailed_chars = sum(
            len(str(data)) for data in self.chapter_data.values()
        )
        total_summary_chars = sum(
            len(summary) for summary in self.chapter_summaries.values()
        )

        # 计算累积上下文字符数
        total_cumulative_chars = sum(
            len(summary) for summary in self.cumulative_context.values()
        )

        # 计算RAG知识库字符数
        total_rag_chars = sum(
            len(str(item)) for item in self.rag_knowledge_base
        )

        config_type = "enhanced_cumulative" if self.config == self.ENHANCED_CUMULATIVE_CONFIG else \
                     "simplified" if self.config == self.SIMPLIFIED_CONFIG else "default"

        return {
            "detailed_chapters": len(self.chapter_data),
            "summary_chapters": len(self.chapter_summaries),
            "cumulative_chapters": len(self.cumulative_context),
            "rag_knowledge_items": len(self.rag_knowledge_base),
            "total_detailed_chars": total_detailed_chars,
            "total_summary_chars": total_summary_chars,
            "total_cumulative_chars": total_cumulative_chars,
            "total_rag_chars": total_rag_chars,
            "total_chars": total_detailed_chars + total_summary_chars + total_cumulative_chars + total_rag_chars,
            "config_type": config_type,
            "cumulative_enabled": self.config.get("enable_cumulative_context", False),
            "rag_enabled": self.config.get("enable_rag", False)
        }

    # ==================== 新增方法：增强累积上下文 ====================

    def _generate_cumulative_context(self, chapter_number: int, content: str, continuity_data: Dict) -> str:
        """
        生成累积上下文摘要

        实现您要求的累积能力：
        - 第2章: 第1章内容
        - 第3章: 第1+2章内容
        - 第4章: 第1+2+3章内容

        但通过智能压缩控制成本
        """
        try:
            if chapter_number == 1:
                # 第1章没有前序内容
                return self._generate_compressed_summary(content, continuity_data)

            # 获取所有前序章节的累积摘要
            previous_cumulative = []
            for prev_ch in range(1, chapter_number):
                if prev_ch in self.cumulative_context:
                    previous_cumulative.append(f"第{prev_ch}章: {self.cumulative_context[prev_ch]}")

            # 当前章节的摘要
            current_summary = self._generate_compressed_summary(content, continuity_data)

            # 合并所有摘要
            all_summaries = previous_cumulative + [f"第{chapter_number}章: {current_summary}"]
            combined_summary = " | ".join(all_summaries)

            # 应用累积压缩因子，控制总长度
            compression_factor = self.config.get("cumulative_compression_factor", 0.8)
            max_length = int(self.config["max_total_context_length"] * compression_factor)

            if len(combined_summary) > max_length:
                # 智能压缩：保留最重要的信息
                compressed_summary = self._compress_cumulative_summary(combined_summary, max_length)
                logger.info(f"第{chapter_number}章累积摘要压缩: {len(combined_summary)} → {len(compressed_summary)}字符")
                return compressed_summary

            return combined_summary

        except Exception as e:
            logger.error(f"生成第{chapter_number}章累积上下文时出错: {str(e)}")
            return self._generate_compressed_summary(content, continuity_data)

    def _compress_cumulative_summary(self, summary: str, max_length: int) -> str:
        """智能压缩累积摘要"""
        if len(summary) <= max_length:
            return summary

        # 分割各章节摘要
        chapter_summaries = summary.split(" | ")

        # 优先保留最近的章节，压缩较早的章节
        compressed_parts = []
        current_length = 0

        # 从最新章节开始，逆向处理
        for i in range(len(chapter_summaries) - 1, -1, -1):
            chapter_summary = chapter_summaries[i]

            if current_length + len(chapter_summary) <= max_length:
                compressed_parts.insert(0, chapter_summary)
                current_length += len(chapter_summary)
            else:
                # 压缩这个章节摘要
                remaining_space = max_length - current_length - 20  # 留20字符余量
                if remaining_space > 50:  # 至少50字符才有意义
                    compressed_chapter = chapter_summary[:remaining_space] + "..."
                    compressed_parts.insert(0, compressed_chapter)
                break

        return " | ".join(compressed_parts)

    def _build_enhanced_cumulative_context(self, previous_chapters: List[int],
                                         current_chapter: int) -> Dict[str, str]:
        """构建增强累积上下文"""
        context = {
            "plot_continuity": "",
            "character_consistency": "",
            "style_inheritance": "",
            "emotional_flow": "",
            "recent_events": "",
            "cumulative_summary": ""  # 新增累积摘要字段
        }

        try:
            # 获取累积摘要
            if current_chapter > 1 and (current_chapter - 1) in self.cumulative_context:
                context["cumulative_summary"] = self.cumulative_context[current_chapter - 1]

            # 构建其他上下文信息（复用原有逻辑）
            base_context = self._build_layered_context(previous_chapters, current_chapter)
            context.update(base_context)

            # 增强剧情连贯性：结合累积信息
            if context["cumulative_summary"]:
                enhanced_plot = f"累积剧情: {context['cumulative_summary'][:500]}"
                if context["plot_continuity"]:
                    context["plot_continuity"] = f"{enhanced_plot} | 最近剧情: {context['plot_continuity']}"
                else:
                    context["plot_continuity"] = enhanced_plot

            return context

        except Exception as e:
            logger.error(f"构建增强累积上下文时出错: {str(e)}")
            return self._build_layered_context(previous_chapters, current_chapter)

    # ==================== 新增方法：RAG检索增强生成 ====================

    def _initialize_rag_knowledge_base(self):
        """初始化RAG知识库"""
        try:
            # 初始化基础写作知识
            self.rag_knowledge_base = [
                {
                    "type": "writing_technique",
                    "content": "当代网文特征：短句为主，对话丰富，节奏明快，避免过度修辞",
                    "keywords": ["短句", "对话", "节奏", "当代网文"],
                    "relevance_score": 1.0
                },
                {
                    "type": "plot_structure",
                    "content": "章节连贯性要点：情节承接、人物一致、情感延续、风格统一",
                    "keywords": ["连贯性", "情节", "人物", "情感", "风格"],
                    "relevance_score": 1.0
                },
                {
                    "type": "character_development",
                    "content": "人物塑造原则：性格一致、行为逻辑、对话风格、成长轨迹",
                    "keywords": ["人物", "性格", "逻辑", "对话", "成长"],
                    "relevance_score": 1.0
                }
            ]

            logger.info(f"RAG知识库初始化完成，包含 {len(self.rag_knowledge_base)} 个知识条目")

        except Exception as e:
            logger.error(f"初始化RAG知识库时出错: {str(e)}")
            self.rag_knowledge_base = []

    def _update_rag_knowledge_base(self, chapter_number: int, content: str, continuity_data: Dict):
        """更新RAG知识库"""
        try:
            # 从章节内容中提取关键知识
            knowledge_items = self._extract_writing_knowledge(chapter_number, content, continuity_data)

            for item in knowledge_items:
                # 检查是否已存在类似知识
                if not self._is_duplicate_knowledge(item):
                    self.rag_knowledge_base.append(item)

            # 控制知识库大小
            max_knowledge_items = 50  # 最多保留50个知识条目
            if len(self.rag_knowledge_base) > max_knowledge_items:
                # 按相关性分数排序，保留最重要的
                self.rag_knowledge_base.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
                self.rag_knowledge_base = self.rag_knowledge_base[:max_knowledge_items]

            logger.debug(f"第{chapter_number}章更新RAG知识库，当前条目数: {len(self.rag_knowledge_base)}")

        except Exception as e:
            logger.error(f"更新RAG知识库时出错: {str(e)}")

    def _extract_writing_knowledge(self, chapter_number: int, content: str, continuity_data: Dict) -> List[Dict]:
        """从章节内容中提取写作知识"""
        knowledge_items = []

        try:
            # 提取对话风格知识
            if continuity_data.get("dialogue_style"):
                dialogue_knowledge = {
                    "type": "dialogue_style",
                    "content": f"第{chapter_number}章对话特点: {continuity_data['dialogue_style']}",
                    "keywords": ["对话", "风格", f"第{chapter_number}章"],
                    "relevance_score": 0.8,
                    "chapter_source": chapter_number
                }
                knowledge_items.append(dialogue_knowledge)

            # 提取情感基调知识
            if continuity_data.get("emotional_tone"):
                emotion_knowledge = {
                    "type": "emotional_tone",
                    "content": f"第{chapter_number}章情感基调: {continuity_data['emotional_tone']}",
                    "keywords": ["情感", "基调", continuity_data["emotional_tone"]],
                    "relevance_score": 0.7,
                    "chapter_source": chapter_number
                }
                knowledge_items.append(emotion_knowledge)

            # 提取关键事件知识
            if continuity_data.get("key_events"):
                for event in continuity_data["key_events"][:2]:  # 最多2个事件
                    event_knowledge = {
                        "type": "plot_event",
                        "content": f"第{chapter_number}章关键事件: {event}",
                        "keywords": ["事件", "剧情", f"第{chapter_number}章"],
                        "relevance_score": 0.9,
                        "chapter_source": chapter_number
                    }
                    knowledge_items.append(event_knowledge)

            return knowledge_items

        except Exception as e:
            logger.error(f"提取第{chapter_number}章写作知识时出错: {str(e)}")
            return []

    def _is_duplicate_knowledge(self, new_item: Dict) -> bool:
        """检查是否为重复知识"""
        try:
            new_content = new_item.get("content", "")
            new_keywords = set(new_item.get("keywords", []))

            for existing_item in self.rag_knowledge_base:
                existing_content = existing_item.get("content", "")
                existing_keywords = set(existing_item.get("keywords", []))

                # 内容相似度检查
                if len(new_content) > 0 and len(existing_content) > 0:
                    similarity = len(set(new_content.split()) & set(existing_content.split())) / \
                               max(len(set(new_content.split())), len(set(existing_content.split())))
                    if similarity > 0.7:  # 70%相似度认为重复
                        return True

                # 关键词重叠检查
                if len(new_keywords & existing_keywords) >= 2:  # 至少2个关键词重叠
                    return True

            return False

        except Exception as e:
            logger.error(f"检查重复知识时出错: {str(e)}")
            return False

    def _get_rag_enhanced_context(self, current_chapter: int, base_context: Dict[str, str]) -> Dict[str, str]:
        """获取RAG增强的上下文"""
        try:
            # 从基础上下文中提取关键词
            context_keywords = self._extract_context_keywords(base_context)

            # 检索相关知识
            relevant_knowledge = self._retrieve_relevant_knowledge(context_keywords, current_chapter)

            # 构建RAG增强上下文
            rag_context = {
                "rag_writing_guidance": "",
                "rag_style_reference": "",
                "rag_plot_insights": ""
            }

            # 分类整理检索到的知识
            writing_guidance = []
            style_reference = []
            plot_insights = []

            for knowledge in relevant_knowledge:
                knowledge_type = knowledge.get("type", "")
                content = knowledge.get("content", "")

                if knowledge_type in ["writing_technique", "dialogue_style"]:
                    style_reference.append(content)
                elif knowledge_type in ["plot_structure", "plot_event"]:
                    plot_insights.append(content)
                else:
                    writing_guidance.append(content)

            # 构建RAG上下文字段
            if writing_guidance:
                rag_context["rag_writing_guidance"] = " | ".join(writing_guidance[:2])  # 最多2条

            if style_reference:
                rag_context["rag_style_reference"] = " | ".join(style_reference[:2])  # 最多2条

            if plot_insights:
                rag_context["rag_plot_insights"] = " | ".join(plot_insights[:2])  # 最多2条

            logger.debug(f"第{current_chapter}章RAG增强: 检索到{len(relevant_knowledge)}条相关知识")

            return rag_context

        except Exception as e:
            logger.error(f"获取RAG增强上下文时出错: {str(e)}")
            return {}

    def _extract_context_keywords(self, context: Dict[str, str]) -> List[str]:
        """从上下文中提取关键词"""
        keywords = []

        try:
            # 从各个上下文字段中提取关键词
            for field_name, field_content in context.items():
                if field_content and isinstance(field_content, str):
                    # 简单的关键词提取（可以后续优化为更复杂的NLP方法）
                    words = re.findall(r'[\u4e00-\u9fa5]{2,4}', field_content)  # 提取2-4字的中文词
                    keywords.extend(words[:3])  # 每个字段最多3个关键词

            # 去重并限制数量
            unique_keywords = list(set(keywords))[:10]  # 最多10个关键词

            return unique_keywords

        except Exception as e:
            logger.error(f"提取上下文关键词时出错: {str(e)}")
            return []

    def _retrieve_relevant_knowledge(self, keywords: List[str], current_chapter: int) -> List[Dict]:
        """检索相关知识"""
        try:
            scored_knowledge = []

            for knowledge in self.rag_knowledge_base:
                score = self._calculate_relevance_score(knowledge, keywords, current_chapter)
                if score > 0.3:  # 相关性阈值
                    scored_knowledge.append((knowledge, score))

            # 按相关性分数排序
            scored_knowledge.sort(key=lambda x: x[1], reverse=True)

            # 返回最相关的知识（限制数量）
            limit = self.config.get("rag_retrieval_limit", 3)
            return [knowledge for knowledge, score in scored_knowledge[:limit]]

        except Exception as e:
            logger.error(f"检索相关知识时出错: {str(e)}")
            return []

    def _calculate_relevance_score(self, knowledge: Dict, keywords: List[str], current_chapter: int) -> float:
        """计算知识的相关性分数"""
        try:
            base_score = knowledge.get("relevance_score", 0.5)

            # 关键词匹配分数
            knowledge_keywords = knowledge.get("keywords", [])
            keyword_matches = len(set(keywords) & set(knowledge_keywords))
            keyword_score = keyword_matches / max(len(keywords), 1) * 0.5

            # 章节距离分数（越近的章节越相关）
            chapter_source = knowledge.get("chapter_source", 0)
            if chapter_source > 0:
                chapter_distance = abs(current_chapter - chapter_source)
                chapter_score = max(0, 1 - chapter_distance / 10) * 0.3  # 10章内的知识更相关
            else:
                chapter_score = 0.2  # 基础知识的默认分数

            total_score = base_score + keyword_score + chapter_score

            return min(total_score, 1.0)  # 最大分数为1.0

        except Exception as e:
            logger.error(f"计算相关性分数时出错: {str(e)}")
            return 0.0
