{% extends "v3/base.html" %}

{% block title %}预设模板详情 - 九猫{% endblock %}

{% block styles %}
<style>
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        font-weight: 500;
        padding: 0.75rem 1rem;
    }

    .nav-tabs .nav-link.active {
        color: #4a6bff;
        border-bottom: 3px solid #4a6bff;
        background-color: transparent;
    }

    .dimension-item {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .dimension-item:hover {
        background-color: #f8f9fa;
    }

    .dimension-item.active {
        background-color: #e9ecef;
        font-weight: bold;
    }

    .accordion-button {
        padding: 0.75rem 1rem;
        font-weight: 500;
    }

    .accordion-button:not(.collapsed) {
        background-color: #e9ecef;
        color: #4a6bff;
    }

    .accordion-body {
        padding: 0;
    }

    .chapter-dimension-item {
        cursor: pointer;
        padding: 0.5rem 1rem 0.5rem 2rem;
        transition: all 0.2s ease;
        border-left: 0;
        border-right: 0;
        border-radius: 0;
    }

    .chapter-dimension-item:hover {
        background-color: #f8f9fa;
    }

    .chapter-dimension-item.active {
        background-color: #e9ecef;
        font-weight: bold;
        color: #4a6bff;
        border-left: 3px solid #4a6bff;
    }

    .template-content {
        white-space: pre-wrap;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
        min-height: 400px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .markdown-body h1 {
        font-size: 1.8rem;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .markdown-body h2 {
        font-size: 1.5rem;
        margin-top: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .markdown-body h3 {
        font-size: 1.3rem;
        margin-top: 1rem;
        margin-bottom: 0.6rem;
    }

    .markdown-body h4, .markdown-body h5, .markdown-body h6 {
        font-size: 1.1rem;
        margin-top: 0.8rem;
        margin-bottom: 0.5rem;
    }

    .markdown-body p {
        margin-bottom: 0.8rem;
    }

    .markdown-body ul, .markdown-body ol {
        margin-bottom: 1rem;
        padding-left: 2rem;
    }

    .markdown-body blockquote {
        border-left: 4px solid #ced4da;
        padding-left: 1rem;
        margin-left: 0;
        color: #6c757d;
    }

    .markdown-body code {
        background-color: #f1f3f5;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Courier New', Courier, monospace;
    }

    .markdown-body pre {
        background-color: #f1f3f5;
        padding: 1rem;
        border-radius: 5px;
        overflow-x: auto;
        margin-bottom: 1rem;
    }

    .markdown-body pre code {
        background-color: transparent;
        padding: 0;
    }

    .markdown-body table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
    }

    .markdown-body th, .markdown-body td {
        border: 1px solid #dee2e6;
        padding: 0.5rem;
    }

    .markdown-body th {
        background-color: #f8f9fa;
    }

    .progress-badge {
        font-size: 0.8rem;
        padding: 0.2rem 0.5rem;
        margin-left: 0.5rem;
        vertical-align: middle;
    }

    .dimension-progress {
        display: inline-block;
        width: 50px;
        height: 6px;
        background-color: #e9ecef;
        border-radius: 3px;
        margin-left: 0.5rem;
        vertical-align: middle;
    }

    .dimension-progress-bar {
        height: 100%;
        background-color: #4a6bff;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="mb-0">{{ preset.title }} - 预设模板</h1>
                    <div>
                        <a href="{{ url_for('v3.view_preset', preset_id=preset.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回预设详情
                        </a>
                        <a href="{{ url_for('v3.console') }}" class="btn btn-outline-primary ms-2">
                            <i class="fas fa-terminal me-1"></i>控制台
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="lead">预设模板是基于参考蓝本的分析结果生成的，包含整本书的所有预设维度模板和章节列表预设模板。</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提示：</strong> 请从左侧选择要查看的维度或章节，右侧将显示对应的预设模板内容。您可以将这些预设模板应用到您的写作中。
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <ul class="nav nav-tabs card-header-tabs" id="templateTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="book-tab" data-bs-toggle="tab" data-bs-target="#book" type="button" role="tab" aria-controls="book" aria-selected="true">
                                <i class="fas fa-book me-1"></i>整本书
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="false">
                                <i class="fas fa-list-ol me-1"></i>章节
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-0">
                    <div class="tab-content" id="templateTabContent">
                        <!-- 整本书标签页 -->
                        <div class="tab-pane fade show active" id="book" role="tabpanel" aria-labelledby="book-tab">
                            <div class="row g-0">
                                <div class="col-md-3 p-3 border-end">
                                    <h5 class="mb-3">维度列表</h5>
                                    <div class="list-group">
                                        {% for dimension in dimensions %}
                                        <a href="javascript:void(0)" class="list-group-item list-group-item-action dimension-item" data-dimension="{{ dimension.key }}">
                                            <i class="{{ dimension.icon }} me-2"></i>{{ dimension.name }}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="col-md-9 p-3">
                                    <div id="bookTemplateContent" class="template-content markdown-body">
                                        <div class="text-center py-5">
                                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                            <h4>请选择维度</h4>
                                            <p class="text-muted">从左侧选择一个维度，查看对应的预设模板内容。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 章节标签页 -->
                        <div class="tab-pane fade" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                            <div class="row g-0">
                                <div class="col-md-3 p-3 border-end">
                                    <h5 class="mb-3">章节列表</h5>
                                    <div class="accordion" id="chaptersAccordion">
                                        {% if chapters and chapters|length > 0 %}
                                        {% for chapter in chapters %}
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="chapter-heading-{{ chapter.id }}">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter-collapse-{{ chapter.id }}" aria-expanded="false" aria-controls="chapter-collapse-{{ chapter.id }}">
                                                        第{{ chapter.chapter_number }}章: {{ chapter.title }}
                                                    </button>
                                                </h2>
                                                <div id="chapter-collapse-{{ chapter.id }}" class="accordion-collapse collapse" aria-labelledby="chapter-heading-{{ chapter.id }}" data-bs-parent="#chaptersAccordion">
                                                    <div class="accordion-body p-0">
                                                        <div class="list-group list-group-flush">
                                                            {% for dimension in dimensions %}
                                                            <a href="javascript:void(0)" class="list-group-item list-group-item-action chapter-dimension-item"
                                                               data-chapter-id="{{ chapter.id }}" data-dimension="{{ dimension.key }}">
                                                                <i class="{{ dimension.icon }} me-2"></i>{{ dimension.name }}
                                                            </a>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        {% else %}
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <strong>未找到章节列表</strong>，请确保：
                                                <ol>
                                                    <li>参考蓝本已正确设置</li>
                                                    <li>参考蓝本包含章节</li>
                                                    <li>预设模板与参考蓝本正确关联</li>
                                                </ol>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-9 p-3">
                                    <div id="chapterTemplateContent" class="template-content markdown-body">
                                        <div class="text-center py-5">
                                            <i class="fas fa-list-ol fa-3x text-muted mb-3"></i>
                                            <h4>请选择章节和维度</h4>
                                            <p class="text-muted">从左侧先选择一个章节，然后选择一个维度，查看对应的预设模板内容。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
    $(document).ready(function() {
        // 定义变量
        var selectedChapterId = null;
        var selectedDimension = null;
        var bookTemplateContent = {};
        var chapterTemplateContent = {};
        var novelId = "{{ novel.id }}";
        var novelTitle = "{{ novel.title }}";
        var templateId = novelId;  // 参考蓝本ID，与小说ID相同

        // 选择整本书维度
        $('.dimension-item').click(function() {
            $('.dimension-item').removeClass('active');
            $(this).addClass('active');

            var dimension = $(this).data('dimension');
            selectedDimension = dimension;

            // 显示加载中
            $('#bookTemplateContent').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中，请稍候...</p></div>');

            // 先尝试使用新的API调用路径
            $.ajax({
                url: "/api/template/" + templateId + "/analysis/" + dimension,
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        // 处理成功响应
                        handleBookTemplateResponse(response, dimension);
                    } else {
                        console.log("新API路径失败，尝试旧路径", response);
                        // 如果新API失败，回退到旧API
                        callOldBookTemplateAPI(dimension);
                    }
                },
                error: function(xhr) {
                    console.log("新API路径请求错误，尝试旧路径", xhr);
                    // 如果新API请求失败，回退到旧API
                    callOldBookTemplateAPI(dimension);
                }
            });
        });

        // 章节维度点击事件
        $('.chapter-dimension-item').click(function() {
            $('.chapter-dimension-item').removeClass('active');
            $(this).addClass('active');

            var chapterId = $(this).data('chapter-id');
            var dimension = $(this).data('dimension');
            selectedChapterId = chapterId;
            
            // 显示加载中
            $('#chapterTemplateContent').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">加载中，请稍候...</p></div>');

            // 先尝试使用新的API调用路径
            $.ajax({
                url: "/api/template/" + templateId + "/chapter/" + chapterId + "/analysis/" + dimension,
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        // 处理成功响应
                        handleChapterTemplateResponse(response, chapterId, dimension);
                    } else {
                        console.log("新API路径失败，尝试旧路径", response);
                        // 如果新API失败，回退到旧API
                        callOldChapterTemplateAPI(chapterId, dimension);
                    }
                },
                error: function(xhr) {
                    console.log("新API路径请求错误，尝试旧路径", xhr);
                    // 如果新API请求失败，回退到旧API
                    callOldChapterTemplateAPI(chapterId, dimension);
                }
            });
        });

        // 旧API调用函数（整本书）
        function callOldBookTemplateAPI(dimension) {
            $.ajax({
                url: "/novel/" + templateId + "/analysis/" + dimension,
                type: "GET",
                success: function(response) {
                    // 处理旧API响应
                    handleBookTemplateResponse(response, dimension);
                },
                error: function(xhr) {
                    console.log("旧API路径也请求失败", xhr);
                    // 如果API都失败了，生成一个基本模板
                    var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
                    var errorResponse = {
                        success: false,
                        analysis: {
                            result: "无法获取分析结果，请检查参考蓝本设置。"
                        }
                    };
                    handleBookTemplateResponse(errorResponse, dimension);
                }
            });
        }

        // 旧API调用函数（章节）
        function callOldChapterTemplateAPI(chapterId, dimension) {
            $.ajax({
                url: "/novel/" + templateId + "/chapter/" + chapterId + "/analysis/" + dimension,
                type: "GET",
                success: function(response) {
                    // 处理旧API响应
                    handleChapterTemplateResponse(response, chapterId, dimension);
                },
                error: function(xhr) {
                    console.log("旧API路径也请求失败", xhr);
                    // 如果API都失败了，生成一个基本模板
                    var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
                    var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();
                    var errorResponse = {
                        success: false,
                        analysis: {
                            result: "无法获取章节分析结果，请检查参考蓝本设置。"
                        }
                    };
                    handleChapterTemplateResponse(errorResponse, chapterId, dimension);
                }
            });
        }

        // 处理整本书模板响应的函数
        function handleBookTemplateResponse(response, dimension) {
            if (response.analysis && response.analysis.result) {
                // 构建更详细的模板内容
                var content = "# " + novelTitle + " - " + dimension + " 预设模板\n\n";
                
                content += "## 基本信息\n";
                content += "- 小说名称：" + novelTitle + "\n";
                content += "- 维度：" + dimension + "\n";
                content += "- 适用字数范围：30-300万字\n";
                content += "- 生成时间：" + new Date().toLocaleString() + "\n\n";
                
                content += "## 维度分析结果\n" + response.analysis.result + "\n\n";
                
                // 根据不同维度添加特定模板
                if (dimension === "language_style") {
                    content += createLanguageStyleTemplate(response);
                } else if (dimension === "rhythm_pacing") {
                    content += createRhythmPacingTemplate(response);
                } else if (dimension === "structure") {
                    content += createStructureTemplate(response);
                } else if (dimension === "sentence_variation") {
                    content += createSentenceVariationTemplate(response);
                } else if (dimension === "paragraph_length") {
                    content += createParagraphLengthTemplate(response);
                } else if (dimension === "perspective_shifts") {
                    content += createPerspectiveShiftsTemplate(response);
                } else if (dimension === "paragraph_flow") {
                    content += createParagraphFlowTemplate(response);
                } else if (dimension === "novel_characteristics") {
                    content += createNovelCharacteristicsTemplate(response);
                } else if (dimension === "world_building") {
                    content += createWorldBuildingTemplate(response);
                } else if (dimension === "character_relationships") {
                    content += createCharacterRelationshipsTemplate(response);
                } else if (dimension === "opening_effectiveness") {
                    content += createOpeningEffectivenessTemplate(response);
                } else if (dimension === "climax_pacing") {
                    content += createClimaxPacingTemplate(response);
                } else if (dimension === "chapter_outline") {
                    content += createChapterOutlineTemplate(response);
                } else if (dimension === "outline_analysis") {
                    content += createOutlineAnalysisTemplate(response);
                } else if (dimension === "popular_tropes") {
                    content += createPopularTropesTemplate(response);
                } else {
                    // 通用模板
                    content += createGenericTemplate(response, dimension);
                }
                
                // 添加应用指南
                content += "## 应用指南\n";
                content += "本预设模板适用于30-300万字的长篇小说创作，请根据您的具体需求和创作风格进行调整。在使用过程中，建议：\n\n";
                content += "1. **灵活应用**：根据您的故事情节和风格需求，有选择地应用模板中的元素\n";
                content += "2. **保持连贯性**：确保各个维度之间的设定保持一致，避免风格冲突\n";
                content += "3. **创新发展**：在模板基础上进行创新，发展自己独特的创作风格\n";
                content += "4. **读者反馈**：关注读者反馈，及时调整写作策略\n";
                content += "5. **长篇规划**：对于30-300万字的长篇作品，合理规划节奏和结构，避免情节拖沓或紧凑不当\n\n";
                
                content += "## 扩展资源\n";
                content += "- 九猫写作系统提供的其他维度模板\n";
                content += "- 《" + novelTitle + "》的章节分析模板\n";
                content += "- 同类型成功作品的分析案例\n\n";
                
                content += "---\n";
                content += "© " + new Date().getFullYear() + " 九猫写作系统 - 预设模板由AI辅助生成\n";
                
                bookTemplateContent[dimension] = content;
                
                // 使用marked.js渲染Markdown内容
                var renderedContent = marked.parse(content);

                // 添加导出按钮和标题
                var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
                var headerHtml = 
                    '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                        '<h3>' + dimensionName + ' 预设模板</h3>' +
                        '<button class="btn btn-sm btn-outline-primary export-template-btn" data-template-type="book" data-dimension="' + dimension + '">' +
                            '<i class="fas fa-download me-1"></i>导出模板' +
                        '</button>' +
                    '</div>';

                $('#bookTemplateContent').html(headerHtml + renderedContent);
            } else {
                $('#bookTemplateContent').html('<div class="alert alert-warning">未找到有效的分析结果</div>');
            }
        }

        // 处理章节模板响应的函数
        function handleChapterTemplateResponse(response, chapterId, dimension) {
            if (response.analysis && response.analysis.result) {
                if (!chapterTemplateContent[chapterId]) {
                    chapterTemplateContent[chapterId] = {};
                }
                
                // 构建更详细的章节模板内容
                var content = "# " + novelTitle + " - " + response.chapter.title + " - " + dimension + " 预设模板\n\n";
                
                content += "## 章节基本信息\n";
                content += "- 小说: " + novelTitle + "\n";
                content += "- 章节: " + response.chapter.title + "\n";
                content += "- 章节编号: " + response.chapter.chapter_number + "\n";
                content += "- 维度: " + dimension + "\n";
                content += "- 适用字数范围: 5000-10000字/章\n\n";
                
                content += "## 章节分析结果\n" + response.analysis.result + "\n\n";
                
                // 添加章节特定维度详细模板
                content += createChapterDetailedTemplate(response, dimension);
                
                content += "## 章节内容结构建议\n";
                content += "### 开篇部分\n";
                content += "- 开场方式：[直接开场/环境描写开场/对话开场/悬念开场]\n";
                content += "- 情境设置：[时间地点人物活动的明确交代]\n";
                content += "- 前情回顾：[与前章节的自然衔接方式]\n";
                content += "- 读者引导：[阅读兴趣的快速调动方法]\n\n";
                
                content += "### 中间发展部分\n";
                content += "- 情节推进：[新情节的引入节奏/线索展开方式]\n";
                content += "- 冲突设置：[冲突类型/展现手法/强度控制]\n";
                content += "- 人物互动：[对话设计/关系显现/性格体现]\n";
                content += "- 场景描绘：[环境与情节的配合技巧]\n\n";
                
                content += "### 结尾部分\n";
                content += "- 章节收束：[圆满式/悬念式/暗示式/开放式]\n";
                content += "- 后续铺垫：[对下一章的自然引导]\n";
                content += "- 情感余韵：[给读者留下的思考空间]\n";
                content += "- 节奏控制：[与整体故事节奏的协调]\n\n";
                
                content += "## 章节写作技巧提示\n";
                content += "1. **场景转换**：本章节中场景转换应注意[平滑过渡/明确分隔/时空标记]\n";
                content += "2. **人物塑造**：重点展现[特定人物特点/关系变化/性格侧面]\n";
                content += "3. **情感渲染**：主要情感基调为[紧张/温馨/悲伤/欢快]，通过[对话/心理/环境]渲染\n";
                content += "4. **信息控制**：本章应揭示的信息量适中，关键信息[逐步展现/巧妙隐藏/明确交代]\n";
                content += "5. **主题呼应**：与整体主题的呼应点在于[特定情节/象征场景/人物成长]\n\n";
                
                content += "## 与整体故事的衔接建议\n";
                content += "- **前序章节关系**：承接前章的[悬念/情感/行动]，保持角色行为的一致性\n";
                content += "- **后续章节铺垫**：为后续章节埋下[伏笔/悬念/转折可能]\n";
                content += "- **主线推进度**：本章节推进主线故事约[5-10%]，关键点在于[特定事件/决定/发现]\n";
                content += "- **支线发展**：可同时发展的支线包括[人物关系/世界背景/次要冲突]\n\n";
                
                content += "## 适用于长篇写作的特别建议\n";
                content += "- 在30-300万字的长篇作品中，本类型章节建议每[5-10]章安排一次\n";
                content += "- 长篇写作中应特别注意本章与[前30章/后30章]情节的呼应与铺垫\n";
                content += "- 角色塑造应考虑长期发展路线，本章体现的性格特点需为后续成长留下空间\n";
                content += "- 世界观/背景设定的展示应与整体规划协调，避免后期难以自洽\n\n";
                
                content += "---\n";
                content += "© " + new Date().getFullYear() + " 九猫写作系统 - 章节预设模板由AI辅助生成\n";
                
                chapterTemplateContent[chapterId][dimension] = content;

                // 使用marked.js渲染Markdown内容
                var renderedContent = marked.parse(content);

                // 获取章节标题和维度名称
                var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
                var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();

                // 添加导出按钮和标题
                var headerHtml = 
                    '<div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">' +
                        '<h3>' + chapterTitle + ' - ' + dimensionName + ' 预设模板</h3>' +
                        '<button class="btn btn-sm btn-outline-primary export-chapter-btn" data-chapter-id="' + chapterId + '" data-dimension="' + dimension + '">' +
                            '<i class="fas fa-download me-1"></i>导出模板' +
                        '</button>' +
                    '</div>';

                $('#chapterTemplateContent').html(headerHtml + renderedContent);
            } else {
                $('#chapterTemplateContent').html('<div class="alert alert-warning">未找到有效的章节分析结果</div>');
            }
        }

        // 导出整本书模板按钮事件
        $(document).on('click', '.export-template-btn', function() {
            var dimension = $(this).data('dimension');
            exportBookTemplate(dimension);
        });

        // 导出章节模板按钮事件
        $(document).on('click', '.export-chapter-btn', function() {
            var chapterId = $(this).data('chapter-id');
            var dimension = $(this).data('dimension');
            exportChapterTemplate(chapterId, dimension);
        });

        // 导出整本书预设模板
        function exportBookTemplate(dimension) {
            var content = bookTemplateContent[dimension];
            var dimensionName = $('.dimension-item[data-dimension="' + dimension + '"]').text().trim();
            var filename = novelTitle + "_" + dimensionName + "_预设模板.md";
            
            // 创建下载链接
            var blob = new Blob([content], { type: 'text/markdown' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 导出章节预设模板
        function exportChapterTemplate(chapterId, dimension) {
            var content = chapterTemplateContent[chapterId][dimension];
            var chapterTitle = $('#chapter-heading-' + chapterId + ' .accordion-button').text().trim();
            var dimensionName = $('.chapter-dimension-item[data-chapter-id="' + chapterId + '"][data-dimension="' + dimension + '"]').text().trim();
            var filename = novelTitle + "_" + chapterTitle + "_" + dimensionName + "_预设模板.md";

            // 创建下载链接
            var blob = new Blob([content], { type: 'text/markdown' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 为各维度创建详细模板的函数
        function createLanguageStyleTemplate(response) {
            var content = "## 语言风格详细模板\n\n";
            
            content += "### 总体语言风格\n";
            content += "- 风格基调：[例如：简洁明快/华丽典雅/质朴自然/锋利犀利]\n";
            content += "- 语言氛围：[例如：轻松幽默/庄重肃穆/神秘悬疑/浪漫温馨]\n";
            content += "- 特色词汇：[专业领域词汇/方言俚语/古典词汇/现代流行语]\n";
            content += "- 句式偏好：[长短句搭配比例/复合句使用频率/排比句运用]\n\n";
            
            content += "### 人物语言塑造\n";
            content += "- 主角语言特点：[用词习惯/说话方式/特殊表达]\n";
            content += "- 配角语言差异：[不同角色的语言区分方式]\n";
            content += "- 对话与内心独白区分：[表达方式的不同处理]\n";
            content += "- 语言与身份的匹配：[不同阶层/职业/地域人物的语言特征]\n\n";
            
            content += "### 场景描写语言\n";
            content += "- 自然环境描写：[景色/天气/季节变化的语言表现]\n";
            content += "- 城市场景描写：[建筑/街道/人群的描绘方式]\n";
            content += "- 室内场景描写：[不同场所的氛围营造]\n";
            content += "- 动态场景语言：[动作/变化/过程的表达技巧]\n\n";
            
            content += "### 情感表达语言\n";
            content += "- 喜悦情绪表达：[词汇选择/句式特点/修辞手法]\n";
            content += "- 悲伤情绪表达：[语调变化/节奏控制/意象运用]\n";
            content += "- 紧张氛围营造：[短句使用/快节奏描写/悬念设置]\n";
            content += "- 抒情段落特点：[意境创造/情感渲染/节奏控制]\n\n";
            
            content += "### 语言技巧运用\n";
            content += "- 常用修辞手法：[比喻/拟人/夸张/对比等的使用频率和效果]\n";
            content += "- 意象系统构建：[反复出现的意象及其象征意义]\n";
            content += "- 节奏感创造：[句长变化/停顿设置/重复技巧]\n";
            content += "- 新颖表达创造：[独特比喻/新词组合/创新表达]\n\n";
            
            content += "### 长篇写作语言策略\n";
            content += "- 语言风格随情节变化的调整方式\n";
            content += "- 长篇作品中保持语言新鲜感的技巧\n";
            content += "- 不同篇章语言风格的统一与变化\n";
            content += "- 长篇叙事中避免语言单调的方法\n\n";
            
            content += "### 实用写作指导\n";
            content += "- 开场段落语言设计：[引人入胜的语言技巧]\n";
            content += "- 高潮段落语言处理：[紧张感/情感高涨的表达方式]\n";
            content += "- 过渡段落语言技巧：[自然流畅的衔接方式]\n";
            content += "- 结尾段落语言艺术：[余韵留存/意蕴深远的表达]\n\n";
            
            return content;
        }

        function createRhythmPacingTemplate(response) {
            var content = "## 节奏控制详细模板\n\n";
            
            content += "### 整体节奏特征分析\n";
            content += "- 节奏类型：[快速紧凑型/舒缓铺陈型/波动变化型/情节驱动型]\n";
            content += "- 加速减速模式：[递进式/波浪式/起伏式/阶梯式]\n";
            content += "- 章节内节奏变化：[开头-中段-结尾的节奏变化规律]\n";
            content += "- 情节与节奏的匹配度：[快节奏场景/慢节奏场景的内容特点]\n\n";
            
            content += "### 节奏控制的结构设计\n";
            content += "- 章节长度分布：[短章节/长章节的分布规律与功能]\n";
            content += "- 段落长短安排：[紧凑段落/舒缓段落的分布规律]\n";
            content += "- 悬念设置位置：[章节开头/中间/结尾的悬念类型]\n";
            content += "- 章节间的连接：[承接式/转折式/并行式/闪回式]\n\n";
            
            content += "### 不同情节类型的节奏处理\n";
            content += "- 动作场景节奏：[打斗/追逐/紧张场景的节奏技巧]\n";
            content += "- 对话场景节奏：[快速对话/深入交流的节奏控制]\n";
            content += "- 情感场景节奏：[内心独白/情感发泄/关系变化的节奏]\n";
            content += "- 描写场景节奏：[环境/人物/氛围描写的节奏特点]\n\n";
            
            content += "### 节奏变化的转换技巧\n";
            content += "- 加速转换手法：[句式简化/段落缩短/冲突集中/对话加快]\n";
            content += "- 减速转换手法：[详细描写/插入回忆/内心活动/环境氛围]\n";
            content += "- 节奏转折点：[突发事件/情感爆发/关键决定/信息揭示]\n";
            content += "- 平滑过渡技巧：[渐变式/缓冲式/预示式/对比式]\n\n";
            
            content += "### 节奏与读者体验设计\n";
            content += "- 注意力管理：[高潮部分/过渡部分/铺垫部分的注意力控制]\n";
            content += "- 情绪引导：[紧张-舒缓-期待的情绪节奏设计]\n";
            content += "- 阅读疲劳控制：[节奏变化与阅读舒适度的关系]\n";
            content += "- 沉浸感创造：[节奏与内容沉浸度的关系]\n\n";
            
            content += "### 长篇作品的节奏规划\n";
            content += "- 整体节奏曲线：[开篇-发展-高潮-结局的节奏设计]\n";
            content += "- 关键节点安排：[重要转折点的节奏处理]\n";
            content += "- 多条故事线节奏协调：[主线/支线的节奏交织]\n";
            content += "- 长篇阅读体验的节奏节制：[避免节奏单调和过度波动]\n\n";
            
            content += "### 节奏控制优化建议\n";
            content += "- 节奏不当识别：[过快/过慢/过于平缓/过于跳跃的问题]\n";
            content += "- 节奏与内容关系优化：[内容重要性与节奏速度的匹配]\n";
            content += "- 节奏多样性增强：[避免公式化的节奏模式]\n";
            content += "- 读者预期管理：[节奏与读者期待的协调]\n\n";
            
            if (response.analysis && response.analysis.reasoning_content) {
                content += "### 专业分析见解\n";
                content += "根据专业分析，本书节奏控制的特征如下：\n";
                var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 500);
                if (response.analysis.reasoning_content.length > 500) {
                    reasoningExcerpt += "...";
                }
                content += reasoningExcerpt + "\n\n";
            }
            
            return content;
        }

        function createStructureTemplate(response) {
            var content = "## 结构分析详细模板\n\n";
            
            content += "### 整体结构框架\n";
            content += "- 基本结构类型：[线性/非线性/环形/多线并行]\n";
            content += "- 主要篇章划分：[引子/序曲/正文/尾声等组成部分]\n";
            content += "- 情节发展阶段：[开端/发展/高潮/结局的分布比例]\n";
            content += "- 时空结构设计：[时间轴处理/空间转换安排]\n\n";
            
            content += "### 章节结构设计\n";
            content += "- 标准章节框架：[结构组成/长度范围/内部分段]\n";
            content += "- 章节类型多样化：[叙事章/对话章/心理章/描写章]\n";
            content += "- 章节连接方式：[承接式/转折式/并行式/闪回式]\n";
            content += "- 章节内部结构：[开头类型/中间发展/结尾处理]\n\n";
            
            content += "### 情节结构组织\n";
            content += "- 主要情节线：[核心冲突/发展脉络/解决方式]\n";
            content += "- 次要情节线：[支线数量/与主线关系/交织方式]\n";
            content += "- 伏笔与照应：[设置位置/实现方式/密度控制]\n";
            content += "- 冲突设计结构：[冲突层次/升级模式/解决步骤]\n\n";
            
            content += "### 长篇结构特殊技巧\n";
            content += "- 多卷/多部结构设计：[卷与卷之间的关系/独立性与连续性]\n";
            content += "- 长篇小说的节奏控制：[起伏变化/平缓过渡/爆发设计]\n";
            content += "- 长篇阅读结构优化：[信息分布/悬念安排/回顾提示]\n";
            content += "- 大型场景的结构安排：[场景内部分段/场景间转换]\n\n";
            
            content += "### 叙事结构技巧\n";
            content += "- 叙事视角选择：[第一人称/第三人称/多视角交替]\n";
            content += "- 时间处理技巧：[顺序/倒叙/插叙/同时性]\n";
            content += "- 空间转换结构：[场景切换/多地同时/空间对比]\n";
            content += "- 信息揭示节奏：[逐步揭示/突然揭示/误导后揭示]\n\n";
            
            content += "### 长篇结构控制方法\n";
            content += "- 故事节点规划：[重大转折点的分布与影响]\n";
            content += "- 人物发展结构：[成长轨迹/关系变化/能力进步]\n";
            content += "- 主题展开结构：[主题引入/深化/变奏/升华]\n";
            content += "- 长篇作品的结构完整性：[首尾呼应/整体一致/局部变化]\n\n";
            
            content += "### 实用结构指导\n";
            content += "- 开篇结构设计：[吸引读者的开场方式]\n";
            content += "- 中段结构维持：[保持阅读兴趣的结构变化]\n";
            content += "- 高潮结构安排：[多重高潮的层次设计]\n";
            content += "- 结局结构设计：[悬念解答/情感满足/主题升华]\n\n";
            
            return content;
        }

        function createGenericTemplate(response, dimension) {
            var content = "## " + dimension + " 详细模板\n\n";
            
            content += "### 基本特征分析\n";
            content += "- 总体特点：[根据分析结果总结的主要特征]\n";
            content += "- 独特之处：[与一般作品的区别所在]\n";
            content += "- 风格倾向：[鲜明的风格标签]\n";
            content += "- 技术水平：[技巧运用的纯熟程度]\n\n";
            
            content += "### 应用策略\n";
            content += "- 创作时的注意事项\n";
            content += "- 技巧运用的关键点\n";
            content += "- 常见问题及解决方案\n";
            content += "- 个性化调整建议\n\n";
            
            content += "### 长篇写作的特殊考量\n";
            content += "- 长篇作品中的持续应用方法\n";
            content += "- 保持一致性的技巧\n";
            content += "- 适度变化的安排\n";
            content += "- 大型作品的整体把控\n\n";
            
            if (response.analysis && response.analysis.reasoning_content) {
                content += "### 专业分析见解\n";
                content += "根据专业分析，该维度的深层特征为：\n";
                var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 500);
                if (response.analysis.reasoning_content.length > 500) {
                    reasoningExcerpt += "...";
                }
                content += reasoningExcerpt + "\n\n";
            }
            
            content += "### 读者体验分析\n";
            content += "- 读者预期反应：[情感共鸣/思考启发/娱乐效果]\n";
            content += "- 潜在问题预警：[可能引起的阅读疲劳/理解困难]\n";
            content += "- 目标读者群定位：[最适合的读者类型]\n";
            content += "- 读者反馈利用策略：[根据反馈调整的方法]\n\n";
            
            content += "### 实用写作建议\n";
            content += "1. 在开始新作品时，明确该维度的基本风格取向\n";
            content += "2. 创建个人检查清单，确保风格一致性\n";
            content += "3. 阶段性回顾并调整，保持作品的整体协调\n";
            content += "4. 参考成功案例，但避免简单模仿\n";
            content += "5. 建立个人风格库，记录有效的表现方式\n\n";
            
            return content;
        }

        // 其他维度模板函数，简化示例中省略，实际需要实现
        function createSentenceVariationTemplate(response) {
            var content = "## 句式变化详细模板\n\n";
            
            content += "### 句式多样性概述\n";
            content += "- 总体句式特点：[简练型/复杂型/平衡型/波动型]\n";
            content += "- 主要句式类型：[陈述句/疑问句/感叹句/祈使句]的比例与分布\n";
            content += "- 句长变化规律：[长句多/短句多/长短交替/以短句为主偶有长句]\n";
            content += "- 独特句式标志：[特殊标点使用/特殊句式结构/重复句型]\n\n";
            
            content += "### 主要句式类型分析\n";
            content += "- 简单句使用：[频率/典型场景/效果分析]\n";
            content += "- 复合句结构：[并列句/转折句/因果句/条件句的使用比例]\n";
            content += "- 特殊句式：[设问句/反问句/省略句/强调句]的应用场景\n";
            content += "- 排比与对偶：[使用频率/典型例子/写作效果]\n\n";
            
            content += "### 不同情境的句式选择\n";
            content += "- 动作场景句式：[简短有力/快速节奏/句式特点]\n";
            content += "- 描写场景句式：[修饰丰富/意象堆叠/细节表现]\n";
            content += "- 对话句式特点：[简练对话/复杂内心独白/语气词使用]\n";
            content += "- 抒情段落句式：[舒缓流畅/意境营造/感情表达]\n\n";
            
            content += "### 句式变化的节奏控制\n";
            content += "- 快节奏句式特点：[短句快速推进/省略结构/直切主题]\n";
            content += "- 慢节奏句式特点：[长句缓慢展开/修饰丰富/细节铺陈]\n";
            content += "- 节奏转换技巧：[由快到慢/由慢到快/起伏变化]\n";
            content += "- 句式与情感的同步：[情感强烈时的句式特点/平静时的句式特点]\n\n";
            
            content += "### 长篇写作中的句式变化策略\n";
            content += "- 不同章节的句式差异：[开篇/高潮/结尾章节的句式特点]\n";
            content += "- 主题变化与句式调整：[不同主题下的句式选择]\n";
            content += "- 长期句式一致性维护：[保持个人风格的同时避免单调]\n";
            content += "- 句式更新与创新点：[定期引入新句式/突破常规句式]\n\n";
            
            content += "### 句式变化的实用技巧\n";
            content += "- 句首变化方法：[主语变换/状语前置/直接动作引入]\n";
            content += "- 句中结构调整：[插入语使用/状语位置调整/修饰语增减]\n";
            content += "- 句尾效果强化：[结尾词语选择/句尾节奏控制/余韵营造]\n";
            content += "- 标点灵活运用：[破折号/省略号/分号等的效果运用]\n\n";
            
            if (response.analysis && response.analysis.reasoning_content) {
                content += "### 专业分析洞见\n";
                content += "根据专业分析，本书句式变化的核心特征如下：\n";
                var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 500);
                if (response.analysis.reasoning_content.length > 500) {
                    reasoningExcerpt += "...";
                }
                content += reasoningExcerpt + "\n\n";
            }
            
            return content;
        }
        
        function createParagraphLengthTemplate(response) {
            var content = "## 段落长度详细模板\n\n";
            
            content += "### 段落长度基本特征\n";
            content += "- 整体段落长度偏好：[短段为主/长段为主/长短结合/有规律变化]\n";
            content += "- 平均段落字数范围：[例如：50-100字/100-200字/200字以上]\n";
            content += "- 段落长度变化规律：[递增式/递减式/波浪式/分块聚集式]\n";
            content += "- 特殊段落处理：[独句段落使用频率/超长段落特点]\n\n";
            
            content += "### 不同功能段落的长度特点\n";
            content += "- 开场段落：[长度特点/使用技巧/效果分析]\n";
            content += "- 对话段落：[单一对话段长度/对话段组织方式]\n";
            content += "- 描写段落：[环境描写/人物描写/动作描写的段落长度]\n";
            content += "- 过渡段落：[场景转换/时间跳跃的段落长度控制]\n";
            content += "- 结尾段落：[章节/小节结尾的段落长度特点]\n\n";
            
            content += "### 段落长度与内容的关系\n";
            content += "- 紧张情节中的段落长度：[一般是否变短/节奏加快]\n";
            content += "- 舒缓场景中的段落长度：[是否倾向于加长/铺陈详细]\n";
            content += "- 人物心理活动段落：[内心独白/思考的段落长度]\n";
            content += "- 关键信息段落：[重要信息/转折点的段落长度特点]\n\n";
            
            content += "### 段落长度的节奏控制\n";
            content += "- 节奏变化模式：[如何通过段落长度变化控制节奏]\n";
            content += "- 注意力引导：[如何利用段落长度引导读者注意力]\n";
            content += "- 阅读流畅度：[如何保证不同长度段落间的过渡自然]\n";
            content += "- 视觉舒适度：[页面排版与段落长度的协调]\n\n";
            
            content += "### 长篇写作中的段落长度策略\n";
            content += "- 章节间段落长度的连贯与变化\n";
            content += "- 长篇作品中段落长度的整体控制\n";
            content += "- 不同情节阶段的段落长度调整\n";
            content += "- 避免段落长度单调的技巧\n\n";
            
            content += "### 段落长度优化建议\n";
            content += "- 检查过长段落的可读性\n";
            content += "- 评估过短段落的连贯性\n";
            content += "- 调整段落长度与内容的匹配度\n";
            content += "- 通过段落长度强化情感和节奏\n\n";
            
            if (response.analysis && response.analysis.reasoning_content) {
                content += "### 专业分析见解\n";
                content += "根据专业分析，本书段落长度的特征如下：\n";
                var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 500);
                if (response.analysis.reasoning_content.length > 500) {
                    reasoningExcerpt += "...";
                }
                content += reasoningExcerpt + "\n\n";
            }
            
            return content;
        }
        
        function createPerspectiveTemplate(response) {
            var content = "## 视角转换详细模板\n\n";
            
            content += "### 整体视角策略概述\n";
            content += "- 主导叙述视角：[第一人称/第三人称限制/第三人称全知/多视角]\n";
            content += "- 视角切换频率：[固定单一/偶尔切换/频繁切换/有规律切换]\n";
            content += "- 视角切换方式：[章节间切换/章节内切换/场景转换切换/标记显式切换]\n";
            content += "- 视角分配比例：[主要角色占比/次要角色占比/场景切换规律]\n\n";
            
            content += "### 主要视角类型分析\n";
            content += "- 第一人称视角特点：[使用场景/情感表达强度/限制与优势]\n";
            content += "- 第三人称限制视角：[跟随角色/信息控制方式/读者体验设计]\n";
            content += "- 第三人称全知视角：[叙述者特点/全景展示技巧/与人物关系]\n";
            content += "- 特殊视角尝试：[非人物视角/物品视角/环境视角/群体视角]\n\n";
            
            content += "### 视角转换的艺术处理\n";
            content += "- 视角转换标记：[明确标记/隐晦过渡/特殊排版/语言风格变化]\n";
            content += "- 视角连贯性处理：[共同事件不同视角/信息重叠与补充/时间线协调]\n";
            content += "- 视角冲突利用：[矛盾视角/不可靠叙述/真相揭示技巧]\n";
            content += "- 视角深度控制：[表层观察/深入心理/全知干预的平衡]\n\n";
            
            content += "### 不同角色视角的表现方式\n";
            content += "- 主角视角特点：[语言风格/观察重点/思维方式/价值取向]\n";
            content += "- 反派视角处理：[动机展示/心理描写/同理心设计/信息揭示]\n";
            content += "- 次要角色视角：[补充信息/对主要情节的侧面反映/关系网络展示]\n";
            content += "- 旁观者视角：[客观性/有限认知/评价性描述的特点]\n\n";
            
            content += "### 视角与情节推动的关系\n";
            content += "- 视角选择与悬念：[信息控制/读者期待/真相揭示时机]\n";
            content += "- 视角与冲突展现：[多方视角展示冲突/偏向性处理/读者判断空间]\n";
            content += "- 关键事件的视角安排：[最有效视角/多视角补充/情感强化]\n";
            content += "- 视角转换与情节节奏：[加速/减速/扩展/聚焦效果]\n\n";
            
            content += "### 视角使用的长篇规划\n";
            content += "- 叙事弧与视角变化：[起承转合中的视角安排]\n";
            content += "- 人物成长与视角调整：[内部视角深化/外部评价变化]\n";
            content += "- 主题表达与视角选择：[哪些主题适合哪种视角]\n";
            content += "- 读者体验设计：[沉浸感/距离感/认同感的控制]\n\n";
            
            content += "### 视角使用优化建议\n";
            content += "- 视角混乱问题检查与修正\n";
            content += "- 视角深度不足的强化方法\n";
            content += "- 视角特点与角色匹配度评估\n";
            content += "- 视角转换技巧提升建议\n\n";
            
            if (response.analysis && response.analysis.reasoning_content) {
                content += "### 专业分析见解\n";
                content += "根据专业分析，本书视角转换的特征如下：\n";
                var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 500);
                if (response.analysis.reasoning_content.length > 500) {
                    reasoningExcerpt += "...";
                }
                content += reasoningExcerpt + "\n\n";
            }
            
            return content;
        }
        
        function createParagraphFlowTemplate(response) {
            var content = "## 段落流动详细模板\n\n";
            
            content += "### 段落连贯性分析\n";
            content += "- 段落过渡类型：[顺承式/转折式/对比式/因果式/并列式]\n";
            content += "- 过渡词使用：[时间过渡词/空间过渡词/逻辑关系词的使用频率]\n";
            content += "- 段落间连接紧密度：[紧密相连/自然过渡/明显分隔]\n";
            content += "- 段落主题延续性：[主题平滑延续/转换/环形结构]\n\n";
            
            content += "### 段落内部结构分析\n";
            content += "- 段落组织模式：[总分结构/分总结构/并列结构/对比结构]\n";
            content += "- 段落焦点位置：[开头集中/结尾集中/中间展开/平均分布]\n";
            content += "- 句子连接方式：[衔接词/代词/重复关键词/平行结构]\n";
            content += "- 段落内逻辑流动：[时间顺序/空间顺序/因果顺序/重要性顺序]\n\n";
            
            content += "### 段落功能多样性\n";
            content += "- 叙事段落特点：[事件推进/情节发展/时间流动的处理]\n";
            content += "- 描写段落特点：[细节呈现/氛围营造/感官描述的层次]\n";
            content += "- 对话段落特点：[对话与叙述的比例/对话标签使用/内外表现]\n";
            content += "- 心理活动段落：[内心独白/思考过程/情感变化的表达]\n\n";
            
            content += "### 段落节奏与变化\n";
            content += "- 段落长度变化规律：[长短交替/渐变/突变/模式重复]\n";
            content += "- 信息密度控制：[信息集中段落/信息舒缓段落的分布]\n";
            content += "- 段落焦点转换：[人物间/场景间/时间间的焦点平滑转移]\n";
            content += "- 段落情绪流动：[情绪铺垫/转变/爆发/余波的段落安排]\n\n";
            
            content += "### 段落与整体结构的关系\n";
            content += "- 开场段落技巧：[引入方式/注意力抓取/基调奠定]\n";
            content += "- 过渡段落设计：[场景转换/时间跳跃/视角切换的衔接]\n";
            content += "- 高潮段落处理：[情节强化/节奏加快/细节突出]\n";
            content += "- 结尾段落效果：[情感收束/启示点明/开放性设计]\n\n";
            
            content += "### 段落流动的艺术效果\n";
            content += "- 节奏感营造：[段落长短变化创造的阅读节奏]\n";
            content += "- 情感引导：[段落特点与情感基调的匹配]\n";
            content += "- 焦点突出：[重点信息的段落位置安排]\n";
            content += "- 读者体验设计：[阅读流畅度/沉浸感/参与感的创造]\n\n";
            
            content += "### 段落流动优化建议\n";
            content += "- 段落衔接问题修正：[生硬转换/逻辑断层/主题跳跃的处理]\n";
            content += "- 段落内部优化：[冗余信息/结构混乱/重点不明的调整]\n";
            content += "- 段落多样性增强：[避免结构单一/增加段落层次变化]\n";
            content += "- 长段落可读性提升：[分解过长段落/增加内部结构标记]\n\n";
            
            if (response.analysis && response.analysis.reasoning_content) {
                content += "### 专业分析见解\n";
                content += "根据专业分析，本书段落流动的特征如下：\n";
                var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 500);
                if (response.analysis.reasoning_content.length > 500) {
                    reasoningExcerpt += "...";
                }
                content += reasoningExcerpt + "\n\n";
            }
            
            return content;
        }
        
        function createNovelCharacteristicsTemplate(response) {
            // 实现小说特点模板
            return "## 小说特点详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createWorldBuildingTemplate(response) {
            // 实现世界构建模板
            return "## 世界构建详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createCharacterRelationshipsTemplate(response) {
            // 实现人物关系模板
            return "## 人物关系详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createOpeningEffectivenessTemplate(response) {
            // 实现开篇效果模板
            return "## 开篇效果详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createClimaxPacingTemplate(response) {
            // 实现高潮节奏模板
            return "## 高潮节奏详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createChapterOutlineTemplate(response) {
            // 实现章纲分析模板
            return "## 章纲分析详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createOutlineAnalysisTemplate(response) {
            // 实现大纲分析模板
            return "## 大纲分析详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }
        
        function createPopularTropesTemplate(response) {
            // 实现热梗统计模板
            return "## 热梗统计详细模板\n\n[根据分析结果生成详细内容]\n\n";
        }

        // 为章节创建详细模板的函数
        function createChapterDetailedTemplate(response, dimension) {
            var content = "";
            
            // 根据不同维度提供详细的章节设计模板
            if (dimension === "language_style") {
                content += "## 本章语言风格详细设计\n\n";
                content += "### 总体语言基调\n";
                content += "- 主要语气：[正式/轻松/紧张/舒缓/幽默/严肃]\n";
                content += "- 词汇选择：[专业术语比例/口语化程度/华丽词藻使用频率]\n";
                content += "- 句式特点：[长短句比例/复杂句使用/特殊句式安排]\n";
                content += "- 修辞偏好：[本章重点使用的修辞手法及效果]\n\n";
                
                content += "### 场景描写语言\n";
                content += "- 主要场景：[场景特点及其语言表现方式]\n";
                content += "- 环境氛围：[氛围营造的语言技巧]\n";
                content += "- 感官描写：[视觉/听觉/嗅觉/触觉描写的比重与方式]\n";
                content += "- 细节处理：[细节描写的深度与语言风格]\n";
                content += "- 情感表达：[情感基调与语言风格的关系]\n\n";
                
                content += "### 对话设计\n";
                content += "- 人物话语特点：[主要角色在本章的说话方式]\n";
                content += "- 对话节奏：[简短对话/长篇对话/对话与叙述的比例]\n";
                content += "- 潜台词处理：[言外之意的表达技巧]\n";
                content += "- 口头禅与习惯用语：[体现人物特点的语言习惯]\n\n";
            } 
            else if (dimension === "rhythm_pacing") {
                content += "## 本章节奏节拍详细设计\n\n";
                content += "### 章节节奏曲线\n";
                content += "- 开场节奏：[缓慢铺垫/中速展开/快速进入]\n";
                content += "- 中段变化：[渐进加速/平稳过渡/起伏波动]\n";
                content += "- 高潮设计：[位置/持续长度/强度]\n";
                content += "- 结尾节奏：[戛然而止/缓慢收束/余韵悠长]\n\n";
                
                content += "### 段落节奏控制\n";
                content += "- 段落长度变化：[长段铺陈/短段节奏/长短交替]\n";
                content += "- 场景切换频率：[频繁转换/长时间停留/渐进切换]\n";
                content += "- 信息密度分布：[信息集中段/舒缓段/过渡段]\n";
                content += "- 停顿与转折：[自然过渡/突然转折/意外插入]\n\n";
                
                content += "### 特殊节奏技巧\n";
                content += "- 时间流速控制：[时间压缩/时间延展/时间跳跃]\n";
                content += "- 紧张感营造：[短句增多/节奏加快/细节放大]\n";
                content += "- 悬念设置点：[章节内悬念分布与揭示节奏]\n";
                content += "- 情感起伏控制：[情感波动幅度/频率/层次]\n\n";
            }
            else if (dimension === "structure") {
                content += "## 本章结构详细设计\n\n";
                content += "### 章节内部结构\n";
                content += "- 段落组织：[总段落数/各部分段落比例/关键段落位置]\n";
                content += "- 内部小节划分：[明显分节/暗示分节/平滑过渡]\n";
                content += "- 结构形态：[线性/环形/并行/插叙]\n";
                content += "- 视角安排：[单一视角/多视角/视角转换点]\n\n";
                
                content += "### 情节结构设计\n";
                content += "- 核心事件：[主要事件/次要事件/背景事件的安排]\n";
                content += "- 冲突设置：[主要冲突点/冲突发展阶段/解决方向]\n";
                content += "- 转折点设计：[数量/位置/强度/影响]\n";
                content += "- 伏笔与照应：[本章埋下的伏笔/呼应前文的照应点]\n\n";
                
                content += "### 与整体结构的关系\n";
                content += "- 在总体架构中的位置与功能\n";
                content += "- 与前后章节的结构连接方式\n";
                content += "- 对整体故事线的推动作用\n";
                content += "- 结构创新与变化点\n\n";
            }
            else {
                // 通用章节模板
                content += "## 本章" + dimension + "详细设计\n\n";
                content += "### 章节特点分析\n";
                content += "- 本章在此维度的主要特征\n";
                content += "- 与整书风格的一致性与变化\n";
                content += "- 特色表现方式\n";
                content += "- 读者预期效果\n\n";
                
                content += "### 实施要点\n";
                content += "- 开篇部分的处理方式\n";
                content += "- 中段发展的关键技巧\n";
                content += "- 结尾设计的特别考量\n";
                content += "- 整体协调性保障方法\n\n";
                
                if (response.analysis && response.analysis.reasoning_content) {
                    content += "### 专业分析参考\n";
                    var reasoningExcerpt = response.analysis.reasoning_content.substring(0, 300);
                    if (response.analysis.reasoning_content.length > 300) {
                        reasoningExcerpt += "...";
                    }
                    content += reasoningExcerpt + "\n\n";
                }
            }
            
            return content;
        }

        // 初始化页面
        function initPage() {
            // 添加页面说明
            $('.card-body .lead').after(
                '<div class="alert alert-success mt-3">' +
                    '<i class="fas fa-lightbulb me-2"></i>' +
                    '<strong>提示：</strong> 预设模板可以帮助您更好地理解参考蓝本的写作风格和技巧，为您的创作提供指导。您可以点击"导出模板"按钮将模板保存为Markdown文件，方便离线查看和使用。' +
                '</div>'
            );

            // 添加返回顶部按钮
            $('body').append(
                '<button id="backToTop" class="btn btn-primary rounded-circle position-fixed" style="bottom: 20px; right: 20px; width: 50px; height: 50px; display: none;">' +
                    '<i class="fas fa-arrow-up"></i>' +
                '</button>'
            );

            // 返回顶部按钮事件
            $('#backToTop').click(function() {
                $('html, body').animate({ scrollTop: 0 }, 300);
            });

            // 显示/隐藏返回顶部按钮
            $(window).scroll(function() {
                if ($(this).scrollTop() > 300) {
                    $('#backToTop').fadeIn();
                } else {
                    $('#backToTop').fadeOut();
                }
            });

            // 设置页面标题
            document.title = novelTitle + " - 预设模板 - 九猫";
            
            // 自动加载整本书的所有维度数据
            $.ajax({
                url: "/api/template/" + templateId + "/analysis",
                type: "GET",
                success: function(response) {
                    if (response.success && response.book_analyses) {
                        console.log("成功获取整本书所有维度分析数据");
                        // 如果需要，这里可以缓存数据以便后续使用
                    }
                },
                error: function(xhr) {
                    console.error('获取整本书所有维度分析数据失败:', xhr);
                }
            });
        }

        // 初始化页面
        initPage();
    });
</script>
{% endblock %}
