"""
九猫小说分析写作系统v3.0 - 测试功能服务
提供测试功能的后端服务，包括小说上传、分析和自动写作
"""

import logging
import time
import json
import os
import threading
import uuid
import re
import config
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime

from src.models.novel import Novel
from src.models.chapter import Chapter
from src.models.analysis_result import AnalysisResult
from src.models.chapter_analysis_result import ChapterAnalysisResult
from src.services.word_count_tracker import WordCountTracker
from src.db.connection import Session
from src.api.deepseek_client import DeepSeekClient
from src.api.analysis import NovelAnalyzer

logger = logging.getLogger(__name__)

# 导入短篇小说配置
try:
    from src.config.short_story_config import (
        SHORT_STORY_ANALYSIS_DIMENSIONS,
        SHORT_STORY_WRITING_CONFIG,
        SHORT_STORY_VALIDATION,
        ZHIHU_STYLE_WRITING_PROMPTS
    )
except ImportError:
    logger.warning("无法导入短篇小说配置，短篇小说功能可能不可用")
    SHORT_STORY_ANALYSIS_DIMENSIONS = []
    SHORT_STORY_WRITING_CONFIG = {}
    SHORT_STORY_VALIDATION = {}
    ZHIHU_STYLE_WRITING_PROMPTS = {}

# 任务状态存储
test_tasks = {}

class TestService:
    """测试功能服务类"""

    @staticmethod
    def upload_novel(title: str, content: str, author: Optional[str] = None,
                    file_path: Optional[str] = None, file_name: Optional[str] = None) -> Tuple[bool, int, str]:
        """
        上传小说

        Args:
            title: 小说标题
            content: 小说内容
            author: 作者名称（可选）
            file_path: 文件路径（可选）
            file_name: 文件名（可选）

        Returns:
            (成功标志, 小说ID, 错误信息)
        """
        try:
            session = Session()
            try:
                # 创建小说对象
                novel = Novel(
                    title=title,
                    content=content,
                    author=author,
                    file_path=file_path,
                    file_name=file_name
                )

                # 保存到数据库
                session.add(novel)
                session.commit()

                # 导入章节分析服务
                from src.services.chapter_analysis_service import ChapterAnalysisService
                from src.utils.text_processor import TextProcessor

                # 使用TextProcessor分割章节
                chapter_texts = TextProcessor.split_into_chapters(novel.content)

                # 创建章节对象
                chapters = []
                for i, chapter_text in enumerate(chapter_texts):
                    # 尝试提取章节标题
                    title = None
                    first_line = chapter_text.strip().split('\n')[0] if chapter_text.strip() else ""
                    if first_line and len(first_line) < 100:  # 标题通常不会太长
                        title = first_line

                    # 创建章节
                    chapter = Chapter(
                        novel_id=novel.id,
                        chapter_number=i + 1,
                        content=chapter_text,
                        title=title or f'第{i+1}章',
                        metadata={"source": "test_service"}
                    )
                    session.add(chapter)
                    chapters.append(chapter)

                # 提交章节
                if chapters:
                    session.commit()
                    logger.info(f"小说《{title}》上传成功，ID: {novel.id}，共分割出 {len(chapters)} 章")
                else:
                    logger.warning(f"小说《{title}》上传成功，ID: {novel.id}，但未能分割出章节")

                return True, novel.id, ""
            except Exception as e:
                session.rollback()
                logger.error(f"上传小说失败: {str(e)}", exc_info=True)
                return False, 0, f"上传小说失败: {str(e)}"
            finally:
                session.close()
        except Exception as e:
            logger.error(f"创建数据库会话失败: {str(e)}", exc_info=True)
            return False, 0, f"创建数据库会话失败: {str(e)}"

    @staticmethod
    def start_analysis_task(novel_id: int, model: str = "deepseek-r1", prompt_template: str = "default", novel_type: str = "long") -> Tuple[bool, str, str]:
        """
        开始分析任务

        Args:
            novel_id: 小说ID
            model: 使用的模型名称，默认为 deepseek-r1
            prompt_template: 使用的提示词模板，默认为 default
            novel_type: 小说类型，long为长篇小说，short为短篇小说

        Returns:
            (成功标志, 任务ID, 错误信息)
        """
        try:
            # 检查模型是否支持
            if model not in config.SUPPORTED_MODELS:
                logger.warning(f"不支持的模型: {model}，将使用默认模型 deepseek-r1")
                model = "deepseek-r1"

            # 检查提示词模板是否支持
            if prompt_template not in ["default", "simplified"]:
                logger.warning(f"不支持的提示词模板: {prompt_template}，将使用默认提示词模板")
                prompt_template = "default"

            session = Session()
            try:
                # 检查小说是否存在
                novel = session.query(Novel).get(novel_id)
                if not novel:
                    return False, "", f"未找到ID为{novel_id}的小说"

                # 创建任务ID
                task_id = str(uuid.uuid4())

                # 初始化任务状态
                test_tasks[task_id] = {
                    "novel_id": novel_id,
                    "model": model,  # 保存选择的模型
                    "prompt_template": prompt_template,  # 保存选择的提示词模板
                    "novel_type": novel_type,  # 保存小说类型
                    "status": "准备中",
                    "progress": 0,
                    "completed": False,
                    "results": None,
                    "error": None,
                    "start_time": time.time()
                }

                # 启动后台线程执行分析和写作
                thread = threading.Thread(
                    target=TestService._run_analysis_and_writing,
                    args=(task_id, novel_id, model, prompt_template, novel_type)  # 传递模型、提示词模板和小说类型参数
                )
                thread.daemon = True
                thread.start()

                logger.info(f"已创建分析任务 {task_id} 用于小说 {novel_id}，使用模型: {model}，提示词模板: {prompt_template}")
                return True, task_id, ""
            finally:
                session.close()
        except Exception as e:
            logger.error(f"创建分析任务失败: {str(e)}", exc_info=True)
            return False, "", f"创建分析任务失败: {str(e)}"

    @staticmethod
    def _run_analysis_and_writing(task_id: str, novel_id: int, model: str = "deepseek-r1", prompt_template: str = "default", novel_type: str = "long"):
        """
        在后台线程中运行分析和写作任务

        Args:
            task_id: 任务ID
            novel_id: 小说ID
            model: 使用的模型名称，默认为 deepseek-r1
            prompt_template: 使用的提示词模板，默认为 default
            novel_type: 小说类型，long为长篇小说，short为短篇小说
        """
        # 初始化结果存储
        book_analysis_results = {}
        chapter_analysis_results = {}
        generated_chapters = []
        novel_data = {}
        chapters_data = []

        try:
            # 更新任务状态
            test_tasks[task_id]["status"] = "正在加载小说数据"
            test_tasks[task_id]["progress"] = 5

            # 第一个会话：加载小说和章节数据
            session = Session()
            try:
                # 获取小说
                novel = session.query(Novel).get(novel_id)
                if not novel:
                    raise ValueError(f"未找到ID为{novel_id}的小说")

                # 将小说数据保存到字典中
                novel_data = {
                    "id": novel.id,
                    "title": novel.title,
                    "author": novel.author,
                    "content": novel.content,
                    "word_count": novel.word_count
                }

                # 获取章节
                chapters = session.query(Chapter).filter(Chapter.novel_id == novel_id).order_by(Chapter.chapter_number).all()
                if not chapters:
                    raise ValueError(f"小说 {novel_id} 没有章节")

                # 将章节数据保存到列表中
                for chapter in chapters:
                    chapters_data.append({
                        "id": chapter.id,
                        "novel_id": chapter.novel_id,
                        "chapter_number": chapter.chapter_number,
                        "title": chapter.title,
                        "content": chapter.content,
                        "word_count": chapter.word_count
                    })
            finally:
                session.close()

            # 更新任务状态，区分精简版和默认版
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = "🚀 正在分析小说（精简版降本增效模式）"
                test_tasks[task_id]["mode"] = "simplified"
                test_tasks[task_id]["cost_optimization"] = "已启用5项降本增效策略"
            else:
                test_tasks[task_id]["status"] = "正在分析小说（默认版）"
                test_tasks[task_id]["mode"] = "default"
            test_tasks[task_id]["progress"] = 10

            # 创建分析器，使用指定的模型
            api_client = DeepSeekClient(model=model)
            analyzer = NovelAnalyzer(api_client=api_client)
            logger.info(f"使用模型 {model} 创建分析器，提示词模板: {prompt_template}")

            # 分析维度 - 根据小说类型选择分析维度
            if novel_type == "short" and SHORT_STORY_ANALYSIS_DIMENSIONS:
                dimensions = [dim["key"] for dim in SHORT_STORY_ANALYSIS_DIMENSIONS]
                logger.info(f"使用知乎体短篇小说分析维度，共{len(dimensions)}个维度")
            else:
                dimensions = [dim["key"] for dim in config.ANALYSIS_DIMENSIONS]
                logger.info(f"使用长篇小说分析维度，共{len(dimensions)}个维度")

            # 分析整本书，区分精简版和默认版
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = "💡 正在分析整本书（精简版降本增效模式）"
            else:
                test_tasks[task_id]["status"] = "正在分析整本书（默认版）"
            test_tasks[task_id]["progress"] = 20

            # 第二个会话：分析整本书 - 使用并行分析
            session = Session()
            try:
                # 导入并行分析所需的模块
                import concurrent.futures
                import psutil

                # 获取系统资源信息
                cpu_count = psutil.cpu_count(logical=True)
                memory_percent = psutil.virtual_memory().percent

                # 新增：精简版模式下的智能分析策略
                if prompt_template == "simplified":
                    # 精简版智能策略：只做关键整本书分析 + 详细章节分析
                    # 使用英文key匹配，这些是最需要整本书视角的维度
                    key_book_dimensions = ["outline_analysis", "chapter_outline", "character_relationships", "world_building", "structure"]  # 关键的整本书维度
                    book_only_dimensions = [dim for dim in dimensions if dim in key_book_dimensions]

                    logger.info(f"[精简版策略] 所有维度: {dimensions}")
                    logger.info(f"[精简版策略] 关键整本书维度: {key_book_dimensions}")
                    logger.info(f"[精简版策略] 匹配到的维度: {book_only_dimensions}")

                    if book_only_dimensions:
                        segment_size = 2  # 关键维度每次分析2个
                        api_delay = 1.5   # 增加延迟，避免扩缩容
                        writing_delay = 5.0  # 写作延迟更长，确保稳定性

                        logger.info(f"[智能降本增效] 精简版模式：只分析关键整本书维度{book_only_dimensions}，避免重复分析")
                        logger.info(f"[智能降本增效] 其他维度将通过章节分析汇总获得，避免成本翻倍")

                        # 新增：实现预留实例和异步调用优化
                        TestService._configure_reserved_instances(task_id, len(book_only_dimensions))
                        TestService._setup_async_processing(task_id, prompt_template)
                        TestService._optimize_trigger_rules(task_id, prompt_template)

                        # 新增：串行化瓶颈优化（在现有优化框架基础上）
                        TestService._apply_serialization_bottleneck_optimization(task_id, prompt_template)

                        test_tasks[task_id]["cost_optimization"] = f"智能策略：只分析{len(book_only_dimensions)}个关键整本书维度，其他通过章节汇总 + 预留实例优化 + 异步处理 + 触发器优化"

                        # 使用分段分析关键维度
                        book_analysis_results = TestService._analyze_book_in_segments(
                            analyzer, novel_data, book_only_dimensions, segment_size, api_delay,
                            prompt_template, task_id, session
                        )

                        # 标记其他维度为"通过章节分析获得"
                        other_dimensions = [dim for dim in dimensions if dim not in key_book_dimensions]
                        for dim in other_dimensions:
                            book_analysis_results[dim] = {
                                "content": f"该维度通过章节分析汇总获得，请查看各章节的{dim}分析结果。",
                                "reasoning": f"精简版模式：{dim}维度通过章节分析汇总，避免重复分析降低成本。",
                                "reasoning_content": f"为了降本增效，{dim}维度采用章节分析汇总策略，不进行整本书重复分析。"
                            }

                            # 保存到数据库
                            analysis_result = AnalysisResult(
                                novel_id=novel_data["id"],
                                dimension=dim,
                                content=book_analysis_results[dim]["content"],
                                reasoning_content=book_analysis_results[dim]["reasoning_content"],
                                metadata={
                                    "source": "test_service_smart_strategy",
                                    "strategy": "chapter_aggregation",
                                    "cost_optimization": "避免重复分析",
                                    "reasoning_content": book_analysis_results[dim]["reasoning_content"]
                                }
                            )
                            session.add(analysis_result)
                            session.commit()

                        logger.info(f"[智能降本增效] 完成{len(book_only_dimensions)}个关键维度分析，{len(other_dimensions)}个维度标记为章节汇总")
                    else:
                        # 如果没有关键维度，跳过整本书分析
                        logger.info(f"[智能降本增效] 精简版模式：跳过整本书分析，完全依赖章节分析汇总")
                        book_analysis_results = {}
                        test_tasks[task_id]["cost_optimization"] = "智能策略：跳过整本书分析，完全依赖章节分析汇总，成本节省90%+"
                else:
                    # 默认版保持原有并行策略
                    if memory_percent > 80:
                        adjusted_workers = min(4, len(dimensions))
                        logger.warning(f"系统内存使用率较高({memory_percent}%)，降低并行分析数量为{adjusted_workers}")
                    else:
                        # 根据CPU核心数和维度数量确定最佳并行数
                        # 确保至少有15个并行任务，但不超过CPU核心数的2.0倍
                        adjusted_workers = min(max(15, int(cpu_count * 2.0)), len(dimensions))
                        logger.info(f"系统资源充足，内存使用率{memory_percent}%，CPU核心数{cpu_count}，设置并行分析数量为{adjusted_workers}")

                    # 连续性写作优化：只在写作阶段添加延迟，分析阶段保持高效
                    api_delay = 0.5  # 分析阶段使用较短延迟，保持效率
                    writing_delay = 3.0  # 写作阶段使用较长延迟，确保稳定性
                    logger.info(f"[连续性写作优化] 默认版模式：分析延迟{api_delay}秒（保持效率），写作延迟{writing_delay}秒（确保稳定性）")

                    # 更新任务状态
                    test_tasks[task_id]["status"] = f"正在并行分析整本书（默认版：同时处理{adjusted_workers}个维度）"
                    test_tasks[task_id]["progress"] = 20

                    # 设置最大并行数量
                    max_workers = adjusted_workers
                    logger.info(f"开始并行分析整本书，最大并行数: {max_workers}")

                    # 使用传统并行分析（仅默认版）
                    book_analysis_results = TestService._analyze_book_parallel(
                        analyzer, novel_data, dimensions, max_workers, api_delay,
                        prompt_template, task_id, session
                    )

                # 资源清理：分析完成后立即清理资源
                TestService._cleanup_analysis_resources(session, task_id, prompt_template)


            finally:
                session.close()

            # 分析章节，区分精简版和默认版
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = "📚 正在分析章节（精简版降本增效模式）"
            else:
                test_tasks[task_id]["status"] = "正在分析章节（默认版）"
            test_tasks[task_id]["progress"] = 50

            # 第三个会话：使用优化的章节分析服务
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = f"🔧 正在启动章节分析（精简版：智能维度分组+分段处理+连接池优化）"
            else:
                test_tasks[task_id]["status"] = f"正在启动章节分析（默认版：性能优先+连接池优化）"
            test_tasks[task_id]["progress"] = 50

            # 使用章节分析服务的优化版本
            from src.services.chapter_analysis_service import ChapterAnalysisService

            # 初始化章节分析结果字典
            for chapter_data in chapters_data:
                chapter_analysis_results[chapter_data["id"]] = {}

            # 逐个维度进行章节分析，应用所有优化策略
            for dimension_index, dimension in enumerate(dimensions):
                # 更新任务状态
                progress = 50 + int(15 * (dimension_index / len(dimensions)))
                if prompt_template == "simplified":
                    test_tasks[task_id]["status"] = f"📊 正在分析维度: {dimension}（精简版优化：{dimension_index + 1}/{len(dimensions)}）"
                else:
                    test_tasks[task_id]["status"] = f"正在分析维度: {dimension}（默认版优化：{dimension_index + 1}/{len(dimensions)}）"
                test_tasks[task_id]["progress"] = progress

                logger.info(f"开始分析维度: {dimension}，使用优化的章节分析服务")

                try:
                    # 调用优化的章节分析服务
                    analysis_result = ChapterAnalysisService.start_chapter_analysis_task(
                        novel_id=novel_id,
                        dimension=dimension,
                        model=model,
                        parallel=True,
                        max_workers=12 if prompt_template == "default" else 5,  # 根据模式调整并行数
                        prompt_template=prompt_template  # 传递提示词模板以启用优化
                    )

                    if analysis_result.get("success"):
                        task_id_chapter = analysis_result["task_id"]
                        logger.info(f"章节分析任务已启动: {task_id_chapter}")

                        # 等待章节分析完成
                        max_wait_time = 300  # 最多等待5分钟
                        wait_interval = 5    # 每5秒检查一次
                        waited_time = 0

                        while waited_time < max_wait_time:
                            # 检查任务进度
                            progress_result = ChapterAnalysisService.get_chapter_analysis_task_progress(novel_id)

                            if progress_result.get("success"):
                                task_status = progress_result.get("status")
                                task_progress = progress_result.get("progress", 0)
                                is_running = progress_result.get("is_running", False)

                                # 更新主任务状态
                                if prompt_template == "simplified":
                                    test_tasks[task_id]["status"] = f"📊 {dimension}分析中（精简版优化）: {task_status} - {task_progress}%"
                                else:
                                    test_tasks[task_id]["status"] = f"{dimension}分析中（默认版优化）: {task_status} - {task_progress}%"

                                # 检查是否完成
                                if not is_running and task_status in ["completed", "partially_completed"]:
                                    logger.info(f"维度 {dimension} 分析完成")
                                    break
                                elif not is_running and task_status == "failed":
                                    logger.error(f"维度 {dimension} 分析失败")
                                    break

                            time.sleep(wait_interval)
                            waited_time += wait_interval

                        # 获取分析结果
                        results = ChapterAnalysisService.get_chapter_analysis_results(novel_id, dimension)
                        if results.get("success"):
                            chapters_results = results.get("chapters", {})

                            # 将结果整合到chapter_analysis_results中
                            for chapter_id, chapter_result in chapters_results.items():
                                if chapter_id not in chapter_analysis_results:
                                    chapter_analysis_results[chapter_id] = {}

                                if dimension in chapter_result.get("results", {}):
                                    result_data = chapter_result["results"][dimension]
                                    chapter_analysis_results[chapter_id][dimension] = {
                                        "content": result_data.get("content", ""),
                                        "reasoning": result_data.get("reasoning_content", ""),
                                        "reasoning_content": result_data.get("reasoning_content", "")
                                    }

                        logger.info(f"维度 {dimension} 分析完成并整合到结果中")

                    else:
                        logger.error(f"启动维度 {dimension} 分析失败: {analysis_result.get('error')}")

                except Exception as e:
                    logger.error(f"分析维度 {dimension} 时出错: {str(e)}", exc_info=True)

            # 更新最终进度
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = f"✅ 已完成所有章节分析（精简版智能优化：维度分组+分段处理+连接池管理）"
            else:
                test_tasks[task_id]["status"] = f"✅ 已完成所有章节分析（默认版性能优化：高并发+连接池管理）"
            test_tasks[task_id]["progress"] = 65

            # 写作功能保护：确保写作不受降本增效影响
            TestService._configure_writing_protection(task_id, prompt_template)

            # 生成新作品，区分精简版和默认版
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = "✍️ 正在生成新作品（精简版降本增效模式，写作功能完全不限制）"
            else:
                test_tasks[task_id]["status"] = "正在生成新作品（默认版）"
            test_tasks[task_id]["progress"] = 70

            # 创建Novel和Chapter对象用于构建提示词
            novel_obj = Novel(
                title=novel_data["title"],
                content=novel_data["content"],
                author=novel_data["author"]
            )
            novel_obj.id = novel_data["id"]
            novel_obj.word_count = novel_data["word_count"]
            # 添加小说类型信息，用于写作提示词构建
            novel_obj.novel_type = novel_type

            # 记录原文章节数量
            original_chapter_count = len(chapters_data)
            logger.info(f"原文共有 {original_chapter_count} 个章节，将生成相同数量的章节")

            # 记录每个章节的字数，用于后续比较
            chapter_word_counts = [chapter["word_count"] for chapter in chapters_data]
            logger.info(f"原文各章节字数: {chapter_word_counts}")

            # 初始化章节连贯性管理
            previous_chapters_content = []
            chapter_continuity_history = []

            for i, chapter_data in enumerate(chapters_data):
                # 更新任务状态
                progress = 70 + int(25 * (i / len(chapters_data)))
                test_tasks[task_id]["status"] = f"正在准备生成第 {i+1} 章"
                test_tasks[task_id]["progress"] = progress

                try:
                    # 为写作阶段添加延迟，确保连续性写作的稳定性
                    if i > 0:  # 第一章不延迟
                        logger.info(f"[连续性写作优化] 第{i+1}章写作前延迟{writing_delay}秒，确保API服务器稳定")
                        time.sleep(writing_delay)

                    # 创建Chapter对象用于构建提示词
                    chapter_obj = Chapter(
                        novel_id=chapter_data["novel_id"],
                        chapter_number=chapter_data["chapter_number"],
                        content=chapter_data["content"],
                        title=chapter_data["title"]
                    )
                    chapter_obj.id = chapter_data["id"]
                    chapter_obj.word_count = chapter_data["word_count"]

                    # 更新状态为开始写作
                    test_tasks[task_id]["status"] = f"正在写作第 {i+1} 章: 0字（多轮迭代+连贯性优化）"

                    # 准备连贯性数据 - 使用新的连贯性管理器
                    current_continuity_data = {}
                    writing_context = {}

                    # 初始化连贯性管理器（根据提示词模板选择配置）
                    from src.services.chapter_continuity_manager import ChapterContinuityManager

                    # 智能选择配置类型 - 精简版也使用增强累积配置
                    if prompt_template == "simplified":
                        config_type = "enhanced_cumulative"  # 精简版使用增强累积配置
                    elif prompt_template in ["enhanced", "premium", "rag"]:
                        config_type = "enhanced_cumulative"  # 使用增强累积版
                    else:
                        config_type = "default"

                    continuity_manager = ChapterContinuityManager(config_type)

                    # 添加前序章节数据到管理器
                    if previous_chapters_content:
                        for prev_idx, prev_chapter in enumerate(previous_chapters_content):
                            prev_chapter_num = prev_idx + 1
                            prev_content = prev_chapter if isinstance(prev_chapter, str) else prev_chapter.get("content", "")
                            if prev_content:
                                continuity_manager.add_chapter_data(prev_chapter_num, prev_content)

                    # 获取写作上下文
                    if i > 0:
                        writing_context = continuity_manager.get_writing_context(i + 1)
                        # 为了兼容现有代码，也提取传统的连贯性数据
                        if previous_chapters_content:
                            current_continuity_data = TestService._extract_chapter_continuity_data(
                                previous_chapters_content[-1], i
                            )

                        # 记录成本统计
                        cost_stats = continuity_manager.get_cost_statistics()
                        logger.info(f"[连贯性管理器] 第{i+1}章成本统计: "
                                   f"详细章节数: {cost_stats['detailed_chapters']}, "
                                   f"摘要章节数: {cost_stats['summary_chapters']}, "
                                   f"总字符数: {cost_stats['total_chars']}")

                    logger.info(f"[连贯性优化] 第{i+1}章连贯性数据准备完成，"
                               f"配置类型: {config_type}, "
                               f"上下文长度: {sum(len(v) for v in writing_context.values()) if writing_context else 0}字符")

                    # 使用多次迭代优化生成内容（包含连贯性支持）
                    generated_content = TestService._generate_chapter_with_iterative_optimization(
                        novel=novel_obj,
                        chapter=chapter_obj,
                        book_analysis=book_analysis_results,
                        chapter_analysis=chapter_analysis_results.get(chapter_data["id"], {}),
                        prompt_template=prompt_template,
                        max_iterations=3,  # 最多3轮迭代优化
                        previous_chapters=previous_chapters_content,
                        chapter_continuity_data=current_continuity_data,
                        writing_context=writing_context  # 新增：智能写作上下文
                    )

                    # 保存当前章节内容用于下一章节的连贯性
                    previous_chapters_content.append(generated_content)
                    chapter_continuity_history.append(current_continuity_data)

                    # 计算生成内容的字数
                    word_count = len(generated_content)

                    # 更新状态为写作完成
                    test_tasks[task_id]["status"] = f"第 {i+1} 章写作完成: {word_count}字"

                    # 先保存生成的章节内容
                    generated_chapters.append({
                        "title": f"第{i+1}章",  # 临时标题
                        "content": generated_content
                    })

                    logger.info(f"成功生成第 {i+1} 章内容: {word_count}字")
                except Exception as e:
                    logger.error(f"生成第 {i+1} 章内容时出错: {str(e)}", exc_info=True)
                    generated_chapters.append({
                        "title": f"第{i+1}章 {chapter_data['title'] or ''}",
                        "content": f"生成内容时出错: {str(e)}"
                    })

            # 生成分析摘要
            analysis_summary = TestService._generate_analysis_summary(novel_obj, book_analysis_results)

            # 所有章节内容生成完成后，基于完整内容智能生成章节标题
            test_tasks[task_id]["status"] = "正在基于生成内容创建网文风格章节标题..."
            for i, chapter in enumerate(generated_chapters):
                try:
                    # 基于完整章节内容智能生成网文风格标题
                    generated_title = TestService._generate_chapter_title_from_content(
                        chapter["content"],
                        i+1,
                        novel_data["title"],
                        generated_chapters  # 传入所有章节，便于了解整体故事脉络
                    )
                    # 更新章节标题
                    generated_chapters[i]["title"] = generated_title
                    logger.info(f"成功为第 {i+1} 章基于内容生成标题: {generated_title}")
                except Exception as e:
                    logger.error(f"为第 {i+1} 章生成标题时出错: {str(e)}", exc_info=True)
                    # 如果生成失败，使用默认标题
                    generated_chapters[i]["title"] = f"第{i+1}章"

            # 将生成的内容保存到内容仓库
            generated_novel_id = TestService._save_to_content_repository(
                novel_data["title"],
                novel_data["author"],
                generated_chapters,
                novel_id
            )

            # 计算章节字数对比信息
            chapter_word_count_comparison = []
            for i, chapter_data in enumerate(chapters_data):
                if i < len(generated_chapters):
                    original_word_count = chapter_data["word_count"]
                    generated_word_count = len(generated_chapters[i]["content"])

                    # 计算字数差异百分比
                    if original_word_count > 0:
                        difference_percent = abs(generated_word_count - original_word_count) / original_word_count * 100
                    else:
                        difference_percent = 0

                    chapter_word_count_comparison.append({
                        "chapter_number": i + 1,
                        "chapter_title": chapter_data["title"] or f"第{i+1}章",
                        "original_word_count": original_word_count,
                        "generated_word_count": generated_word_count,
                        "difference_percent": round(difference_percent, 1)
                    })

            # 整理结果 - 根据小说类型选择正确的分析维度
            if novel_type == "short" and SHORT_STORY_ANALYSIS_DIMENSIONS:
                dimensions_info = [{"key": dim["key"], "name": dim["name"]} for dim in SHORT_STORY_ANALYSIS_DIMENSIONS]
                logger.info(f"使用知乎体短篇小说分析维度，共{len(dimensions_info)}个维度")
            else:
                dimensions_info = [{"key": dim["key"], "name": dim["name"]} for dim in config.ANALYSIS_DIMENSIONS]
                logger.info(f"使用长篇小说分析维度，共{len(dimensions_info)}个维度")

            results = {
                "novel_id": novel_id,
                "novel_title": novel_data["title"],
                "novel_type": novel_type,  # 添加小说类型信息
                "analysis_summary": analysis_summary,
                "dimensions": dimensions_info,
                "generated_chapters": generated_chapters,
                "generated_novel_id": generated_novel_id,  # 添加生成的小说ID，方便用户在内容仓库中查看
                "chapter_count": len(generated_chapters),  # 添加章节数量
                "original_chapter_count": len(chapters_data),  # 添加原文章节数量
                "chapter_word_count_comparison": chapter_word_count_comparison  # 添加章节字数对比信息
            }

            # 更新任务状态，区分精简版和默认版
            if prompt_template == "simplified":
                test_tasks[task_id]["status"] = "🎉 已完成（精简版降本增效模式），内容已保存到内容仓库"
            else:
                test_tasks[task_id]["status"] = "已完成（默认版），内容已保存到内容仓库"
            test_tasks[task_id]["progress"] = 100
            test_tasks[task_id]["completed"] = True
            test_tasks[task_id]["results"] = results

            logger.info(f"分析写作任务 {task_id} 已完成，生成的内容已保存到内容仓库，ID: {generated_novel_id}")
        except Exception as e:
            logger.error(f"分析写作任务 {task_id} 失败: {str(e)}", exc_info=True)
            test_tasks[task_id]["status"] = "失败"
            test_tasks[task_id]["error"] = str(e)
            test_tasks[task_id]["completed"] = True
            test_tasks[task_id]["results"] = None  # 确保失败时也有results字段

    @staticmethod
    def _build_writing_prompt(novel: Novel, chapter: Chapter,
                             book_analysis: Dict[str, Dict[str, str]],
                             chapter_analysis: Dict[str, Dict[str, str]],
                             prompt_template: str = "default",
                             iteration_round: int = 1,
                             previous_content: str = "",
                             logic_feedback: str = "") -> str:
        """
        构建写作提示词

        Args:
            novel: 小说对象
            chapter: 章节对象
            book_analysis: 整本书分析结果
            chapter_analysis: 章节分析结果
            prompt_template: 提示词模板，默认为 default

        Returns:
            写作提示词
        """
        # 添加调试日志
        logger.info(f"构建写作提示词 - 小说标题: {novel.title if novel else 'None'}")
        logger.info(f"构建写作提示词 - 章节标题: {chapter.title if chapter else 'None'}")
        logger.info(f"构建写作提示词 - 章节编号: {chapter.chapter_number if chapter else 'None'}")
        logger.info(f"构建写作提示词 - 章节字数: {chapter.word_count if chapter else 'None'}")
        logger.info(f"构建写作提示词 - 整本书分析维度数量: {len(book_analysis) if book_analysis else 0}")
        logger.info(f"构建写作提示词 - 章节分析维度数量: {len(chapter_analysis) if chapter_analysis else 0}")
        logger.info(f"构建写作提示词 - 使用提示词模板: {prompt_template}")

        # 检查关键参数是否为空
        if not novel:
            logger.error("构建写作提示词失败: novel参数为空")
            return ""
        if not chapter:
            logger.error("构建写作提示词失败: chapter参数为空")
            return ""
        if not chapter.content:
            logger.error("构建写作提示词失败: chapter.content为空")
            return ""

        # 收集所有可用的分析维度
        all_dimensions = set(book_analysis.keys()) | set(chapter_analysis.keys())

        # 检查是否为短篇小说（通过novel_type判断）
        novel_type = getattr(novel, 'novel_type', 'long')  # 默认为长篇
        is_short_story = novel_type == 'short'

        # 添加详细的调试日志
        logger.info(f"🔍 写作提示词构建调试信息:")
        logger.info(f"   - novel.novel_type: {getattr(novel, 'novel_type', '未设置')}")
        logger.info(f"   - novel_type: {novel_type}")
        logger.info(f"   - is_short_story: {is_short_story}")
        logger.info(f"   - prompt_template: {prompt_template}")
        logger.info(f"   - 将使用的提示词类型: {'知乎体短篇小说' if is_short_story else '长篇小说'}")

        # 根据提示词模板和小说类型构建不同的分析摘要
        if prompt_template == "simplified":
            # 精简版提示词 - 降本增效模式 + AI指令理解增强
            if is_short_story:
                analysis_summary = f"""# 知乎体短篇小说分析与写作指导（精简版降本增效模式 + AI指令理解增强）

## 🎯 重要提醒：写作功能完全不限制
**注意：虽然是精简版模式，但写作功能完全不受任何限制，请全力发挥创作能力！**

## 📋 知乎体短篇小说AI指令理解确认清单（必须全部执行）
请在开始写作前确认以下指令已完全理解：
✅ 1. 创作知乎体（盐选故事）风格的全新内容，绝不复制原文情节
✅ 2. 开篇45字内抛出核心冲突（三秒定律）
✅ 3. 情绪驱动：精准触发虐文/爽文情绪
✅ 4. 快节奏强冲突：每1000字包含关键剧情
✅ 5. 第一人称叙述，口语化纪实性语言
✅ 6. 目标字数约{chapter.word_count}字（知乎体标准）
✅ 7. 基于分析结果和推理过程+全部原文样本（学习句子结构/语言/12个知乎体维度，不复制情节）指导创作

## 原文信息
- 小说标题: {novel.title}
- 章节标题: {chapter.title or f'第{chapter.chapter_number}章'}
- 章节编号: {chapter.chapter_number}
- 章节字数: 约{chapter.word_count}字

## 🔑 知乎体核心要求（多层确认）
### 第一层确认：知乎体基本要求
- 开篇即高潮：45字内抛出核心冲突或悬念
- 情绪驱动：精准触发虐文/爽文情绪，极致人设激发愤怒或爽感
- 快节奏强冲突：每1000字包含关键剧情，避免背景铺陈过长
- 第一人称叙述：口语化纪实性语言，模拟知乎问答真实感

### 第二层确认：格式要求
- 三行切割法：单段≤3行（手机屏幕显示上限）
- 情绪单元独立成段：关键动作、致命台词、感官冲击独占段落
- 空行使用逻辑：导语→正文3行，场景转换2行，关键反转1行

### 第三层确认：执行验证
- 写作完成后检查是否符合知乎体特征
- 确认字数是否达到目标（8000-12000字）
- 验证内容质量是否满足盐选故事标准

## 🎓 知乎体学习指导说明（基于完整分析和原文样本）
### 📚 学习内容范围
1. **分析结果学习**：理解每个知乎体维度的分析内容，作为创作指导
2. **推理过程学习**：学习分析的思路和方法，指导创作逻辑
3. **原文样本学习**：
   - ✅ 学习句子结构和表达方式
   - ✅ 学习语言风格和叙述技巧
   - ✅ 学习12个知乎体维度的具体表现
   - ❌ 绝不复制原文情节和故事内容

### 🎯 知乎体创作原则
- **学习形式，创新内容**：学习原文的写作技巧，但创作全新的故事情节
- **维度指导创作**：每个知乎体分析维度都要在新内容中有所体现
- **推理过程应用**：将分析的推理逻辑应用到新内容的构思中
- **情绪经济导向**：平衡商业性与艺术性，精准触发读者情绪

## 关键分析要点（精简版但详细指导）
"""
            else:
                analysis_summary = f"""# 小说分析与写作指导（精简版降本增效模式 + AI指令理解增强）

## 🎯 重要提醒：写作功能完全不限制
**注意：虽然是精简版模式，但写作功能完全不受任何限制，请全力发挥创作能力！**

## 📋 AI指令理解确认清单（必须全部执行）
请在开始写作前确认以下指令已完全理解：
✅ 1. 创作全新内容，绝不复制原文情节
✅ 2. 保持人物名称一致性
✅ 3. 确保章节过渡自然，情节连贯
✅ 4. 保持叙述风格一致性
✅ 5. 目标字数约{chapter.word_count}字
✅ 6. 基于分析结果和推理过程+全部原文样本（学习句子结构/语言/15个维度，不复制情节）指导创作

## 原文信息
- 小说标题: {novel.title}
- 章节标题: {chapter.title or f'第{chapter.chapter_number}章'}
- 章节编号: {chapter.chapter_number}
- 章节字数: 约{chapter.word_count}字

## 🔑 核心要求（多层确认）
### 第一层确认：基本要求
- 保持人物名称一致性，避免随意更改
- 确保章节过渡自然，情节连贯
- 保持叙述风格一致性

### 第二层确认：质量要求
- 创作内容必须完全原创
- 情节发展要有逻辑性
- 人物行为要符合性格设定

### 第三层确认：执行验证
- 写作完成后检查是否符合所有要求
- 确认字数是否达到目标
- 验证内容质量是否满足标准

## 🎓 学习指导说明（基于完整分析和原文样本）
### 📚 学习内容范围
1. **分析结果学习**：理解每个维度的分析内容，作为创作指导
2. **推理过程学习**：学习分析的思路和方法，指导创作逻辑
3. **原文样本学习**：
   - ✅ 学习句子结构和表达方式
   - ✅ 学习语言风格和叙述技巧
   - ✅ 学习15个维度的具体表现
   - ❌ 绝不复制原文情节和故事内容

### 🎯 创作原则
- **学习形式，创新内容**：学习原文的写作技巧，但创作全新的故事情节
- **维度指导创作**：每个分析维度都要在新内容中有所体现
- **推理过程应用**：将分析的推理逻辑应用到新内容的构思中

## 关键分析要点（精简版但详细指导）
"""
        else:
            # 默认版提示词 - 完整模式
            analysis_summary = f"""# 小说分析与写作指导

## 原文信息
- 小说标题: {novel.title}
- 章节标题: {chapter.title or f'第{chapter.chapter_number}章'}
- 章节编号: {chapter.chapter_number}
- 章节字数: 约{chapter.word_count}字

## 人物连贯性要求
- 必须保持人物名称的一致性，避免随意更改人物姓名
- 人物性格和行为模式必须与前序章节保持连贯
- 人物关系发展应自然合理，避免突兀的转变

## 章节过渡要求
- 本章节必须与前一章节在情节、情感和氛围上自然衔接
- 避免突兀的场景转换，确保时间和空间的逻辑连贯性
- 保持叙述风格和语言特色的一致性

## 原文风格分析摘要
"""

        # 添加各个维度的分析摘要
        dimension_mapping = {
            "language_style": "语言风格",
            "rhythm_pacing": "节奏节拍",
            "structure": "结构分析",
            "paragraph_flow": "段落流畅度",
            "character_relationships": "人物关系",
            "world_building": "世界构建",
            "opening_effect": "开篇效果",
            "climax_pacing": "高潮节奏",
            "novel_features": "小说特点",
            "sentence_variation": "句式变化",
            "perspective_changes": "视角变化",
            "chapter_outline": "章纲分析",
            "outline_analysis": "大纲分析",
            "hot_meme_statistics": "热梗统计"
        }

        # 根据提示词模板和小说类型处理分析维度
        if prompt_template == "simplified":
            # 精简版：只包含核心维度的关键要点，大幅减少内容长度
            if is_short_story:
                # 短篇小说使用知乎体专用维度
                core_dimensions = ["opening_hook", "emotion_driving", "conflict_intensity", "character_labeling", "narrative_authenticity"]
                # 更新维度映射以支持知乎体维度
                short_story_dimension_mapping = {
                    "opening_hook": "开篇钩子",
                    "emotion_driving": "情绪驱动",
                    "conflict_intensity": "冲突强度",
                    "character_labeling": "人物标签化",
                    "narrative_authenticity": "叙述真实感",
                    "plot_density": "情节密度",
                    "theme_extremization": "主题极致化",
                    "commercial_balance": "商业平衡",
                    "innovation_within_formula": "套路内创新",
                    "pacing_control": "节奏控制",
                    "ending_impact": "结尾冲击",
                    "social_relevance": "社会相关性"
                }
                dimension_mapping.update(short_story_dimension_mapping)
            else:
                # 长篇小说使用传统维度
                core_dimensions = ["language_style", "rhythm_pacing", "structure", "character_relationships", "world_building"]

            for dimension in core_dimensions:
                if dimension in all_dimensions:
                    dimension_name = dimension_mapping.get(dimension, dimension.replace("_", " ").title())

                    # 优先使用章节分析，如果没有则使用整本书分析
                    if dimension in chapter_analysis and chapter_analysis[dimension].get("content"):
                        content = chapter_analysis[dimension]["content"]
                        # 精简版：只取前200字的关键要点
                        simplified_content = content[:200] + "..." if len(content) > 200 else content
                        analysis_summary += f"### {dimension_name}（精简）\n{simplified_content}\n\n"
                    elif dimension in book_analysis and book_analysis[dimension].get("content"):
                        content = book_analysis[dimension]["content"]
                        # 精简版：只取前200字的关键要点
                        simplified_content = content[:200] + "..." if len(content) > 200 else content
                        analysis_summary += f"### {dimension_name}（精简）\n{simplified_content}\n\n"
        else:
            # 默认版：包含完整的分析结果和推理过程
            for dimension in all_dimensions:
                dimension_name = dimension_mapping.get(dimension, dimension.replace("_", " ").title())

                # 优先使用章节分析，如果没有则使用整本书分析
                if dimension in chapter_analysis and chapter_analysis[dimension].get("content"):
                    content = chapter_analysis[dimension]["content"]
                    reasoning = chapter_analysis[dimension].get("reasoning_content", "")

                    # 添加分析结果
                    analysis_summary += f"### {dimension_name} - 章节分析结果\n{content}\n\n"

                    # 添加完整的推理过程（不截断，这是关键信息）
                    if reasoning:
                        analysis_summary += f"### {dimension_name} - 章节推理过程（完整）\n{reasoning}\n\n"
                    else:
                        # 如果章节分析没有推理过程，尝试使用整本书的推理过程
                        if dimension in book_analysis:
                            book_reasoning = book_analysis[dimension].get("reasoning_content", "")
                            if book_reasoning:
                                analysis_summary += f"### {dimension_name} - 整本书推理过程（作为章节推理参考）\n{book_reasoning}\n\n"

                elif dimension in book_analysis and book_analysis[dimension].get("content"):
                    content = book_analysis[dimension]["content"]
                    reasoning = book_analysis[dimension].get("reasoning_content", "")

                    # 添加分析结果
                    analysis_summary += f"### {dimension_name} - 整本书分析结果\n{content}\n\n"

                    # 添加完整的推理过程（不截断，这是关键信息）
                    if reasoning:
                        analysis_summary += f"### {dimension_name} - 整本书推理过程（完整）\n{reasoning}\n\n"

        # 添加创新性写作任务 - 基于维度驱动的故事创新
        if is_short_story:
            # 短篇小说使用知乎体专用的创新性写作任务
            analysis_summary += f"""
## 知乎体创新性写作任务 - "学我者生，似我者死"

### 核心理念：基于知乎体维度分析构建全新故事脉络

请基于上述12个知乎体维度的深度分析结果，创作一个在风格上"学习"原文精髓，但在情节上完全"脱胎"于原文的全新知乎体短篇小说。

### 知乎体特殊要求：
1. **开篇即高潮**：45字内抛出核心冲突（三秒定律）
2. **情绪驱动**：精准触发虐文/爽文情绪，极致人设激发愤怒或爽感
3. **快节奏强冲突**：每1000字包含关键剧情，避免背景铺陈过长
4. **第一人称叙述**：口语化纪实性语言，模拟知乎问答真实感
5. **人物标签化**：使用扶弟魔、凤凰男、真假千金等标签化人设
6. **格式规范**：三行切割法，情绪单元独立成段，空行使用逻辑

### 知乎体创作目标：
创作出一个读者看后会说"这个知乎体故事风格很像原文，但情节完全不同，很有创意"的全新知乎体短篇小说。
"""
        else:
            # 长篇小说使用传统的创新性写作任务
            analysis_summary += f"""
## 创新性写作任务 - "学我者生，似我者死"

### 核心理念：基于维度分析构建全新故事脉络

请基于上述15个维度的深度分析结果，创作一个在风格上"学习"原文精髓，但在情节上完全"脱胎"于原文的全新章节。

### 创作目标：
创作出一个读者看后会说"这个作者的风格很像原文，但故事完全不同，很有创意"的全新章节，而不是"这是抄袭的"。
"""

        # 添加通用的创作指导（适用于长篇和短篇）
        analysis_summary += f"""

### 第一阶段：维度精髓提取与重组
1. **语言风格精髓**：提取原文的语言节奏、表达习惯、修辞特色，但应用到全新的场景和情节中
2. **结构特色重组**：学习原文的章节架构、段落组织、情节推进方式，但构建完全不同的故事线
3. **人物关系模式**：学习原文的人物互动方式、关系发展模式，但创造全新的人物和关系网络
4. **世界构建理念**：学习原文的世界观构建方法、背景设定思路，但创造全新的故事背景
5. **情感基调运用**：学习原文的情感表达方式、氛围营造技巧，但表达全新的情感内容

### 第二阶段：独立故事主线构建
基于维度分析结果，构建一个与原文情节完全不同的故事主线：

1. **情节创新要求**：
   - 绝对禁止使用原文的情节发展脉络
   - 必须基于维度分析创造全新的情节发展路径
   - 可以是完全不同的故事类型：日常生活、商业竞争、学习成长、家庭关系、友情发展等
   - 情节发展要有独立的逻辑性和创新性

2. **场景环境创新**：
   - 完全更换故事发生的环境和背景
   - 可以是现代都市、古代宫廷、乡村田园、校园生活、职场环境等
   - 环境设定要与新的情节主线相匹配

3. **冲突设置创新**：
   - 设计与原文完全不同的矛盾冲突
   - 冲突可以是内心挣扎、人际关系、目标追求、价值观碰撞等
   - 冲突解决方式要体现原文的风格特色但内容全新

### 第三阶段：风格特色的创造性应用
1. **语言风格**：使用原文的表达习惯和语言节奏，但描述全新的内容
2. **节奏控制**：学习原文的节奏变化规律，但应用到新的情节发展中
3. **人物塑造**：学习原文的人物刻画方法，但创造全新的人物形象
4. **情感表达**：学习原文的情感渲染技巧，但表达不同的情感内容

### 具体创作要求：
1. 内容长度：约{chapter.word_count}字
2. 格式要求：{"知乎体短篇小说格式，无需章节编号，直接开始故事内容" if is_short_story else "必须为\"# 第{chapter.chapter_number}章 [全新标题]\""}
3. 人物名称：创造全新的、有特色的人名（避免烂大街的名字）
4. 情节主线：与原文完全不同的故事发展路径
5. 开头设计：与原文有明显差异的开场方式
6. 语言风格：保持原文的语言特色和表达习惯
7. 结构安排：{"学习原文的知乎体连续叙述结构，无需分章节" if is_short_story else "学习原文的章节结构但应用到新内容中"}
8. 情感基调：体现原文的情感表达方式但内容全新

### 禁止事项：
1. 严禁复制或模仿原文的具体情节发展
2. 严禁使用原文中的人物名称
3. 严禁使用原文的具体场景和事件
4. 严禁按照原文的情节脉络发展故事
5. 严禁使用HTML标签，只使用Markdown格式
{"6. 严禁使用章节标题，直接开始故事内容" if is_short_story else ""}

请直接开始创作，展现"学我者生"的创新精神。
"""

        # 将原文样本学习指导提前到重要位置，并大幅增强学习指导
        original_sample = chapter.content

        if is_short_story:
            # 短篇小说使用知乎体专用的学习指导
            analysis_summary += f"""

## 🎓 知乎体原文样本深度学习指导（核心重点：学习技巧，创新内容）

### 📚 原文完整内容（必须深度学习的知乎体技巧宝库）

{original_sample}

### 🔍 12个知乎体维度深度学习指导（每个维度都要仔细学习）

#### 1. 开篇钩子深度学习
- **45字法则**：观察原文如何在45字内抛出核心冲突
- **三秒定律**：学习原文如何在3秒内产生"为什么？"或"后来呢？"的疑问
- **异常事件**：学习原文如何设置反常识场景
- **行动悬念**：学习原文如何留下行动性悬念

#### 2. 情绪驱动深度学习
- **情绪触发**：学习原文如何精准触发虐文/爽文情绪
- **极致人设**：学习原文如何设计激发愤怒或爽感的人物
- **情绪递进**：学习原文如何层层递进情绪强度
- **情绪锚点**：学习原文如何设置情绪爆发点

#### 3. 冲突强度深度学习
- **矛盾极致化**：学习原文如何将冲突推向极致
- **情节密度**：学习原文每1000字包含关键剧情的技巧
- **冲突升级**：学习原文如何逐步升级矛盾
- **高潮设计**：学习原文的冲突高潮构建方法

#### 4. 人物标签化深度学习
- **标签化人设**：学习原文如何使用扶弟魔、凤凰男等标签
- **功能性设计**：学习原文如何让每个角色有明确情绪价值
- **关系词策略**：学习原文如何用"丈夫""婆婆"等替代人名
- **极致化处理**：学习原文如何极致化人物特征

#### 5. 叙述真实感深度学习
- **第一人称技巧**：学习原文的第一人称叙述技巧
- **口语化表达**：学习原文的口语化纪实性语言
- **真实感营造**：学习原文如何模拟知乎问答真实感
- **代入感设计**：学习原文如何增强读者代入感

#### 6. 知乎体爆款案例分段技巧深度学习（基于实战案例）
- **导语钩子**：学习"怀孕第三个月，老公他奶上门立规矩"式的45字黄金开头
- **三段式导语**：学习"她故意杀了我养了三年的狗→餐桌炫耀→饭后立规矩"的递进结构
- **反转预告**：学习"只是她不知道，认干奶奶容易，送干奶奶就难了！"的悬念设置
- **章节数字标记**：学习用简单数字"1"替代传统章节标题的技巧
- **背景铺垫段**：学习"我奶是杠精，我妈是悍妇"式的人物关系快速建立
- **冲突升级链**：学习从"杀狗→炖汤→立规矩→反击"的情节推进节奏
- **对话独立成段**：学习每句关键台词独占一行的分段技巧
- **动作细节段**：学习"球球好似闪电一样，冲到公婆面前"的动作描写独立成段
- **心理活动简化**：学习用行为代替"我想""我觉得"等心理描述
- **空行节奏控制**：学习在关键反转前后使用空行制造停顿效果
- **拟声词独立**：学习"嗷嗷嗷！"等拟声词独占段落的技巧
- **加粗强调**：学习用**加粗**突出关键信息的视觉技巧
- **结尾留白**：学习"希望善良的人都能被老天庇佑"式的开放式结尾

### 🎯 知乎体短篇小说格式特殊要求（重要！）

#### 📝 格式核心原则："视觉呼吸感+情绪节奏器"
- **无章节标题**：知乎体短篇小说是连续的整体故事，不分章节，直接开始故事内容
- **连续叙述**：从开头到结尾是一个完整的故事，无需任何章节分割
- **移动端专用设计**：专为手机阅读场景优化的分段规则

#### 📱 整体结构框架（万字小说分段示例）
```
[导语]（黄金45字，独立成段）
▼▼▼ （空行分隔，暗示正文开始）

[开篇冲突]（300-500字，2-4段）
- 段1：快速交代导语后续
- 段2：倒叙关键背景（1句话回溯）
- 段3：深化矛盾

[发展推进]（每800字一个情绪爆点）
▌ 段落群1（铺垫仇恨）
▌ 段落群2（反转准备）

[付费点]（2500字处设置）
⚠️ 在冲突顶点突然断章，下一段首句即结果

[高潮决战]（多采用"对话搏击式"分段）
[结局]（利落收尾≤3段）
```

#### 🔥 分段核心法则（头部盐选编辑规范）

##### 1. "三行切割法"（移动端生死线）
- **单段≤3行**（手机屏幕显示上限）
- ✅ 正确示例：
  ```
  "刀尖抵住他喉咙...
  （空行）
  '这一刀...'
  （空行）
  '是你教我的'"（共3段5行）
  ```
- ❌ 错误：超过4行的长段落会导致读者疲劳

##### 2. 情绪单元独立成段
- **关键动作**：`我撕碎孕检单...（动作描写独占段）`
- **致命台词**：`'孩子是你初恋儿子的'（对话独立段+空行隔离）`
- **感官冲击**：`**血滴在保险单签名处**（加粗强化视觉）`

##### 3. 空行使用逻辑（精确规范）
| 场景 | 空行数量 | 作用 |
|------|----------|------|
| 导语→正文过渡 | 3行 | 制造阅读仪式感 |
| 时间/场景转换 | 2行 | 替代传统过渡语 |
| 关键反转前后 | 1行 | 制造心跳暂停效果 |
| 长描写后接短句 | 1行 | 重置读者注意力 |

#### 💥 情绪节奏器要求
- **每300字必须有情绪爆点段**（加粗/短句/悬念）
- **付费点前最后三段呈加速结构**（例：动作→台词→拟声词）
- **关键台词/动作必须独占段落**
- **拟声词独立成段**：`**滴——**（拟声词段）`

#### 🎭 知乎体实战案例分段技巧（基于爆款案例深度学习）
##### 导语三段式经典结构：
```
怀孕第三个月，老公他奶上门立规矩。（背景+冲突）
她故意杀了我养了三年的狗，炖汤恶心我。（具体事件）
餐桌上，她得意炫耀：「吃精贵狗粮长大的小畜生肉就是鲜~」（关键台词）
饭后，又让我给她养了四十年的乌龟磕头认干奶奶。（升级冲突）
我听话照做。（表面妥协）
只是她不知道，认干奶奶容易，送干奶奶就难了！（反转预告）
```

##### 数字章节标记技巧：
- 用简单数字"1"替代传统章节标题
- 数字后直接开始故事，无需空行过渡
- 学习"1"这种极简标记的视觉效果

##### 人物关系快速建立：
```
我奶是杠精，我妈是悍妇，两人斗了一辈子，见面就掐。（一句话建立背景）
在两人的耳濡目染下，我小小年纪便深谙婆媳之道。（主角能力来源）
人见人怕，狗见狗跑。（能力程度）
```

##### 对话独立成段经典示例：
```
「奶！」（称呼独立）
（空行）
然后拽着我回房。（动作承接）
（空行）
「老婆，收拾行李，咱上住几天...」（完整台词独立）
```

##### 动作细节段落化：
```
球球好似闪电一样，冲到公婆面前。（动作描写独立）
（空行）
在马犬的威胁下，老太太被逼跳到沙发顶。（结果描写独立）
（空行）
两条柴火棍一样的小腿瑟瑟发抖。（细节特写独立）
```

##### 拟声词独立技巧：
```
「嗷嗷嗷！」（拟声词独占段落）
（空行）
这一回，球球不仅冲老太太叫，还往她身上扑。（动作承接）
```

##### 加粗强调视觉技巧：
```
**玻璃碎片扎进掌心**（感官强化段）
（空行）
**血滴在离婚协议上**（关键画面强化）
（空行）
**滴——**（拟声词加粗）
```

#### 🎬 知乎体复杂情节案例深度学习（基于第二个爆款案例）
##### 多层次导语结构：
```
和老公亲密时，借住的闺蜜突然闯了进来。（核心冲突）
我们以极难为情的姿势被撞见，还来不及反应过来。（具体画面）
「星瑶，我借用下卫生间。」（第一次打断）
「你们继续哈，当我不存在就好……」（假装无意）
过了一会，敲门声又响起。（升级预告）
「星瑶，不好意思，我的精华液也没了，你能不能再借我？」（第二次打断）
老公咬牙切齿地在我耳边低语...（男主反应）
「顾星瑶，再这样下去，憋坏了我，你下半辈子的幸福就没了。」（威胁台词）
「你看着办！」（最后通牒）
```

##### 情节推进的节奏控制：
- **三次打断法**：面膜→精华液→卫生间，层层递进
- **男主情绪升级**：幽怨→咬牙切齿→威胁→爆发
- **时间节点标记**：用数字"1""2"等分割大的情节段落
- **悬念设置**：每个段落结尾都有钩子引向下一段

##### 对话的情绪层次：
```
「星瑶，我的面膜没了...」（试探性请求）
（空行）
「你的好闺蜜！」（男主不满）
（空行）
「星瑶，不好意思，我的精华液也没了...」（得寸进尺）
（空行）
「顾星瑶，再这样下去，憋坏了我...」（威胁升级）
```

##### 心理描写的外化技巧：
- 用行为代替心理：「我小心翼翼地瞧了眼江辞暮」
- 用对话暴露内心：「老婆，如果我要杀人，你会不会替我收尸？」
- 用细节展现情绪：「双眼憋得通红，后槽牙都要咬碎了」

##### 长篇情节的分段策略：
- **数字分段**：用"1""2"等数字分割大段落
- **场景转换**：每个数字段落代表一个完整场景
- **情绪递进**：每段都有明确的情绪主题
- **悬念承接**：段落间用悬念连接

##### 高级情节设计技巧：
- **多线并进**：主线（夫妻关系）+副线（闺蜜觊觎）同时推进
- **伏笔呼应**：前文的细节在后文得到解释和回应
- **反转设计**：表面妥协实则布局，看似失败实则胜利
- **情绪控制**：通过节奏控制读者的情绪起伏

##### 人物塑造的分段体现：
```
江辞暮的情绪变化：
「你的好闺蜜！」（初期不满）
→「顾星瑶，再这样下去，憋坏了我...」（威胁升级）
→「老婆，如果我要杀人，你会不会替我收尸？」（黑化边缘）
→「看见一个脱光的女人，就能随便发情的，那不是人，是畜生。」（价值观展现）
```

##### 悬念设置的层次递进：
- **表层悬念**：闺蜜什么时候搬走？
- **深层悬念**：闺蜜的真实目的是什么？
- **终极悬念**：主角如何反击？
- **结局悬念**：所有人的最终命运如何？

##### 知乎体特有的叙述技巧：
- **第一人称限制视角**：只展现主角知道的信息
- **现在时叙述**：增强代入感和紧张感
- **口语化表达**：「呐，现在还得她弟弟的婚事都闹没了」
- **网络用语融入**：适度使用网络流行语增强真实感

#### ⚠️ 格式禁止事项
- **严禁使用章节标题**：如"第X章"、"Chapter X"等
- **严禁分章节写作**：整个故事应该是连续的
- **严禁传统小说格式**：不使用长篇小说的章节结构
- **严禁大段心理描写**：必须拆解为短句+空行
- **严禁对话粘连**：每句对话必须独立成段
- **严禁删除"他想""她感到"等心理提示词**：用行为代替

#### 📋 格式质检清单（写作后必查）
1. **视觉检测**：
   - 手机预览时每屏有≥1个空行分隔块？
   - 关键台词/动作是否独占段落？
2. **节奏检测**：
   - 每300字是否有情绪爆点段？
   - 付费点前最后三段是否呈加速结构？
3. **冗余处理**：
   - 是否删除所有"他想""她感到"等心理提示词？
   - 是否合并连续环境描写≥2行的段落？

> **终极心法**：知乎体的段落是情绪子弹，每个回车键都是扳机。
"""
        else:
            # 长篇小说使用传统的学习指导
            analysis_summary += f"""

## 🎓 原文样本深度学习指导（核心重点：学习技巧，创新内容）

### 📚 原文完整内容（必须深度学习的技巧宝库）

{original_sample}

### 🔍 15个维度深度学习指导（每个维度都要仔细学习）

#### 1. 语言风格深度学习
- **用词习惯**：观察原文偏好使用哪些词汇类型（书面语/口语、简单词/复杂词）
- **表达方式**：学习原文的表达节奏、语言温度、情感色彩
- **语言节奏**：分析原文句子的长短搭配、停顿节奏、语言流畅度
- **修辞特色**：学习原文使用的修辞手法，但要适度，不过度使用

#### 2. 句式变化深度学习
- **句子长短搭配**：观察原文如何搭配长句和短句，形成节奏感
- **句式多样性**：学习原文的陈述句、疑问句、感叹句的使用比例
- **复杂句处理**：学习原文如何处理复杂句子，保持可读性
- **句式连贯**：重点学习句子之间的自然过渡和逻辑连接

#### 3. 叙述技巧深度学习
- **叙述视角**：学习原文的第一人称/第三人称使用技巧
- **描写方法**：学习原文的人物描写、环境描写、心理描写技巧
- **对话处理**：学习原文的对话风格、对话与叙述的穿插技巧
- **视角转换**：学习原文如何在不同视角间自然切换

#### 4. 节奏控制深度学习
- **情节推进速度**：观察原文的快节奏和慢节奏部分如何安排
- **紧张与舒缓交替**：学习原文如何调节读者的情绪节奏
- **高潮设计**：学习原文的高潮部分如何构建和处理
- **过渡技巧**：学习原文在不同节奏间的过渡方法

#### 5. 段落组织深度学习
- **段落长度**：观察原文的段落长短搭配规律
- **过渡方式**：学习原文段落间的自然过渡技巧
- **结构安排**：学习原文的整体结构和局部结构安排
- **逻辑连接**：学习原文段落间的逻辑关系处理

#### 6. 人物刻画深度学习
- **人物描写技巧**：学习原文如何通过外貌、动作、语言刻画人物
- **性格展现方式**：学习原文如何通过细节展现人物性格
- **人物对话特色**：学习原文中不同人物的语言个性化处理
- **人物关系处理**：学习原文中人物关系的发展和变化

#### 7. 环境描写深度学习
- **场景描述方法**：学习原文的环境描写技巧和详略处理
- **氛围营造技巧**：学习原文如何通过环境烘托情感氛围
- **环境与情节融合**：学习原文如何让环境描写服务于情节发展
- **适度原则**：学习原文环境描写的分寸把握

#### 8. 对话风格深度学习
- **对话特点**：学习原文对话的自然度、生活化程度
- **语言个性化**：学习原文如何让不同人物有不同的说话风格
- **对话功能**：学习原文对话如何推进情节、展现性格、营造氛围
- **对话与叙述衔接**：重点学习对话前后的自然过渡

#### 9. 情感表达深度学习
- **情感渲染技巧**：学习原文如何表达和渲染各种情感
- **氛围营造方法**：学习原文如何营造不同的情感氛围
- **情感层次**：学习原文如何处理复杂的情感变化
- **情感真实性**：学习原文情感表达的真实感和感染力

#### 10. 细节处理深度学习
- **细节描写**：学习原文的细节选择和描写技巧
- **生活化表达**：学习原文中贴近生活的表达方式
- **细节功能**：学习原文细节如何服务于人物塑造和情节发展
- **细节与整体平衡**：学习原文细节描写的分寸把握

#### 11. 流畅性深度学习（重点中的重点）
- **句式连贯性**：仔细学习原文句子间的自然连接
- **段落过渡技巧**：深度学习原文段落间的流畅过渡
- **逻辑连贯性**：学习原文的逻辑发展和因果关系
- **整体流畅度**：学习原文整体的阅读流畅感

#### 12. 对话与叙述衔接深度学习（关键技巧）
- **自然过渡**：学习原文如何在对话和叙述间自然切换
- **节奏控制**：学习原文对话和叙述的节奏搭配
- **功能互补**：学习原文对话和叙述如何互相补充
- **无缝衔接**：学习原文避免突兀跳转的技巧

#### 13. 逻辑性深度学习
- **事件合理性**：学习原文中事件发生的逻辑合理性
- **人物行为动机**：学习原文中人物行为的动机设计
- **因果关系**：学习原文中完整的因果关系链条
- **逻辑自洽**：学习原文如何保持内在逻辑的一致性

#### 14. 生活化表达深度学习
- **通俗易懂**：学习原文中简单易懂的表达方式
- **日常化描述**：学习原文中贴近日常生活的描述
- **自然表达**：学习原文中自然流畅的语言表达
- **避免别扭**：学习原文如何避免别扭的描述组合

#### 15. 题材处理深度学习
- **题材特色**：深度分析原文的题材特点和处理方式
- **背景设定**：学习原文的背景设定技巧和世界观构建
- **题材与风格融合**：学习原文如何让题材与语言风格完美融合
- **创新空间**：分析原文题材中可以创新发展的空间

### 🎯 学习应用原则（核心重点）

#### ✅ 正确学习方式：
1. **技巧学习**：深度学习原文的所有写作技巧和表达方法
2. **风格传承**：完整继承原文的语言风格和叙述特色
3. **结构借鉴**：学习原文的结构安排和组织方式
4. **节奏掌握**：掌握原文的节奏控制和情感调节技巧
5. **细节处理**：学习原文的细节选择和处理方法

#### ❌ 严格禁止事项：
1. **情节复制**：绝对不要复制原文的任何情节发展
2. **场景模仿**：绝对不要使用原文的具体场景和事件
3. **人物重复**：绝对不要使用原文中的人物名称和关系
4. **对话引用**：绝对不要直接引用原文的对话内容
5. **故事脉络**：绝对不要按照原文的故事发展脉络

#### 🔄 创新应用方式：
1. **技巧移植**：将学到的技巧应用到全新的故事中
2. **风格重现**：用原文的风格讲述完全不同的故事
3. **结构重组**：用原文的结构承载全新的内容
4. **节奏重构**：用原文的节奏推进不同的情节
5. **细节重塑**：用原文的细节处理方法描述新的内容

### 📋 学习检查清单（必须全部完成）

在开始创作前，请确认已完成以下学习：
- 1. 深度分析了原文的语言风格特点
- 2. 仔细学习了原文的句式变化规律
- 3. 掌握了原文的叙述技巧和视角处理
- 4. 理解了原文的节奏控制方法
- 5. 学会了原文的段落组织技巧
- 6. 掌握了原文的人物刻画方法
- 7. 学习了原文的环境描写技巧
- 8. 理解了原文的对话风格特色
- 9. 掌握了原文的情感表达方式
- 10. 学会了原文的细节处理技巧
- 11. 深度学习了原文的流畅性技巧
- 12. 掌握了原文的对话与叙述衔接方法
- 13. 理解了原文的逻辑性处理
- 14. 学会了原文的生活化表达
- 15. 分析了原文的题材处理特色

只有完成以上所有学习，才能开始创作全新的故事！
"""

        # 根据迭代轮次添加不同的优化指导
        if iteration_round > 1:
            analysis_summary += f"""

## 🔄 第{iteration_round}轮迭代优化指导

### 前轮内容回顾
{previous_content[:500] if previous_content else "无前轮内容"}...

### 逻辑性问题反馈
{logic_feedback if logic_feedback else "无特定反馈"}

### 本轮优化重点
1. **逻辑链条完整性**：确保每个事件都有明确的原因→过程→结果→影响
2. **因果关系清晰**：避免突兀的情节发展，每个转折都要有合理解释
3. **人物反应真实**：人物对事件的反应要符合常理和性格设定
4. **环境描写适度**：减少过度的环境描写，专注于剧情推进
5. **语言生活化**：使用通俗易懂的表达，避免玄学词汇和专业术语
6. **句式连贯性**：注意对话与叙述的自然衔接，避免突兀跳转

### 特别注意事项
- 禁止编造前文未提及的情节
- 禁止无原因的突发事件
- 禁止使用破折号句式和AI风格比喻
- 确保系统任务等有完整的前因后果逻辑
"""

        return analysis_summary

    @staticmethod
    def _generate_chapter_with_iterative_optimization(novel: Novel, chapter: Chapter,
                                                    book_analysis: Dict[str, Dict[str, str]],
                                                    chapter_analysis: Dict[str, Dict[str, str]],
                                                    prompt_template: str = "default",
                                                    max_iterations: int = 3,
                                                    previous_chapters: List[str] = None,
                                                    chapter_continuity_data: Dict = None,
                                                    writing_context: Dict[str, str] = None) -> str:
        """
        正确的多次迭代优化章节生成方法

        流程：第1轮生成基础内容 → 第2轮优化语言表达 → 第3轮最终打磨
        注意：每轮都是基于前一轮结果进行优化，而不是重新生成

        Args:
            novel: 小说对象
            chapter: 章节对象
            book_analysis: 整本书分析结果
            chapter_analysis: 章节分析结果
            prompt_template: 提示词模板
            max_iterations: 最大迭代次数
            previous_chapters: 前序章节内容
            chapter_continuity_data: 章节连贯性数据
            writing_context: 写作上下文

        Returns:
            优化后的章节内容
        """
        logger.info(f"开始分批次分段优化生成章节，充分利用DeepSeek R1思考能力，最大迭代次数: {max_iterations}")

        # 使用新的分批次分段优化策略
        return TestService._generate_chapter_with_batch_optimization(
            novel=novel,
            chapter=chapter,
            book_analysis=book_analysis,
            chapter_analysis=chapter_analysis,
            prompt_template=prompt_template,
            max_iterations=max_iterations,
            previous_chapters=previous_chapters,
            chapter_continuity_data=chapter_continuity_data,
            writing_context=writing_context  # 传递写作上下文
        )

    @staticmethod
    def _generate_chapter_with_batch_optimization(novel: Novel, chapter: Chapter,
                                                book_analysis: Dict[str, Dict[str, str]],
                                                chapter_analysis: Dict[str, Dict[str, str]],
                                                prompt_template: str = "default",
                                                max_iterations: int = 3,
                                                previous_chapters: List[str] = None,
                                                chapter_continuity_data: Dict = None,
                                                writing_context: Dict[str, str] = None) -> str:
        """
        真正的3轮迭代优化策略生成章节内容

        流程：
        第1轮：生成基础内容（完整章节）
        第2轮：基于第1轮结果进行语言表达优化
        第3轮：基于第2轮结果进行最终打磨

        Args:
            novel: 小说对象
            chapter: 章节对象
            book_analysis: 整本书分析结果
            chapter_analysis: 章节分析结果
            prompt_template: 提示词模板
            max_iterations: 最大迭代次数
            previous_chapters: 前序章节内容
            chapter_continuity_data: 章节连贯性数据
            writing_context: 写作上下文

        Returns:
            优化后的章节内容
        """
        logger.info("=== 🔄 真正的3轮迭代优化启动 ===")
        logger.info(f"将进行{max_iterations}轮迭代优化，每轮基于前一轮结果进行改进")

        # 第1轮：生成基础内容（完整章节）
        logger.info("=== 第1轮：生成基础内容 ===")

        current_content = TestService._execute_first_iteration_base_generation(
            novel=novel,
            chapter=chapter,
            book_analysis=book_analysis,
            chapter_analysis=chapter_analysis,
            prompt_template=prompt_template,
            previous_chapters=previous_chapters,
            chapter_continuity_data=chapter_continuity_data,
            writing_context=writing_context  # 传递智能写作上下文
        )

        # 验证第1轮结果
        if not current_content or len(current_content.strip()) < 100:
            logger.error("第1轮基础内容生成失败")
            # 检查是否为短篇小说
            novel_type = getattr(novel, 'novel_type', 'long')
            is_short_story = novel_type == 'short'
            if is_short_story:
                return "内容生成失败：基础内容生成失败"
            else:
                return f"# 第{chapter.chapter_number}章 章节生成失败：基础内容生成失败"

        logger.info(f"第1轮完成，基础内容长度: {len(current_content)}")

        # 🔧 扩写优化步骤（在分段微调之前）
        logger.info("=== 🔧 扩写优化步骤 ===")

        # 检查第1轮基础内容的字数
        base_word_count = TestService._count_words_accurately(current_content)

        # 根据小说类型设置字数要求
        novel_type = getattr(novel, 'novel_type', 'long')
        is_short_story = novel_type == 'short'

        if is_short_story:
            # 短篇小说（知乎体）字数要求：8000-12000字
            target_word_count = 10000  # 目标字数（约1万字标准）
            min_words = 8000  # 最低8000字
            max_words = 12000  # 最高12000字
        else:
            # 长篇小说章节字数要求：1500-2500字
            target_word_count = 2000  # 目标字数（1500-2500区间的中位数）
            min_words = 1500  # 最低1500字
            max_words = 2500  # 最高2500字

        logger.info(f"第1轮基础内容字数: {base_word_count}字，目标字数: {target_word_count}字")

        # 🎯 第1步：扩写优化（如果字数不足）
        if base_word_count < min_words:
            logger.info(f"字数不足({base_word_count}字 < {min_words}字)，开始扩写优化")
            expanded_content = TestService._execute_expansion_optimization(
                base_content=current_content,
                novel=novel,
                chapter=chapter,
                book_analysis=book_analysis,
                chapter_analysis=chapter_analysis,
                target_word_count=target_word_count,
                prompt_template=prompt_template
            )

            if expanded_content and len(expanded_content.strip()) > len(current_content):
                # 🔧 修复：完整提取扩写优化的所有内容，像提取最终写作结果一样
                extracted_expanded_content = TestService._extract_complete_generated_content(expanded_content, chapter.chapter_number, "扩写优化")
                current_content = extracted_expanded_content
                logger.info(f"扩写优化完成，原始长度: {len(expanded_content)}，完整提取后长度: {len(current_content)}")
                logger.info(f"扩写优化后字数: {TestService._count_words_accurately(current_content)}字")
            else:
                logger.warning("扩写优化失败，继续使用基础内容")
        else:
            logger.info(f"字数已达标({base_word_count}字 >= {min_words}字)，跳过扩写优化")

        # 🎯 第2步：简单逻辑补充验证（无论字数是否充足都要执行）
        logger.info("🔍 开始简单逻辑补充验证：无论字数是否充足，都要进行最小化逻辑检查")
        logic_enhanced_content = TestService._apply_logic_enhancement_verification(
            current_content, novel, chapter, book_analysis, chapter_analysis, prompt_template
        )

        # 如果简单逻辑补充成功，更新内容
        if logic_enhanced_content and len(logic_enhanced_content.strip()) > len(current_content) * 0.9:
            # 🔧 修复：完整提取简单逻辑补充验证的所有内容，像提取最终写作结果一样
            extracted_logic_content = TestService._extract_complete_generated_content(logic_enhanced_content, chapter.chapter_number, "简单逻辑补充验证")
            current_content = extracted_logic_content
            logger.info(f"✅ 简单逻辑补充验证完成，原始长度: {len(logic_enhanced_content)}，完整提取后长度: {len(current_content)}")
            logger.info(f"简单逻辑补充验证后字数: {TestService._count_words_accurately(current_content)}字")
        else:
            logger.info("📝 简单逻辑补充验证：内容逻辑基本合理，保持原有精品内容")

        # 🎯 第3步：智能质量检查：检查最终内容质量和字数
        actual_word_count = TestService._count_words_accurately(current_content)

        # 检查字数是否在合理范围内
        word_count_acceptable = min_words <= actual_word_count <= max_words

        # 🔧 修正后的质量判定标准：放宽判定，只要没有明显问题就直接输出
        # 检查内容质量（简化后的标准：句式滥用、对话达标、符合当代网文特征）
        sentence_style_check = not TestService._has_sentence_style_abuse(current_content)  # 检查句式滥用
        dialogue_adequate_check = TestService._check_dialogue_adequacy(current_content)  # 检查对话达标
        webnovel_style_check = TestService._check_contemporary_webnovel_compliance(current_content)  # 检查当代网文特征符合度

        # 简化后的质量判定：删除逻辑问题检查，只要没有明显问题就可以直接输出
        content_quality_good = sentence_style_check and dialogue_adequate_check and webnovel_style_check

        # 详细的质量检查日志
        logger.info(f"📊 简化后的质量检查标准:")
        logger.info(f"  - 字数检查: {actual_word_count}字 (目标{target_word_count}字, 范围{min_words}-{max_words}字) = {word_count_acceptable}")
        logger.info(f"  - 句式滥用检查: 无长句复杂句滥用 = {sentence_style_check}")
        logger.info(f"  - 对话达标检查: 对话内容达标 = {dialogue_adequate_check}")
        logger.info(f"  - 当代网文特征检查: 符合当代网文特征 = {webnovel_style_check}")
        logger.info(f"  - 🎯 简化后综合评估: 字数达标={word_count_acceptable}, 质量合格={content_quality_good}")

        # 如果质量不达标，输出具体原因
        if not content_quality_good:
            logger.warning("⚠️ 内容质量不合格，原因:")
            if not sentence_style_check:
                logger.warning("  - 存在句式滥用（长句、复杂句）")
            if not dialogue_adequate_check:
                logger.warning("  - 对话内容不达标")
            if not webnovel_style_check:
                logger.warning("  - 不符合当代网文特征")

        # 如果字数不达标，输出具体原因
        if not word_count_acceptable:
            if actual_word_count < min_words:
                logger.warning(f"⚠️ 字数不足: {actual_word_count}字 < {min_words}字")
            elif actual_word_count > max_words:
                logger.warning(f"⚠️ 字数过多: {actual_word_count}字 > {max_words}字")

        # 🎯 第4步：智能质量优化决策：只有在逻辑补充验证后不合格时才执行4段微调
        # 如果字数和质量都合格，直接作为最终输出结果
        if word_count_acceptable and content_quality_good:
            logger.info("🎉 逻辑补充验证后内容质量和字数都已合格，直接作为最终输出结果")
            logger.info("🎯 跳过4段微调，避免不必要的处理，确保最佳结果")
            return current_content

        # 只有在不合格时才执行4段微调
        logger.info("⚠️ 逻辑补充验证后内容不合格，启动4段微调进行质量提升")

        # 🔧 分段学习指导微调（质量打磨，不限制token）
        try:
            logger.info("🚀 开始分段学习指导微调，专注质量提升，不限制token使用")
            logger.info(f"📋 分段微调接收的内容字数: {TestService._count_words_accurately(current_content)}字")
            logger.info(f"📋 这是{'扩写优化后' if actual_word_count > base_word_count else '基础写作'}的内容")

            refined_content = TestService._apply_segmented_learning_guidance_refinement(
                content=current_content,
                novel=novel,
                chapter=chapter,
                book_analysis=book_analysis,
                chapter_analysis=chapter_analysis,
                prompt_template=prompt_template
            )

            # 🔧 修正验证逻辑：第4段微调的结果应该直接作为最终输出
            refined_word_count = TestService._count_words_accurately(refined_content)

            # 检查第4段微调是否成功生成了有效内容
            if refined_content and len(refined_content.strip()) > 500:
                # 🔧 修复：完整提取4段微调的所有内容，像提取最终写作结果一样
                extracted_refined_content = TestService._extract_complete_generated_content(refined_content, chapter.chapter_number, "4段微调")
                final_refined_word_count = TestService._count_words_accurately(extracted_refined_content)
                logger.info(f"✅ 分段学习指导微调成功，原始长度: {len(refined_content)}，完整提取后长度: {len(extracted_refined_content)}")
                logger.info(f"4段微调最终字数: {final_refined_word_count}字")
                logger.info("🎯 4段微调完成，输出最终结果")
                logger.info(f"📊 字数变化：扩写优化{actual_word_count}字 → 4段微调{final_refined_word_count}字")
                return extracted_refined_content  # 第4段微调的结果是最终输出结果
            else:
                logger.warning(f"⚠️ 4段微调生成内容无效或过短({refined_word_count}字)，返回扩写优化内容")
                return current_content

        except Exception as e:
            logger.error(f"分段学习指导微调失败: {str(e)}，返回原始内容")
            return current_content

        # 如果只需要1轮，直接返回
        if max_iterations <= 1:
            logger.info("只需1轮，返回基础内容")
            return current_content

        # 第2轮：语言表达优化（基于第1轮结果）
        logger.info("=== 第2轮：语言表达优化 ===")

        optimized_content = TestService._execute_second_iteration_language_optimization(
            base_content=current_content,
            novel=novel,
            chapter=chapter,
            book_analysis=book_analysis,
            chapter_analysis=chapter_analysis,
            prompt_template=prompt_template
        )

        # 验证第2轮结果，如果失败或质量下降则使用第1轮结果
        if optimized_content and len(optimized_content.strip()) >= 100:
            # 检查第2轮是否比第1轮质量更好
            second_round_word_count = TestService._count_words_accurately(optimized_content)
            second_round_quality_good = not TestService._has_obvious_quality_issues(optimized_content)

            # 比较第1轮和第2轮的质量
            first_round_word_count = TestService._count_words_accurately(current_content)
            first_round_quality_good = not TestService._has_obvious_quality_issues(current_content)

            logger.info(f"第2轮质量对比: 第1轮字数={first_round_word_count}字,质量={first_round_quality_good} vs 第2轮字数={second_round_word_count}字,质量={second_round_quality_good}")

            # 如果第2轮质量明显下降，保持使用第1轮结果
            if second_round_quality_good and (second_round_word_count >= first_round_word_count * 0.8):
                current_content = optimized_content
                logger.info(f"第2轮完成，优化内容长度: {len(current_content)}")
            else:
                logger.warning("第2轮优化质量下降或字数严重不足，继续使用第1轮结果")
        else:
            logger.warning("第2轮语言优化失败，继续使用第1轮结果")

        # 如果只需要2轮，直接返回
        if max_iterations <= 2:
            logger.info("只需2轮，返回当前内容")
            return current_content

        # 第3轮：最终打磨（基于第2轮结果）
        logger.info("=== 第3轮：最终打磨 ===")

        final_content = TestService._execute_third_iteration_final_polish(
            optimized_content=current_content,
            novel=novel,
            chapter=chapter,
            book_analysis=book_analysis,
            chapter_analysis=chapter_analysis,
            prompt_template=prompt_template
        )

        # 验证第3轮结果，如果失败或质量下降则使用第2轮结果
        if final_content and len(final_content.strip()) >= 100:
            # 检查第3轮是否比第2轮质量更好
            third_round_word_count = TestService._count_words_accurately(final_content)
            third_round_quality_good = not TestService._has_obvious_quality_issues(final_content)

            # 比较第2轮和第3轮的质量
            current_round_word_count = TestService._count_words_accurately(current_content)
            current_round_quality_good = not TestService._has_obvious_quality_issues(current_content)

            logger.info(f"第3轮质量对比: 当前轮字数={current_round_word_count}字,质量={current_round_quality_good} vs 第3轮字数={third_round_word_count}字,质量={third_round_quality_good}")

            # 如果第3轮质量明显下降，保持使用当前轮结果
            if third_round_quality_good and (third_round_word_count >= current_round_word_count * 0.8):
                current_content = final_content
                logger.info(f"第3轮完成，最终内容长度: {len(current_content)}")
            else:
                logger.warning("第3轮打磨质量下降或字数严重不足，继续使用当前轮结果")
        else:
            logger.warning("第3轮最终打磨失败，继续使用当前轮结果")

        logger.info("=== ✅ 3轮迭代优化完成 ===")
        return current_content

    @staticmethod
    def _execute_expansion_optimization(base_content: str, novel: Novel, chapter: Chapter,
                                      book_analysis: Dict[str, Dict[str, str]],
                                      chapter_analysis: Dict[str, Dict[str, str]],
                                      target_word_count: int,
                                      prompt_template: str = "default") -> str:
        """
        扩写优化：将基础内容扩写到目标字数

        这是第1轮基础写作和分段微调之间的关键步骤
        """
        logger.info("🔧 开始扩写优化，将基础内容扩写到目标字数")

        current_word_count = TestService._count_words_accurately(base_content)
        logger.info(f"当前字数: {current_word_count}字，目标字数: {target_word_count}字")

        # 获取原文样本
        original_sample = chapter.content if chapter.content else ""

        # 检查是否为短篇小说
        novel_type = getattr(novel, 'novel_type', 'long')
        is_short_story = novel_type == 'short'

        # 构建扩写优化提示词
        expansion_prompt = f"""
# 🔧 扩写优化任务：将基础内容扩写到目标字数

## ⚠️ 重要说明：这是扩写优化，不是重新写作！

### 🎯 扩写优化核心任务
**你的任务是将第1轮基础内容扩写到约{target_word_count}字，保持故事情节和风格完全不变！**

{"### 📱 知乎体短篇小说格式要求（扩写时必须严格遵循）" if is_short_story else ""}
{"#### 🔥 分段核心法则（头部盐选编辑规范）" if is_short_story else ""}
{"- **三行切割法**：单段≤3行（手机屏幕显示上限）" if is_short_story else ""}
{"- **情绪单元独立成段**：关键动作、致命台词、感官冲击独占段落" if is_short_story else ""}
{"- **空行使用逻辑**：导语→正文过渡3行，时间/场景转换2行，关键反转前后1行" if is_short_story else ""}
{"- **情绪节奏器**：每300字必须有情绪爆点段（加粗/短句/悬念）" if is_short_story else ""}
{"- **拟声词独立成段**：如`**滴——**（拟声词段）`" if is_short_story else ""}
{"- **严禁章节标题**：直接开始故事内容，无需任何章节分割" if is_short_story else ""}

## 📋 第1轮基础内容（必须在此基础上扩写）
以下是第1轮生成的基础内容（{current_word_count}字），你必须在此基础上扩写到约{target_word_count}字：

```
{base_content}
```

## 📚 原文样本深度学习（用户提供的分析素材）
**继续深度学习用户提供的原文样本，在扩写时保持语言特征：**

```
{original_sample}
```

## 🚨 四个强制执行要求（扩写时必须严格遵守，100%达标）
**在扩写优化中，以下四点必须继续100%执行：**

1. **直接套用原文样本的句式结构、语言特征、节奏、逻辑性**
   ⚠️ **重要澄清：这里的"原文样本"是指用户提供给系统分析的全部原文内容，不是基础内容！**
   - 扩写时继续深度学习和模仿用户提供的原文样本的语言风格
   - 新增内容必须完全符合原文样本的句式特征
   - 绝不复制情节，但必须复制原文样本的语言模式
   - 学习原文样本的句子长短、语气词使用、表达方式

2. **更偏向于短句、日常化通俗口语化，符合当代网文特征**
   - 扩写的内容优先使用短句，避免长句和复杂句
   - 采用日常生活化的词汇和表达，减少修辞手法
   - 避免复杂句式和书面化表达，保持通俗易懂
   - 学习原文样本的简洁表达方式
   - **符合当代网文特征**：扩写内容必须符合当代网文读者的阅读习惯和语言风格

3. **要有十分严密的逻辑性、因果联系**
   - 扩写的每个细节都必须有明确的逻辑支撑
   - 确保新增内容与原有内容逻辑完全吻合
   - 强化事件的前因后果关系
   - 避免突兀的情节转折

4. **特别注意生成内容章节之间的联系与继承**
   - 扩写内容必须与前序章节完全连贯
   - 绝对不能改变已确定的人物设定和情节发展
   - 保持故事的连贯性和一致性
   - 学习原文样本的章节衔接技巧

## ✨ 扩写优化具体要求

### 1. 扩写策略（关注逻辑性、语言通俗化、当代网文特征）
- **逻辑性扩写**：增加事件的前因后果说明，让逻辑更加严密
- **语言通俗化扩写**：将简单描述扩展为通俗易懂的详细表达
- **句式结构扩写**：学习原文样本的句式，增加符合原文风格的表达
- **节奏控制扩写**：适当增加对话和叙述的交替，保持阅读节奏
- **当代网文特征扩写**：确保扩写内容符合当代网文的语言特点和表达习惯

### 2. 扩写重点（不要扩写人物心理、环境等细节描写）
- **强化逻辑连接**：增加"因为"、"所以"、"于是"等逻辑连接词
- **通俗化表达**：将抽象描述改为具体通俗的表达
- **短句扩展**：将单句扩展为多个短句，学习原文样本的节奏
- **对话丰富**：适当增加符合人物性格的对话内容

### 3. 扩写禁止事项
- ❌ 不要扩写人物心理活动的细节描写
- ❌ 不要扩写环境景物的细节描写
- ❌ 不要添加过多修辞手法
- ❌ 不要改变基础内容的故事情节
- ❌ 不要修改人物名称和关系设定
- ❌ 不要使用复杂的书面化表达

### 4. 扩写目标
- **目标字数**：约{target_word_count}字（当前{current_word_count}字，需增加约{target_word_count - current_word_count}字）
- **保持质量**：扩写后的内容质量不能下降
- **风格一致**：完全保持原文样本的语言风格
- **逻辑严密**：增强内容的逻辑性和因果关系

## 📝 扩写优化要求
请基于第1轮的基础内容，进行扩写优化，
生成约{target_word_count}字的完整章节内容。
**必须严格执行四个强制要求，确保100%达标！**
**必须深度学习原文样本，而不是学习基础内容！**

⚠️ 重要提醒：扩写时重点关注逻辑性、语言通俗化、句式结构、节奏把握，不要扩写心理和环境细节！

请开始扩写优化：
"""

        try:
            # 调用API进行扩写优化
            result = TestService._generate_chapter_content_original(
                prompt=expansion_prompt,
                prompt_template=prompt_template
            )

            if result and len(result.strip()) > len(base_content):
                # 🔧 修复：完整提取扩写优化的所有内容，像提取最终写作结果一样
                extracted_result = TestService._extract_complete_generated_content(result, chapter.chapter_number, "扩写优化API调用")
                expanded_word_count = TestService._count_words_accurately(extracted_result)
                logger.info(f"扩写优化成功，原始API返回长度: {len(result)}，完整提取后长度: {len(extracted_result)}")
                logger.info(f"扩写优化字数变化：{current_word_count}字 → {expanded_word_count}字")
                return extracted_result
            else:
                logger.warning("扩写优化失败：内容未增加或为空")
                return ""

        except Exception as e:
            logger.error(f"扩写优化异常: {str(e)}")
            return ""

    @staticmethod
    def _execute_first_iteration_base_generation(novel: Novel, chapter: Chapter,
                                               book_analysis: Dict[str, Dict[str, str]],
                                               chapter_analysis: Dict[str, Dict[str, str]],
                                               prompt_template: str = "default",
                                               previous_chapters: List[str] = None,
                                               chapter_continuity_data: Dict = None,
                                               writing_context: Dict[str, str] = None) -> str:
        """
        第1轮：生成精品基础内容（完整章节）

        从第一轮开始就要生成精品内容，严格执行四个强制要求
        """
        logger.info("🎯 第1轮开始：生成精品基础章节内容")

        # 获取原文样本（用户提供给系统分析的全部原文内容）
        original_sample = chapter.content if chapter.content else ""

        # 构建第1轮精品生成提示词
        first_round_prompt = f"""
# 🎯 第1轮任务：生成精品基础内容

## ⚠️ 重要说明：从第1轮开始就要生成精品内容！

### 🚨 四个强制执行要求（第1轮必须严格遵守，100%达标）
**从第1轮开始，以下四点必须100%执行，不得有任何妥协：**

1. **直接套用原文样本的句式结构、语言特征、节奏、逻辑性**
   ⚠️ **重要澄清：这里的"原文样本"是指用户提供给系统分析的全部原文内容，不是批次生成的内容结果！**
   - 必须深度学习用户提供的原文样本的语言风格
   - 严格模仿原文样本的句式模式、表达习惯、语言节奏
   - 禁止复制原文情节，但必须完全复制原文样本的语言特征
   - 学习原文样本的句子长短、语气词使用、表达方式

2. **更偏向于短句、日常化通俗口语化，符合当代网文特征**
   - 优先使用短句，避免长句和复杂句
   - 采用日常生活化的词汇和表达，减少修辞手法
   - 避免复杂句式和书面化表达，保持通俗易懂
   - 学习原文样本的简洁表达方式
   - **符合当代网文特征**：自然融入当代网文的语言特点和表达习惯，让AI自行发现和运用当代网文特征

3. **要有十分严密的逻辑性、因果联系（精品标准）**
   - 每个情节发展都必须有明确的逻辑支撑
   - 确保事件的前因后果关系清晰
   - 人物行为必须符合逻辑和性格设定
   - 避免突兀的情节转折
   - **精品逻辑要求**：逻辑链条必须严密完整，无任何漏洞

4. **特别注意生成内容章节之间的联系与继承（精品标准）**
   - 必须与前序章节完全连贯
   - 绝对不能改变已确定的人物设定
   - 保持故事的连贯性和一致性
   - 继承前章的情节发展脉络
   - **精品连贯要求**：章节衔接必须天衣无缝，无任何矛盾

## 👥 人物名称一致性要求（重要优化）
**严格确保人物名称在全文中的绝对一致性：**
- **统一命名原则**：一旦确定人物名称，全文必须保持绝对一致，不得随意变更
- **穿越文特殊处理**：
  - 主角穿越后统一使用穿越后的名字，避免前后名称混乱
  - 示例：主角穿越前叫"林小满"，穿越后叫"姜若璃"，全文统一使用"姜若璃"
  - 可设定为"穿越到同名同姓的人身上"，保持逻辑合理性
  - **穿越逻辑合理性**：必须解释为什么穿越、穿越到谁身上、如何适应新身份
- **重生文处理**：统一使用重生后的身份名称，解释重生原因和记忆保留情况
- **其他题材**：确保人物名称从始至终保持一致，避免称呼混乱
- **身份转换逻辑**：任何身份变化都必须有合理的前因后果和适应过程

## 🔍 逻辑性与因果性强化要求（核心优化）
**任何设定、道具、事件、人物行为都必须有明确的逻辑解释：**
- **设定逻辑性**：
  - 穿越/重生原因必须合理（意外、系统、特殊体质等）
  - 世界观设定必须自洽（修仙世界的等级制度、现代都市的社会结构等）
  - 时代背景必须一致（古代不能有现代科技，现代不能有古代迷信）
- **道具逻辑性**：
  - 任何道具的出现都要解释来源、用途和对主角的作用
  - 特殊物品必须符合世界观设定（修仙世界的法器、现代都市的科技产品）
  - 道具能力必须有明确限制，不能过于逆天无解释
- **事件因果性**：
  - 主角面临的每个情况都要有前因后果
  - 人物的每个决定都要有合理的动机和背景
  - 事件发展必须符合因果逻辑链条，避免突兀转折
- **人物行为逻辑**：
  - 人物性格必须前后一致，行为符合其身份背景
  - 对话内容必须符合人物的知识水平和时代背景
  - 情感变化必须有合理的触发事件和发展过程

## 🏮 题材词汇严格控制（重要优化）
**不同题材的词汇使用规范：**
- **修仙/玄幻题材允许词汇**：法器、灵宝、丹药、功法、灵石、阵法、符箓、洞府、宗门、修为、筑基、金丹、元婴、化神、炼虚、合体、大乘、渡劫、飞升、灵气、真元、神识、元神、道心、天劫、仙缘、道法、灵根
- **古代背景允许词汇**：府邸、轿子、马车、书信、银两、客栈、酒楼、衙门、县令、知府
- **现代都市允许词汇**：手机、电脑、汽车、地铁、公司、写字楼、咖啡厅、商场、银行卡
- **严禁跨时代词汇**：
  - 古代/修仙题材禁用：科技、电脑、手机、网络、系统、程序、数据、芯片、激光、雷达、机器人、人工智能、虚拟现实、基因、DNA、量子、核能、太空、卫星、导弹、飞机、汽车、电视、收音机、摄像头、监控
  - 现代题材禁用：法器、灵宝、丹药、功法、灵石、阵法、符箓、洞府、宗门、修为等修仙词汇
- **语言时代一致性**：
  - 古代背景：使用文言文或半文言文表达，称谓用"公子、小姐、奴婢"等
  - 现代背景：使用现代汉语表达，称谓用"先生、女士、同学"等
  - 修仙背景：可混用古代表达和修仙专用词汇

## 📚 原文样本深度学习（用户提供的分析素材）
**以下是用户提供给系统分析的原文样本，必须深度学习其语言特征：**

```
{original_sample}
```

### 🔍 原文样本学习要点（必须全部掌握）
1. **语言风格**：仔细分析原文的用词习惯、表达方式、语言节奏
2. **句式特征**：学习原文的句子长短搭配、句式变化规律
3. **叙述技巧**：掌握原文的叙述视角、描写方法、对话处理
4. **节奏控制**：理解原文的情节推进速度、紧张与舒缓交替
5. **逻辑性**：学习原文的事件合理性、因果关系处理

## 🎨 第1轮当代网文特征要求

### 📝 当代网文特征
- **自然融入当代网文特征**：让AI自行发现和运用当代网文的语言特点、表达习惯和叙述风格
- **避免过时表达**：减少使用过于书面化或古典的表达方式
- **保持现代感**：使用符合当代读者阅读习惯的语言风格

## 🎯 第1轮生成目标：精品基础内容

### 核心任务
生成一个高质量的完整章节，包含：
1. 正确的章节标题（第{chapter.chapter_number}章）
2. 完整且逻辑严密的故事情节
3. 符合目标的字数长度（约1500字）
4. 完全模仿原文样本的语言表达

### 第1轮质量标准（精品要求）
- **故事完整性**：情节完整，结构清晰，有开头、发展、高潮、结尾
- **逻辑严密性**：每个事件都有明确的前因后果，无逻辑漏洞
- **语言精品化**：完全模仿原文样本的语言风格和表达方式
- **人物真实性**：人物行为符合性格设定，反应真实自然
- **连贯性**：与前序章节完美衔接，无矛盾之处

### 🎨 创作要求（精品标准）
1. **全新故事**：创造与原文完全不同的故事情节
2. **全新人物**：使用全新的人物名称和关系设定
3. **语言传承**：完美继承原文样本的语言特色
4. **结构借鉴**：学习原文样本的章节结构安排
5. **风格统一**：整章保持与原文样本一致的风格

### 📋 第1轮执行检查清单
在生成内容前，必须确认：
□ 已深度学习原文样本的语言特征
□ 已掌握原文样本的句式规律
□ 已理解原文样本的叙述技巧
□ 已分析原文样本的逻辑处理方式
□ 已准备创造全新的故事内容
□ **已确认符合当代网文特征**
□ **已确认避免过时表达方式**

## 🎯 第1轮质量自检标准
**生成内容后，请自检以下标准：**

### ✅ 语言学习效果检查
- 是否完美模仿了原文样本的语言特征？
- 是否学会了原文样本的句式规律？
- 是否掌握了原文样本的表达习惯？

### ✅ 当代网文特征检查
- 是否自然融入了当代网文特征？
- 是否符合当代网文读者的阅读习惯？
- 是否避免了过时的表达方式？

### ✅ 逻辑性检查
- 情节发展是否有明确的逻辑支撑？
- 事件的前因后果关系是否清晰？

## 📊 写作标准与验证标准说明
**重要说明：以下是双重标准体系**

### 🎯 写作指导标准（当前使用）
- **符合当代网文特征**：自然融入当代网文的语言特点和表达习惯
- **避免过时表达**：减少使用过于书面化或古典的表达方式
- **保持现代感**：使用符合当代读者阅读习惯的语言风格

### ✅ 实际验证标准（进一步放宽）
- **短句比例≥12%**：基础验证要求（保持不变）
- **对话标记≥2个**：基本对话要求（保持不变）
- **当代网文特征**：放宽要求，只要体现基本的当代网文特征即可
- **避免明显过时元素**：基本现代化要求（保持不变）

**策略说明**：用合理标准指导生成优质内容，用宽松标准确保验证通过，既保证质量又保证通过率！

## 🚨 最终提醒
**第1轮就要生成优质内容！四个强制要求+当代网文特征必须达标！**
**原文样本是学习的宝库，必须深度模仿其语言特征！**
**合理标准指导生成，确保生成优质内容！**

请开始第1轮精品内容生成：
"""

        try:
            # 调用API生成精品基础内容
            result = TestService._generate_chapter_content_original(
                prompt=first_round_prompt,
                prompt_template=prompt_template
            )

            if result and len(result.strip()) > 100:
                # 🔧 修复：完整提取第1轮生成的所有内容，像提取最终写作结果一样
                extracted_content = TestService._extract_complete_generated_content(result, chapter.chapter_number, "第1轮基础内容")
                logger.info(f"第1轮精品生成成功，原始长度: {len(result)}，完整提取后长度: {len(extracted_content)}")
                logger.info(f"第1轮基础内容字数: {TestService._count_words_accurately(extracted_content)}字")
                return extracted_content
            else:
                logger.error("第1轮精品生成失败：内容过短或为空")
                return ""

        except Exception as e:
            logger.error(f"第1轮精品生成异常: {str(e)}")
            return ""

    @staticmethod
    def _execute_second_iteration_language_optimization(base_content: str, novel: Novel, chapter: Chapter,
                                                      book_analysis: Dict[str, Dict[str, str]],
                                                      chapter_analysis: Dict[str, Dict[str, str]],
                                                      prompt_template: str = "default") -> str:
        """
        第2轮：语言表达精炼优化（基于第1轮精品结果）

        在第1轮精品基础上进一步精炼语言表达，提升文学品质
        """
        logger.info("🔧 第2轮开始：基于第1轮精品结果进行语言表达精炼优化")

        # 获取原文样本（用户提供给系统分析的全部原文内容）
        original_sample = chapter.content if chapter.content else ""

        # 分析第1轮内容的优化空间
        content_issues = TestService._analyze_content_for_optimization(base_content)

        # 构建第2轮精炼优化提示词
        optimization_prompt = f"""
# 🔧 第2轮任务：语言表达精炼优化

## ⚠️ 重要说明：基于第1轮精品结果进行精炼优化，不是重新写作！

### 🎯 第2轮核心任务
**你的任务是对第1轮的精品内容进行语言表达精炼优化，保持故事情节完全不变！**

## 📋 第1轮精品内容（必须在此基础上精炼优化）
以下是第1轮生成的精品内容，你必须在此基础上进行语言精炼优化：

```
{base_content}
```

## 📚 原文样本深度学习（用户提供的分析素材）
**继续深度学习用户提供的原文样本，进一步精炼语言特征：**

```
{original_sample}
```

## 🚨 四个强制执行要求（第2轮必须继续严格遵守，100%达标）
**在第2轮语言精炼优化中，以下四点必须继续100%执行：**

1. **直接套用原文样本的句式结构、语言特征、节奏、逻辑性**
   ⚠️ **重要澄清：这里的"原文样本"是指用户提供给系统分析的全部原文内容，不是第1轮生成的内容！**
   - 继续深度学习和模仿用户提供的原文样本的语言风格
   - 在精炼优化时更加贴近原文样本的句式特征
   - 绝不复制情节，但必须更完美地复制原文样本的语言模式
   - 进一步学习原文样本的句子长短、语气词使用、表达方式

2. **更偏向于短句、日常化通俗口语化**
   - 将第1轮中的复杂句子进一步拆分为简单短句
   - 精炼表达使其更加通俗易懂，更贴近原文样本风格
   - 避免因为优化而使用复杂表达，保持原文样本的简洁性
   - 学习原文样本的日常化表达方式

3. **要有十分严密的逻辑性、因果联系**
   - 精炼优化的每个细节都必须有更强的逻辑支撑
   - 确保优化后的逻辑比第1轮更加清晰严密
   - 修复任何可能的逻辑漏洞，学习原文样本的逻辑处理
   - 强化事件的前因后果关系

4. **特别注意生成内容章节之间的联系与继承**
   - 精炼优化内容必须与前序章节更加完美连贯
   - 绝对不能改变已确定的人物设定和情节发展
   - 保持并强化故事的连贯性和一致性
   - 学习原文样本的章节衔接技巧

## 👥 人物名称一致性要求（第2轮继续严格执行）
**在精炼优化过程中严格确保人物名称的绝对一致性：**
- **统一命名原则**：绝对不能改变第1轮确定的人物名称
- **穿越文处理**：继续统一使用穿越后的名字，不得出现前后名称混乱
- **重生文处理**：继续统一使用重生后的身份名称
- **其他题材**：确保人物名称与第1轮完全一致
- **身份转换逻辑**：继续保持第1轮确定的身份转换逻辑和适应过程

## 🔍 逻辑性与因果性强化要求（第2轮继续严格执行）
**在精炼优化过程中继续强化逻辑性：**
- **设定逻辑性**：不能改变第1轮确定的世界观设定和时代背景
- **道具逻辑性**：保持第1轮确定的道具来源和用途解释
- **事件因果性**：强化第1轮内容的因果关系，消除任何逻辑漏洞
- **人物行为逻辑**：确保人物行为更加符合其身份背景和性格设定

## 🏮 题材词汇严格控制（第2轮继续严格执行）
**不同题材的词汇使用规范（精炼优化时同样适用）：**
- **修仙/玄幻题材允许词汇**：法器、灵宝、丹药、功法、灵石、阵法、符箓、洞府、宗门、修为、筑基、金丹、元婴、化神、炼虚、合体、大乘、渡劫、飞升、灵气、真元、神识、元神、道心、天劫、仙缘、道法、灵根
- **古代背景允许词汇**：府邸、轿子、马车、书信、银两、客栈、酒楼、衙门、县令、知府
- **现代都市允许词汇**：手机、电脑、汽车、地铁、公司、写字楼、咖啡厅、商场、银行卡
- **严禁跨时代词汇**：精炼优化时绝对不能引入任何不符合题材的词汇
- **语言时代一致性**：继续保持第1轮确定的语言风格和时代特征

## ✨ 第2轮具体精炼优化要求

### 1. 语言表达精炼
- **句式流畅度提升**：进一步改善句子间的连接，消除任何突兀跳转
- **词汇精准选择**：使用更准确、生动且贴近原文样本风格的词汇
- **表达自然度增强**：让语言更加自然流畅，更贴近原文样本的表达习惯
- **语言风格统一强化**：确保整章语言风格与原文样本高度一致

### 2. 对话精炼优化
- **对话自然度提升**：让人物对话更加真实自然，学习原文样本的对话风格
- **语言个性化强化**：确保不同人物有更鲜明的说话风格，参考原文样本
- **对话与叙述衔接优化**：进一步优化对话前后的过渡，学习原文样本技巧

### 3. 叙述精炼优化
- **叙述节奏精调**：调整叙述的快慢节奏，更贴近原文样本的节奏感
- **描写精准度提升**：让描写更加精准有效，学习原文样本的描写技巧
- **情感表达强化**：增强情感的表达力度，学习原文样本的情感处理

### 4. 发现的优化空间
{TestService._format_content_issues(content_issues)}

## 🚫 第2轮严格禁止事项
1. 绝对不要改变第1轮确定的故事情节
2. 绝对不要修改人物名称和关系设定
3. 绝对不要添加新的主要情节线
4. 绝对不要改变章节的整体结构
5. **绝对不能违反四个强制要求**
6. **绝对不能偏离原文样本的语言特征**

## 📋 第2轮精炼检查清单
在输出前，必须确认：
□ 是否进一步贴近了原文样本的语言特征？
□ 是否将复杂句子进一步拆分为短句？
□ 是否强化了逻辑性和因果关系？
□ 是否保持了与前序章节的完美连贯？
□ 是否保持了第1轮确定的所有情节内容？

## 📝 第2轮精炼优化要求
请基于第1轮的精品内容，进行语言表达精炼优化，
生成语言更加贴近原文样本、表达更加精炼流畅的章节内容。
**必须严格执行四个强制要求，确保100%达标！**
**必须深度学习原文样本，而不是学习第1轮生成的内容！**

⚠️ 重要提醒：四个强制要求在第2轮中同样是不可妥协的底线！原文样本是学习的唯一标准！

请开始第2轮的语言表达精炼优化：
"""

        try:
            # 调用API进行语言精炼优化
            result = TestService._generate_chapter_content_original(
                prompt=optimization_prompt,
                prompt_template=prompt_template
            )

            if result and len(result.strip()) > 100:
                logger.info(f"第2轮精炼优化成功，内容长度: {len(result)}")
                return result
            else:
                logger.warning("第2轮精炼优化失败：内容过短或为空")
                return ""

        except Exception as e:
            logger.error(f"第2轮精炼优化异常: {str(e)}")
            return ""

    @staticmethod
    def _execute_third_iteration_final_polish(optimized_content: str, novel: Novel, chapter: Chapter,
                                            book_analysis: Dict[str, Dict[str, str]],
                                            chapter_analysis: Dict[str, Dict[str, str]],
                                            prompt_template: str = "default") -> str:
        """
        第3轮：最终完美打磨（基于第2轮精炼结果）

        这一轮进行最终的完美打磨，确保达到出版级别的质量标准
        """
        logger.info("✨ 第3轮开始：基于第2轮精炼结果进行最终完美打磨")

        # 获取原文样本（用户提供给系统分析的全部原文内容）
        original_sample = chapter.content if chapter.content else ""

        # 检查第2轮内容的质量
        quality_issues = TestService._analyze_content_quality_issues(optimized_content)

        # 构建第3轮完美打磨提示词
        polish_prompt = f"""
# ✨ 第3轮任务：最终完美打磨

## ⚠️ 重要说明：基于第2轮精炼结果进行最终完美打磨，确保出版级别质量！

### 🎯 第3轮核心任务
**你的任务是对第2轮的精炼内容进行最终完美打磨，确保达到出版级别的最高质量！**

## 📋 第2轮精炼内容（必须在此基础上完美打磨）
以下是第2轮精炼的内容，你必须在此基础上进行最终完美打磨：

```
{optimized_content}
```

## 📚 原文样本深度学习（用户提供的分析素材）
**最终深度学习用户提供的原文样本，确保完美模仿其语言特征：**

```
{original_sample}
```

## 🚨 四个强制执行要求（第3轮最终检查，必须100%完美达标）
**在第3轮最终完美打磨中，以下四点必须达到完美执行：**

1. **直接套用原文样本的句式结构、语言特征、节奏、逻辑性**
   ⚠️ **重要澄清：这里的"原文样本"是指用户提供给系统分析的全部原文内容，不是前两轮生成的内容！**
   - 最终检查：是否完美模仿了用户提供的原文样本的语言风格
   - 完美调整：确保每个句子都完美符合原文样本特征
   - 绝对禁止：任何偏离原文样本语言模式的表达
   - 最终学习：原文样本的句子长短、语气词使用、表达方式

2. **更偏向于短句、日常化通俗口语化**
   - 最终检查：是否所有句子都简洁明了，符合原文样本风格
   - 完美调整：将任何复杂句子改为短句，学习原文样本的简洁性
   - 绝对禁止：文绉绉或过于书面化的表达
   - 最终学习：原文样本的日常化表达方式

3. **要有十分严密的逻辑性、因果联系**
   - 最终检查：每个情节是否都有清晰的因果关系
   - 完美调整：修复任何逻辑漏洞或矛盾，学习原文样本的逻辑处理
   - 绝对禁止：任何突兀或缺乏逻辑的情节
   - 最终学习：原文样本的事件合理性和因果关系

4. **特别注意生成内容章节之间的联系与继承**
   - 最终检查：是否与前序章节完美连贯
   - 完美调整：确保人物设定和情节发展完全一致
   - 绝对禁止：任何与前文矛盾的内容
   - 最终学习：原文样本的章节衔接技巧

## ✨ 第3轮具体完美打磨要求

### 1. 最终完美质量检查
1. **逻辑一致性完美检查**：确保情节发展逻辑完美合理，无任何矛盾
2. **人物一致性完美验证**：确保人物性格、对话风格前后完全一致
3. **语言流畅性完美优化**：消除语言表达中的任何生硬之处
4. **细节完美完善**：补充必要的细节，删除任何冗余内容
5. **四要求完美达标检查**：确保四个强制要求100%完美达标

### 2. 发现的质量问题
{TestService._format_quality_issues(quality_issues)}

### 3. 最终完美打磨要求
1. **保持内容完美完整性**：不要删除任何重要情节
2. **优化表达方式至完美**：让语言更加自然流畅，完美贴近原文样本
3. **完善细节描写至完美**：适当增加必要的细节，学习原文样本技巧
4. **确保逻辑完美严密**：修复任何逻辑漏洞，达到原文样本水准
5. **四要求完美执行**：确保四个强制要求100%完美达标

### 4. 出版级别质量标准
- **语言精美度**：达到出版小说的语言精美标准
- **情节完整度**：确保情节发展完整自然，无任何缺陷
- **人物真实度**：人物形象鲜明，行为逻辑完全合理
- **阅读流畅度**：阅读体验流畅自然，无任何阻滞感
- **风格一致度**：与原文样本风格完全一致

## 🚫 第3轮严格禁止事项
1. 绝对不要改变已确定的情节结构
2. 绝对不要修改人物名称和基本设定
3. 绝对不要添加新的主要情节线
4. 绝对不要使用过于复杂的表达
5. **绝对不能违反四个强制要求**
6. **绝对不能偏离原文样本的语言特征**

## 🔍 最终完美检查清单
在输出前，必须逐一完美检查：
□ 是否完美模仿了原文样本的句式和语言特征？
□ 是否全部使用短句和通俗口语化表达？
□ 是否每个情节都有严密的逻辑和因果关系？
□ 是否与前序章节完全连贯一致？
□ 是否达到了出版级别的质量标准？

## 📝 第3轮完美打磨要求
请基于第2轮的精炼内容，进行最终的完美打磨，
生成出版级别的完美章节内容。
**必须严格执行四个强制要求，确保100%完美达标！**
**必须深度学习原文样本，而不是学习前两轮生成的内容！**

⚠️ 最终警告：四个强制要求是质量底线，任何一项不完美达标都不能输出！
⚠️ 重要提醒：原文样本是学习的唯一标准，必须完美模仿其语言特征！

请开始第3轮的最终完美打磨：
"""

        try:
            # 调用API进行最终完美打磨
            result = TestService._generate_chapter_content_original(
                prompt=polish_prompt,
                prompt_template=prompt_template
            )

            if result and len(result.strip()) > 100:
                logger.info(f"第3轮完美打磨成功，内容长度: {len(result)}")
                return result
            else:
                logger.warning("第3轮完美打磨失败：内容过短或为空")
                return ""

        except Exception as e:
            logger.error(f"第3轮完美打磨异常: {str(e)}")
            return ""

    @staticmethod
    def _analyze_content_for_optimization(content: str) -> List[str]:
        """分析内容中需要优化的问题"""
        issues = []

        # 检查语言表达问题
        if content.count('。') < len(content) / 50:
            issues.append("句子过长，需要拆分为短句")

        if content.count('"') < len(content) / 100:
            issues.append("对话偏少，可以增加人物互动")

        if re.search(r'[，。]{2,}', content):
            issues.append("存在标点符号重复，需要修正")

        if re.search(r'[\u4e00-\u9fa5]{20,}', content):
            issues.append("存在过长的句子，建议拆分")

        return issues

    @staticmethod
    def _format_content_issues(issues: List[str]) -> str:
        """格式化内容问题"""
        if not issues:
            return "未发现明显问题，内容整体良好。"

        formatted = "发现以下需要优化的问题：\n"
        for i, issue in enumerate(issues, 1):
            formatted += f"{i}. {issue}\n"
        return formatted

    @staticmethod
    def _analyze_content_quality_issues(content: str) -> List[str]:
        """分析内容质量问题"""
        issues = []

        # 检查逻辑连贯性
        if content.count('突然') > 2:
            issues.append("存在过多突兀转折，需要改善逻辑连贯性")

        # 检查人物一致性
        if re.search(r'["""].*?["""]', content):
            dialogue_count = len(re.findall(r'["""].*?["""]', content))
            if dialogue_count > 0:
                # 简单检查对话是否有明显的风格不一致
                pass

        # 检查语言流畅性
        if re.search(r'[\u4e00-\u9fa5]{30,}', content):
            issues.append("存在过长句子，影响阅读流畅性")

        # 检查细节完善度
        if content.count('，') < len(content) / 100:
            issues.append("句子结构过于简单，可以适当增加细节")

        return issues

    @staticmethod
    def _apply_segmented_learning_guidance_refinement(content: str, novel: Novel, chapter: Chapter,
                                                     book_analysis: Dict[str, Dict[str, str]],
                                                     chapter_analysis: Dict[str, Dict[str, str]],
                                                     prompt_template: str = "default") -> str:
        """
        分段应用学习指导原则进行内容微调（真正的分段处理）

        将学习指导分成4个独立的API调用，每次只处理一个重点，确保AI充分理解和执行

        Args:
            content: 待微调的内容
            novel: 小说对象
            chapter: 章节对象
            book_analysis: 整本书分析结果
            chapter_analysis: 章节分析结果
            prompt_template: 提示词模板

        Returns:
            微调后的内容
        """
        logger.info("🔧 开始真正的分段学习指导微调，4个独立API调用确保AI充分执行")

        # 检查内容是否需要微调
        if not content or len(content.strip()) < 500:
            logger.warning("内容过短，跳过学习指导微调")
            return content

        # 获取原文样本
        original_sample = chapter.content if chapter.content else ""
        current_content = content

        try:
            # 第一段：核心学习原则微调
            logger.info(f"🎯 第1段微调：核心学习原则，输入内容字数: {TestService._count_words_accurately(current_content)}字")
            segment1_result = TestService._apply_core_learning_principles_segment(
                current_content, original_sample, book_analysis, prompt_template
            )
            # 验证第1段微调结果
            if segment1_result and len(segment1_result.strip()) > len(current_content) * 0.7:
                current_content = segment1_result
                logger.info(f"✅ 第1段微调成功，输出内容字数: {TestService._count_words_accurately(current_content)}字")
            else:
                logger.warning(f"⚠️ 第1段微调结果不理想，保持原内容，字数: {TestService._count_words_accurately(current_content)}字")

            # 第二段：风格传承微调
            logger.info(f"🎨 第2段微调：风格传承，输入内容字数: {TestService._count_words_accurately(current_content)}字")
            segment2_result = TestService._apply_style_inheritance_segment(
                current_content, original_sample, book_analysis, prompt_template
            )
            # 验证第2段微调结果
            if segment2_result and len(segment2_result.strip()) > len(current_content) * 0.7:
                current_content = segment2_result
                logger.info(f"✅ 第2段微调成功，输出内容字数: {TestService._count_words_accurately(current_content)}字")
            else:
                logger.warning(f"⚠️ 第2段微调结果不理想，保持第1段结果，字数: {TestService._count_words_accurately(current_content)}字")
            logger.info(f"✅ 第2段微调完成，输出内容字数: {TestService._count_words_accurately(current_content)}字")

            # 第三段：质量标准微调
            logger.info(f"⭐ 第3段微调：质量标准，输入内容字数: {TestService._count_words_accurately(current_content)}字")
            segment3_result = TestService._apply_quality_standards_segment(
                current_content, prompt_template
            )
            # 验证第3段微调结果
            if segment3_result and len(segment3_result.strip()) > len(current_content) * 0.7:
                current_content = segment3_result
                logger.info(f"✅ 第3段微调成功，输出内容字数: {TestService._count_words_accurately(current_content)}字")
            else:
                logger.warning(f"⚠️ 第3段微调结果不理想，保持第2段结果，字数: {TestService._count_words_accurately(current_content)}字")

            # 第四段：禁止事项检查微调
            logger.info(f"🚫 第4段微调：禁止事项检查，输入内容字数: {TestService._count_words_accurately(current_content)}字")
            segment4_result = TestService._apply_prohibition_check_segment(
                current_content, original_sample, prompt_template
            )
            # 验证第4段微调结果
            if segment4_result and len(segment4_result.strip()) > len(current_content) * 0.7:
                current_content = segment4_result
                logger.info(f"✅ 第4段微调成功，输出内容字数: {TestService._count_words_accurately(current_content)}字")
            else:
                logger.warning(f"⚠️ 第4段微调结果不理想，保持第3段结果，字数: {TestService._count_words_accurately(current_content)}字")

            logger.info(f"✅ 分段学习指导微调完成，最终内容长度: {len(current_content)}")
            return current_content

        except Exception as e:
            logger.error(f"分段学习指导微调失败: {str(e)}，返回原始内容")
            return content

    @staticmethod
    def _apply_core_learning_principles_segment(content: str, original_sample: str,
                                              book_analysis: Dict[str, Dict[str, str]],
                                              prompt_template: str) -> str:
        """
        第1段：核心学习原则微调（短提示词，专注核心）
        """
        core_prompt = f"""
# 🎯 第1段微调：核心学习原则

## 当前内容
```
{content}
```

## 📚 核心学习要求
1. **三层学习体系**：
   - 确保语言风格完全模仿原文特征
   - 确保创作逻辑符合原文思维模式
   - 确保内容体现分析结果特色

2. **学习与创新平衡**：
   - ✅ 学习：句子结构、语言风格、叙述技巧
   - ✅ 创新：故事情节、人物关系、事件发展
   - ❌ 禁止：复制原文情节、事件、对话

## 📖 原文样本参考
```
{original_sample[:600] if original_sample else "无原文样本"}
```

## 🎯 微调任务
重点优化语言风格，确保完全模仿原文的表达习惯、句式特点、语言节奏。
保持情节不变，只优化语言表达。

请输出优化后的完整内容：
"""

        try:
            api_client = TestService._get_api_client()
            response = api_client.analyze_text(
                text=core_prompt,
                analysis_type="core_learning_refinement",
                max_tokens=15000,  # 增加token限制，确保深度优化
                prompt_template=prompt_template,
                temperature=0.4  # 稍微提高创造性，让优化更明显
            )

            if response and isinstance(response, dict):
                refined_content = response.get("content", "") or response.get("analysis_result", "")
                if refined_content and len(refined_content.strip()) > len(content) * 0.7:
                    logger.info("✅ 第1段核心学习原则微调成功")
                    return refined_content.strip()

        except Exception as e:
            logger.error(f"第1段核心学习原则微调失败: {str(e)}")

        # 🔧 修复：没有获取到有效结果时，返回原始内容，避免内容丢失
        logger.warning("第1段核心学习原则微调未获取到有效结果，返回原始内容")
        return content

    @staticmethod
    def _apply_style_inheritance_segment(content: str, original_sample: str,
                                       book_analysis: Dict[str, Dict[str, str]],
                                       prompt_template: str) -> str:
        """
        第2段：风格传承微调（短提示词，专注风格）
        """
        language_style = book_analysis.get("language_style", {}).get("content", "")

        style_prompt = f"""
# 🎨 第2段微调：风格传承

## 当前内容
```
{content}
```

## 🔄 风格传承要求
1. **语言风格分析参考**：
{language_style[:400] if language_style else "无语言风格分析"}

2. **传承要点**：
   - 表达习惯：学习原文用词习惯、语气特点
   - 语言节奏：保持原文句子长短搭配、节奏感
   - 叙述技巧：模仿原文叙述视角、描写方法

## 📖 原文样本风格参考
```
{original_sample[:600] if original_sample else "无原文样本"}
```

## 🎯 微调任务
重点优化句式风格、用词习惯、语言节奏，使其更贴近原文特色。
保持内容情节不变，只优化风格表达。

请输出风格优化后的完整内容：
"""

        try:
            api_client = TestService._get_api_client()
            response = api_client.analyze_text(
                text=style_prompt,
                analysis_type="style_inheritance_refinement",
                max_tokens=15000,  # 增加token限制，确保深度优化
                prompt_template=prompt_template,
                temperature=0.4  # 稍微提高创造性，让风格优化更明显
            )

            if response and isinstance(response, dict):
                refined_content = response.get("content", "") or response.get("analysis_result", "")
                if refined_content and len(refined_content.strip()) > len(content) * 0.7:
                    logger.info("✅ 第2段风格传承微调成功")
                    return refined_content.strip()

        except Exception as e:
            logger.error(f"第2段风格传承微调失败: {str(e)}")

        # 🔧 修复：没有获取到有效结果时，返回原始内容，避免内容丢失
        logger.warning("第2段风格传承微调未获取到有效结果，返回原始内容")
        return content

    @staticmethod
    def _apply_quality_standards_segment(content: str, prompt_template: str) -> str:
        """
        第3段：质量标准微调（短提示词，专注质量）
        """
        quality_prompt = f"""
# ⭐ 第3段微调：质量标准

## 当前内容
```
{content}
```

## 📋 质量标准要求
1. **逻辑性强化**：
   - 因果关系完整：原因→过程→结果→影响
   - 人物行为合理：有明确目的和动机
   - 事件解释清楚：新元素有合理出现原因

2. **语言通俗化**：
   - 使用简洁句式，优先短句
   - 日常化表达，避免专业术语
   - 避免AI化表达和过度修辞

3. **连贯性要求**：
   - 人物名称一致
   - 情节逻辑连贯
   - 风格统一

## 🎯 微调任务
重点检查和优化逻辑漏洞、语言表达、连贯性问题。
保持故事情节和字数基本不变，只优化质量问题。

请输出质量优化后的完整内容：
"""

        try:
            api_client = TestService._get_api_client()
            response = api_client.analyze_text(
                text=quality_prompt,
                analysis_type="quality_standards_refinement",
                max_tokens=12000,  # 增加token限制，确保质量深度优化
                prompt_template=prompt_template,
                temperature=0.3  # 稍微提高创造性，让质量优化更明显
            )

            if response and isinstance(response, dict):
                refined_content = response.get("content", "") or response.get("analysis_result", "")
                if refined_content and len(refined_content.strip()) > len(content) * 0.7:
                    logger.info("✅ 第3段质量标准微调成功")
                    return refined_content.strip()

        except Exception as e:
            logger.error(f"第3段质量标准微调失败: {str(e)}")

        # 🔧 修复：没有获取到有效结果时，返回原始内容，避免内容丢失
        logger.warning("第3段质量标准微调未获取到有效结果，返回原始内容")
        return content

    @staticmethod
    def _apply_prohibition_check_segment(content: str, original_sample: str, prompt_template: str) -> str:
        """
        第4段：禁止事项检查微调（短提示词，专注检查）
        """
        prohibition_prompt = f"""
# 🚫 第4段微调：禁止事项深度检查

## 当前内容
```
{content}
```

## 🚨 禁止事项深度检查（必须严格执行）
1. **内容创新性检查**：
   - 🔥 **强制检查**：是否有情节模仿、场景复制
   - 🔥 **强制检查**：是否有人名重复、直接引用
   - 🔥 **强制检查**：是否有对话内容的直接借用

2. **表达方式检查**：
   - 🔥 **强制检查**：消除所有AI化表达（"展现、呈现、进行"等）
   - 🔥 **强制检查**：删除所有破折号（——）和复杂句式
   - 🔥 **强制检查**：替换专业术语（量子、基因、算法、系统等）
   - 🔥 **强制检查**：消除过度修辞和华丽辞藻

3. **逻辑合理性检查**：
   - 🔥 **强制检查**：所有"突然"、"忽然"是否有合理铺垫
   - 🔥 **强制检查**：是否编造了前文没有的人物或设定
   - 🔥 **强制检查**：人物行为是否符合其性格特征

4. **语言风格检查**：
   - 🔥 **强制检查**：是否保持了原文样本的语言特色
   - 🔥 **强制检查**：是否保持了原文样本的句子长短搭配
   - 🔥 **强制检查**：对话是否自然真实

## 📖 原文样本风格参考（严格学习）
```
{original_sample[:600] if original_sample else "无原文样本"}
```

## 🎯 具体检查修正指令（必须执行）
**请逐句检查并修正以下问题：**

1. **AI化表达替换**：
   - "展现" → "显出"、"表现出"
   - "呈现" → "出现"、"露出"
   - "进行" → "做"、"开始"
   - "实施" → "弄"、"搞"

2. **句式简化**：
   - 拆分超过20字的长句
   - 删除不必要的修饰词
   - 增加口语化连接词

3. **逻辑完善**：
   - 为突然出现的情况增加铺垫
   - 确保人物反应合理
   - 补充必要的过渡说明

4. **风格统一**：
   - 确保整体语言风格与原文样本一致
   - 让对话更加个性化
   - 增强情感表达的真实性

⚠️ **重要**：必须进行深度检查和修正，确保内容质量显著提升！

请输出深度检查修正后的完整内容：
"""

        try:
            api_client = TestService._get_api_client()
            response = api_client.analyze_text(
                text=prohibition_prompt,
                analysis_type="prohibition_check_refinement",
                max_tokens=12000,  # 增加token限制，确保深度检查修正
                prompt_template=prompt_template,
                temperature=0.2  # 稍微提高创造性，让修正更明显
            )

            if response and isinstance(response, dict):
                refined_content = response.get("content", "") or response.get("analysis_result", "")
                if refined_content and len(refined_content.strip()) > len(content) * 0.7:
                    logger.info("✅ 第4段禁止事项检查微调成功")
                    return refined_content.strip()

        except Exception as e:
            logger.error(f"第4段禁止事项检查微调失败: {str(e)}")

        # 🔧 修复：没有获取到有效结果时，返回原始内容，避免内容丢失
        logger.warning("第4段禁止事项检查微调未获取到有效结果，返回原始内容")
        return content





    @staticmethod
    def _apply_logic_enhancement_verification(content: str, novel: Novel, chapter: Chapter,
                                            book_analysis: Dict[str, Dict[str, str]],
                                            chapter_analysis: Dict[str, Dict[str, str]],
                                            prompt_template: str = "default") -> str:
        """
        简单逻辑补充验证：只做最小化的、必要的逻辑补充
        至多1-2个点补充，且补充内容必须是短句简单句通俗点、符合样本语言特征
        """
        logger.info("🔍 开始简单逻辑补充验证：最小化补充，保护原有精品内容质量")

        # 获取原文样本
        original_sample = chapter.content if chapter.content else ""

        # 构建简单逻辑补充验证提示词
        logic_prompt = f"""
# 🔍 简单逻辑补充验证任务：最小化逻辑补充

## ⚠️ 重要说明：这是最小化逻辑补充，绝对不是重新写作！

### 🎯 简单逻辑补充核心任务
**你的任务是对生成内容进行最小化的逻辑补充，至多补充1-2个逻辑点，保护原有内容质量！**

## 📋 当前生成内容（需要最小化逻辑检查）
以下是当前生成的精品内容，请进行最小化逻辑检查：

```
{content}
```

## 📚 原文样本参考（学习语言特征）
**学习原文样本的语言特征，补充内容必须符合样本风格：**

```
{original_sample[:500] if original_sample else "无原文样本"}
```

## 🚨 简单逻辑补充要求（严格限制）

### 1. 最小化补充原则
- **至多补充1-2个逻辑点**：只补充最关键的逻辑缺失
- **保护原有内容**：绝对不要大幅修改或重写原有内容
- **保持原有质量**：确保补充后质量不下降

### 2. 补充内容要求
- **短句简单句**：补充的内容必须是短句、简单句
- **通俗易懂**：使用通俗点的表达，避免复杂词汇
- **符合样本特征**：完全模仿原文样本的语言风格和句式

### 3. 重点检查项（只检查最严重的）
- **突兀出现**：检查是否有完全没有铺垫的突然情况
- **行为无因**：检查是否有完全没有动机的人物行为
- **逻辑断层**：检查是否有明显的逻辑跳跃

## 🚫 严格禁止事项
- ❌ 绝对不要大幅修改原有内容
- ❌ 绝对不要重新组织段落结构
- ❌ 绝对不要改变原有的叙述风格
- ❌ 绝对不要添加复杂的描写
- ❌ 绝对不要使用长句复杂句
- ❌ 绝对不要破坏原有的节奏感

## 📝 简单逻辑补充要求
**如果发现明显的逻辑缺失，只在必要位置添加1-2句短句进行最小化补充。**
**如果逻辑基本合理，直接输出原内容，不做任何修改。**
**补充的句子必须是短句、简单句、通俗点，完全符合原文样本的语言特征！**

⚠️ 核心原则：保护精品内容质量，最小化干预，只做必要补充！

请开始简单逻辑补充验证：
"""

        try:
            # 调用API进行简单逻辑补充验证
            result = TestService._generate_chapter_content_original(
                prompt=logic_prompt,
                prompt_template=prompt_template
            )

            # 更严格的验证：只有在补充内容合理且不破坏质量时才使用
            if result and len(result.strip()) > len(content) * 0.9:  # 提高阈值到90%
                original_word_count = TestService._count_words_accurately(content)
                logic_word_count = TestService._count_words_accurately(result)

                # 检查补充是否过度（字数增加不应超过20%）
                if logic_word_count <= original_word_count * 1.2:
                    logger.info(f"简单逻辑补充验证成功，从{original_word_count}字补充到{logic_word_count}字")
                    return result
                else:
                    logger.warning(f"逻辑补充过度（{logic_word_count}字 > {original_word_count * 1.2:.0f}字），保持原内容")
                    return content
            else:
                logger.info("简单逻辑补充验证：内容逻辑基本合理，无需补充")
                return content

        except Exception as e:
            logger.error(f"简单逻辑补充验证异常: {str(e)}")
            return content

    @staticmethod
    def _has_serious_logic_issues(content: str) -> bool:
        """
        检查内容是否有严重的逻辑问题

        Args:
            content: 要检查的内容

        Returns:
            True如果有严重逻辑问题，False如果逻辑良好
        """
        # 检查是否有过多的突兀转折
        if content.count('突然') > 5:
            logger.warning("严重逻辑问题：过多突兀转折")
            return True

        # 检查是否有明显的逻辑错误标志
        error_indicators = ['生成内容失败', 'API返回', '错误', '失败', '无法生成', '莫名其妙']
        for indicator in error_indicators:
            if indicator in content:
                logger.warning(f"严重逻辑问题：包含错误标志'{indicator}'")
                return True

        # 检查是否缺乏基本的因果关系
        causal_words = ['因为', '所以', '于是', '因此', '由于', '导致', '结果', '原来']
        causal_count = sum(content.count(word) for word in causal_words)
        if len(content) > 1500 and causal_count < 2:
            logger.warning("严重逻辑问题：严重缺乏因果关系词汇")
            return True

        return False

    @staticmethod
    def _has_sentence_style_abuse(content: str) -> bool:
        """
        检查内容是否有句式滥用（长句、复杂句）

        Args:
            content: 要检查的内容

        Returns:
            True如果有句式滥用，False如果句式良好
        """
        sentences = [s.strip() for s in content.split('。') if s.strip()]
        if len(sentences) < 3:
            return False

        # 检查长句比例
        long_sentences = [s for s in sentences if len(s) > 50]
        long_sentence_ratio = len(long_sentences) / len(sentences)

        if long_sentence_ratio > 0.3:  # 超过30%的长句
            logger.warning(f"句式滥用：长句比例过高({long_sentence_ratio:.1%})")
            return True

        # 检查复杂句式标志
        complex_indicators = ['不仅', '而且', '虽然', '但是', '尽管', '然而', '因此', '所以']
        complex_count = sum(content.count(indicator) for indicator in complex_indicators)

        if complex_count > len(sentences) * 0.5:  # 平均每句超过0.5个复杂句式标志
            logger.warning(f"句式滥用：复杂句式过多({complex_count}个)")
            return True

        return False

    @staticmethod
    def _check_dialogue_adequacy(content: str) -> bool:
        """
        检查对话内容是否达标

        Args:
            content: 要检查的内容

        Returns:
            True如果对话达标，False如果对话不达标
        """
        # 检查是否包含对话
        dialogue_count = content.count('"') + content.count('"')

        if dialogue_count < 2:  # 至少要有一组对话
            logger.warning("对话不达标：缺少对话内容")
            return False

        # 检查对话的自然性（简单检查）
        if '说道' in content or '说着' in content or '开口道' in content:
            return True

        # 如果有对话标记但没有对话引导词，也认为基本达标
        return dialogue_count >= 4  # 至少两组完整对话

    @staticmethod
    def _check_original_style_compliance(content: str, original_sample: str) -> bool:
        """
        检查内容是否符合原文特征

        Args:
            content: 要检查的内容
            original_sample: 原文样本

        Returns:
            True如果符合原文特征，False如果不符合
        """
        if not original_sample:
            logger.warning("原文特征检查：无原文样本，跳过检查")
            return True

        # 检查句子长度分布是否相似（放宽标准）
        content_sentences = [s.strip() for s in content.split('。') if s.strip()]
        sample_sentences = [s.strip() for s in original_sample.split('。') if s.strip()]

        if len(content_sentences) < 3 or len(sample_sentences) < 3:
            return True  # 句子数量不足时不做严格要求

        # 计算平均句子长度（放宽标准）
        content_avg_len = sum(len(s) for s in content_sentences) / len(content_sentences)
        sample_avg_len = sum(len(s) for s in sample_sentences) / len(sample_sentences)

        # 允许50%的差异（比之前的30%更宽松）
        if abs(content_avg_len - sample_avg_len) > sample_avg_len * 0.5:
            logger.warning(f"原文特征检查：句子长度差异较大，内容平均{content_avg_len:.1f}字，样本平均{sample_avg_len:.1f}字")
            return False

        return True

    @staticmethod
    def _check_contemporary_webnovel_compliance(content: str) -> bool:
        """
        检查内容是否符合当代网文特征

        Args:
            content: 要检查的内容

        Returns:
            True如果符合当代网文特征，False如果不符合
        """
        logger.info("开始检查当代网文特征符合度")

        # 检查基本内容长度
        if len(content.strip()) < 100:
            logger.warning("当代网文特征检查：内容过短")
            return False

        # 1. 检查句子长度分布（当代网文偏向短句）
        sentences = [s.strip() for s in content.split('。') if s.strip()]
        if len(sentences) < 3:
            logger.warning("当代网文特征检查：句子数量不足")
            return False

        # 计算短句比例（≤20字为短句）
        short_sentences = [s for s in sentences if len(s) <= 20]
        short_sentence_ratio = len(short_sentences) / len(sentences)

        # 当代网文应该有合理的短句比例（至少10%，进一步放宽标准）
        if short_sentence_ratio < 0.10:
            logger.warning(f"当代网文特征检查：短句比例过低({short_sentence_ratio:.1%}，应≥10%)")
            return False

        # 2. 检查对话比例（当代网文对话较多）
        dialogue_count = content.count('"') + content.count('"')
        if dialogue_count < 2:
            logger.warning("当代网文特征检查：对话内容不足")
            return False

        # 3. 检查是否有当代网文常见元素
        webnovel_indicators = [
            # 人物称呼
            r'[他她][的]?[眼神表情脸色]',
            # 动作描写
            r'[走过来走向转身回头点头摇头]',
            # 情感表达
            r'[笑了笑笑道冷笑苦笑]',
            # 对话标志
            r'说道?[:：]',
            # 时间表达
            r'[这时此时这时候]',
            # 网文常用词
            r'[突然忽然瞬间立刻马上]'
        ]

        webnovel_feature_count = 0
        for pattern in webnovel_indicators:
            if re.search(pattern, content):
                webnovel_feature_count += 1

        # 进一步放宽要求：至少要有1个当代网文特征即可
        if webnovel_feature_count < 1:
            logger.warning(f"当代网文特征检查：网文特征不足({webnovel_feature_count}个，应≥1个)")
            return False

        # 4. 检查是否有不符合当代网文的元素
        outdated_patterns = [
            r'.*——.*',  # 破折号句式
            r'[，。][而且并且然而但是因此所以]',  # 过于书面化的连接词
            r'[之所以正因为由于].*[，。]',  # 复杂因果句式
            r'[不仅.*而且|虽然.*但是|尽管.*然而]',  # 复杂转折句式
        ]

        for pattern in outdated_patterns:
            if re.search(pattern, content):
                logger.warning(f"当代网文特征检查：包含不符合当代网文的句式")
                return False

        logger.info(f"当代网文特征检查通过：短句比例{short_sentence_ratio:.1%}，网文特征{webnovel_feature_count}个")
        return True

    @staticmethod
    def _get_api_client():
        """获取API客户端实例"""
        # 使用现有的DeepSeek客户端
        from src.api.deepseek_client import DeepSeekClient
        return DeepSeekClient()

    @staticmethod
    def _check_content_logic(content: str) -> bool:
        """
        检查内容的逻辑性

        Args:
            content: 要检查的内容

        Returns:
            True如果逻辑性良好，False如果逻辑性有问题
        """
        # 检查是否有过多的突兀转折
        if content.count('突然') > 3:
            logger.warning("逻辑性检查：过多突兀转折")
            return False

        # 检查是否有明显的逻辑错误标志
        error_indicators = ['生成内容失败', 'API返回', '错误', '失败', '无法生成']
        for indicator in error_indicators:
            if indicator in content:
                logger.warning(f"逻辑性检查：包含错误标志'{indicator}'")
                return False

        # 检查是否有合理的因果关系
        causal_words = ['因为', '所以', '于是', '因此', '由于', '导致']
        causal_count = sum(content.count(word) for word in causal_words)
        if len(content) > 1000 and causal_count < 2:
            logger.warning("逻辑性检查：缺乏因果关系词汇")
            return False

        return True

    @staticmethod
    def _generate_female_protagonist_name(novel_type: str = "现代都市", character_traits: str = "") -> str:
        """
        根据小说类型生成优雅高级的女主角名字（3个字）

        Args:
            novel_type: 小说类型
            character_traits: 人物性格特征

        Returns:
            生成的女主角名字
        """
        # 根据小说类型选择合适的姓氏和名字组合
        name_pools = {
            "古风/仙侠": {
                "surnames": ["慕容", "上官", "司马", "欧阳", "南宫", "东方", "西门", "公孙"],
                "names": ["清雅", "若汐", "婉音", "诗韵", "墨染", "清澜", "若璃", "婉清"]
            },
            "现代都市": {
                "surnames": ["林", "苏", "顾", "沈", "谢", "姜", "陆", "宋"],
                "names": ["诗涵", "墨染", "清欢", "若汐", "思语", "君墨", "微漾", "清辞"]
            },
            "校园青春": {
                "surnames": ["陈", "李", "王", "张", "刘", "赵", "孙", "周"],
                "names": ["思语", "若汐", "君墨", "诗涵", "清欢", "微漾", "婉清", "若璃"]
            },
            "职场商战": {
                "surnames": ["沈", "谢", "姜", "顾", "林", "苏", "陆", "宋"],
                "names": ["清辞", "危澜", "星眠", "墨染", "清欢", "诗涵", "若汐", "微漾"]
            },
            "悬疑推理": {
                "surnames": ["白", "夏", "冷", "秦", "叶", "方", "程", "韩"],
                "names": ["月清", "知秋", "如霜", "若冰", "清澜", "墨染", "思语", "婉音"]
            },
            "科幻未来": {
                "surnames": ["星", "月", "云", "夜", "光", "影", "雪", "风"],
                "names": ["若璃", "清澜", "知音", "思语", "墨染", "婉音", "清雅", "若汐"]
            }
        }

        # 默认使用现代都市类型
        if novel_type not in name_pools:
            novel_type = "现代都市"

        pool = name_pools[novel_type]

        # 简单的名字生成逻辑（实际使用时可以更复杂）
        import random
        surname = random.choice(pool["surnames"])
        name = random.choice(pool["names"])

        # 确保是3个字
        if len(surname) == 1:
            full_name = surname + name
        else:
            # 如果是复姓，名字取一个字
            full_name = surname + name[0]

        return full_name

    @staticmethod
    def _detect_novel_type_from_content(content: str) -> str:
        """
        从内容中检测小说类型

        Args:
            content: 小说内容

        Returns:
            检测到的小说类型
        """
        # 关键词映射
        type_keywords = {
            "古风/仙侠": ["修仙", "仙侠", "古代", "皇帝", "公主", "王爷", "丞相", "江湖", "武功", "内力"],
            "现代都市": ["都市", "现代", "公司", "总裁", "白领", "职场", "商业", "豪门", "都市"],
            "校园青春": ["校园", "学校", "大学", "高中", "同学", "老师", "青春", "学生", "考试"],
            "职场商战": ["职场", "商战", "公司", "总裁", "商业", "企业", "投资", "股票", "商务"],
            "悬疑推理": ["悬疑", "推理", "侦探", "案件", "凶手", "线索", "调查", "真相", "犯罪"],
            "科幻未来": ["科幻", "未来", "机器人", "人工智能", "太空", "星际", "科技", "虚拟"]
        }

        # 统计各类型关键词出现次数
        type_scores = {}
        for novel_type, keywords in type_keywords.items():
            score = sum(content.count(keyword) for keyword in keywords)
            type_scores[novel_type] = score

        # 返回得分最高的类型
        if max(type_scores.values()) > 0:
            return max(type_scores, key=type_scores.get)
        else:
            return "现代都市"  # 默认类型

    @staticmethod
    def _check_sample_style_compliance(content: str, original_sample: str) -> bool:
        """
        检查内容是否符合样本句式和语言风格

        Args:
            content: 要检查的内容
            original_sample: 原文样本

        Returns:
            True如果符合样本风格，False如果不符合
        """
        if not original_sample:
            logger.warning("样本风格检查：无原文样本，跳过检查")
            return True

        # 检查句子长度分布是否相似
        content_sentences = [s.strip() for s in content.split('。') if s.strip()]
        sample_sentences = [s.strip() for s in original_sample.split('。') if s.strip()]

        if len(content_sentences) < 3 or len(sample_sentences) < 3:
            logger.warning("样本风格检查：句子数量不足")
            return False

        # 计算平均句子长度
        content_avg_len = sum(len(s) for s in content_sentences) / len(content_sentences)
        sample_avg_len = sum(len(s) for s in sample_sentences) / len(sample_sentences)

        # 允许30%的差异
        if abs(content_avg_len - sample_avg_len) > sample_avg_len * 0.3:
            logger.warning(f"样本风格检查：句子长度差异过大，内容平均{content_avg_len:.1f}字，样本平均{sample_avg_len:.1f}字")
            return False

        # 检查对话比例是否相似
        content_dialogue_count = content.count('"') + content.count('"')
        sample_dialogue_count = original_sample.count('"') + original_sample.count('"')

        content_dialogue_ratio = content_dialogue_count / len(content) if len(content) > 0 else 0
        sample_dialogue_ratio = sample_dialogue_count / len(original_sample) if len(original_sample) > 0 else 0

        # 允许50%的差异
        if sample_dialogue_ratio > 0 and abs(content_dialogue_ratio - sample_dialogue_ratio) > sample_dialogue_ratio * 0.5:
            logger.warning(f"样本风格检查：对话比例差异过大，内容{content_dialogue_ratio:.3f}，样本{sample_dialogue_ratio:.3f}")
            return False

        return True

    @staticmethod
    def _has_obvious_quality_issues(content: str) -> bool:
        """
        检查内容是否有明显的质量问题

        Args:
            content: 要检查的内容

        Returns:
            True如果有明显质量问题，False如果质量良好
        """
        # 检查是否有过多的突兀转折
        if content.count('突然') > 3:
            return True

        # 检查是否有明显的逻辑错误标志
        error_indicators = ['生成内容失败', 'API返回', '错误', '失败', '无法生成']
        for indicator in error_indicators:
            if indicator in content:
                return True

        # 检查是否内容过于简单（缺乏对话和描述）
        if content.count('。') < 5 or ('"' not in content and '"' not in content):
            return True

        # 检查是否有重复的句子模式（简单检查）
        sentences = content.split('。')
        if len(sentences) > 5:
            # 检查是否有过多相似的句子开头
            sentence_starts = [s.strip()[:3] for s in sentences if len(s.strip()) > 3]
            if len(sentence_starts) > 0:
                most_common_start = max(set(sentence_starts), key=sentence_starts.count)
                if sentence_starts.count(most_common_start) > len(sentence_starts) * 0.4:
                    return True

        return False

    @staticmethod
    def _format_quality_issues(issues: List[str]) -> str:
        """格式化质量问题"""
        if not issues:
            return "内容质量良好，无明显问题。"

        formatted = "发现以下质量问题：\n"
        for i, issue in enumerate(issues, 1):
            formatted += f"{i}. {issue}\n"
        return formatted



    @staticmethod
    def _execute_first_batch_framework_building(novel: Novel, chapter: Chapter,
                                              book_analysis: Dict[str, Dict[str, str]],
                                              chapter_analysis: Dict[str, Dict[str, str]],
                                              prompt_template: str = "default",
                                              previous_chapters: List[str] = None,
                                              chapter_continuity_data: Dict = None) -> str:
        """
        第一批次：基础框架构建（激活DeepSeek R1思考链）

        专注于：
        1. 激活DeepSeek R1的深度思考能力
        2. 构建章节基础框架和结构
        3. 确定核心剧情走向
        4. 建立人物关系和对话基调
        """
        logger.info("🧠 第一批次开始：激活DeepSeek R1思考链，构建基础框架")

        # 分段构建提示词 - 第一段：核心写作指令
        core_prompt = TestService._build_core_writing_segment(
            novel=novel,
            chapter=chapter,
            book_analysis=book_analysis,
            chapter_analysis=chapter_analysis,
            prompt_template=prompt_template
        )

        # 第二段：DeepSeek R1思考链激活指令
        thinking_activation = """
## 🧠 DeepSeek R1深度思考激活指令

在开始写作前，请进行系统性深度思考：

### 第一阶段：分析理解
1. **仔细分析所有分析结果**：逐一理解每个维度的分析内容
2. **理解原文风格特征**：把握语言风格、叙述节奏、人物特色
3. **明确创作要求**：理解所有写作指导和限制条件
4. **确认人物设定**：确保人物名称、性格、关系的一致性

### 第二阶段：创作规划
1. **制定章节结构**：规划开头、发展、高潮、结尾的基本框架
2. **设计情节走向**：确定本章的核心事件和发展脉络
3. **规划人物互动**：安排人物对话和行为的合理性
4. **确定叙述风格**：选择合适的语言风格和表达方式

### 第三阶段：风格定位
1. **语言特色确定**：使用通俗口语化表达，避免文绉绉
2. **句式节奏把握**：多用短句，保持阅读流畅性
3. **对话风格统一**：确保人物对话符合其性格特征
4. **热梗融入策略**：自然融入网络用语，不生硬套用

### 第四阶段：逻辑检查
1. **情节逻辑验证**：确保事件发展的合理性和连贯性
2. **人物行为一致性**：检查人物反应是否符合其性格
3. **前后文呼应**：确保与前序章节的连贯性
4. **细节真实性**：避免编造前文未提及的设定

请充分发挥DeepSeek R1的思考能力，确保完全理解并执行所有指令。
思考完成后，开始构建章节基础框架。
"""

        # 第三段：章节连贯性增强
        continuity_prompt = ""
        if previous_chapters and chapter_continuity_data:
            continuity_prompt = TestService._build_continuity_prompt_enhancement(
                previous_chapters=previous_chapters,
                continuity_data=chapter_continuity_data,
                current_chapter_number=chapter.chapter_number,
                book_analysis=book_analysis
            )

        # 第四段：基础框架构建指令（强化四个强制要求）
        framework_instruction = """
## 📝 第一批次任务：基础框架构建

### 🚨 四个强制执行要求（第一批次必须严格遵守）
**在构建基础框架时，以下四点必须100%执行：**

1. **直接套用原文样本的句式结构、语言特征、句式节奏**
   - 深度学习分析结果中提供的原文样本的语言风格
   - 严格模仿原文样本的句式模式、表达习惯、语言节奏
   - 禁止复制原文情节，但必须完全复制原文样本的语言特征
   - 学习原文样本的句子长短、语气词使用、表达方式
   - 重点关注句式之间的连贯方式和逻辑连接

2. **更偏向于短句、日常化通俗口语化**
   - 优先使用短句构建基础框架，避免长句和复杂句
   - 采用日常生活化的词汇和表达，减少修辞手法
   - 避免复杂句式和书面化表达，保持通俗易懂
   - 多用日常对话式表达，少用华丽辞藻

3. **要有十分严密的逻辑性、因果联系**
   - 基础框架的每个环节都必须有逻辑支撑
   - 人物行为和情节发展必须符合因果关系
   - 确保框架的逻辑严密性，避免突兀的情节转折
   - 任何道具出现都要解释来源和用途
   - 主角面临的情况都要有前因后果
   - 人物决定都要有合理动机

4. **特别注意生成内容章节之间的联系与继承**
   - 必须与前序章节保持完美连贯
   - 人物名称、性格、关系绝对一致
   - 情节发展必须自然承接前文

### 👥 人物命名特别要求
**参考优质命名风格（如：沈清辞、林微漾、谢危澜、姜星眠的命名特点）：**

#### 🌸 女主角命名专项要求（重点优化）
**女主角名字必须符合以下标准：**
- **字数要求**：尽量使用3个字（姓氏1字+名字2字），如"林微漾"、"谢危澜"
- **内涵要求**：名字要有深层文化内涵，体现优雅气质和高级感
- **类型适配**：根据小说类型调整命名风格
  - **古风/仙侠类**：如"慕容清雅"、"上官若汐"、"司马婉音"（古典优雅）
  - **现代都市类**：如"林诗涵"、"苏墨染"、"顾清欢"（现代文雅）
  - **校园青春类**：如"陈思语"、"李若汐"、"王君墨"（清新脱俗）
  - **职场商战类**：如"沈清辞"、"谢危澜"、"姜星眠"（知性干练）
  - **悬疑推理类**：如"白月清"、"夏知秋"、"冷如霜"（神秘优雅）
  - **科幻未来类**：如"星若璃"、"月清澜"、"云知音"（未来感+优雅）

#### 👨 男主角及其他角色命名要求
- 姓氏：根据故事背景和时代设定自由选择，不限制范围
- 名字：根据人物性格、故事风格和文化背景灵活调整
- 风格适配：市井化/内涵化/哲理化/现代化等，与文本内容相匹配
- 现代化风格：简洁但仍需有文化内涵，避免过于平淡无味
- 音韵美感：注重读音流畅，避免拗口难念
- 避免原则：过于俗套、难听或与人物性格严重不符的名字

#### 🎯 命名质量标准
**所有人物命名都应达到以下标准：**
- **音韵美感**：读音流畅，朗朗上口
- **文化内涵**：有一定的文化底蕴和寓意
- **类型匹配**：与小说类型和背景设定相符
- **性格呼应**：与人物性格特征相呼应
- **避免雷同**：避免使用过于常见或俗套的名字

### 🎯 本批次目标
构建章节的基础框架，包括：
1. 章节开头的自然承接
2. 核心情节的基本走向
3. 主要人物的出场和互动
4. 基础对话和叙述结构

### 📋 具体要求
1. **字数控制**：本批次生成800-1200字的基础框架
2. **结构完整**：包含开头、发展、初步结尾的基本结构
3. **风格统一**：严格按照分析结果中的语言风格要求
4. **逻辑清晰**：确保情节发展的合理性和连贯性
5. **四要求达标**：必须100%执行上述四个强制要求

### 🚫 本批次限制
1. 不要过度展开细节描写
2. 不要强行融入所有15个维度
3. 重点关注框架搭建，细节留待后续批次
4. 确保人物名称绝对一致
5. **绝对不能违反四个强制要求**

⚠️ 重要提醒：四个强制要求是不可妥协的底线，必须在基础框架中严格执行！

请开始第一批次的基础框架构建：
"""

        # 组合完整的第一批次提示词
        first_batch_prompt = core_prompt + "\n" + thinking_activation + "\n" + continuity_prompt + "\n" + framework_instruction

        # 增强的第一批次构建，包含重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"第一批次基础框架构建，尝试 {attempt + 1}/{max_retries}")

                # 使用原始生成方法调用API生成基础框架
                base_content = TestService._generate_chapter_content_original(
                    prompt=first_batch_prompt,
                    prompt_template=prompt_template
                )

                logger.info(f"第一批次基础框架构建完成，长度: {len(base_content) if base_content else 0}")

                # 详细检查返回的内容
                if not base_content:
                    logger.warning(f"第一批次尝试 {attempt + 1}: 返回内容为空")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return "第一批次基础框架构建失败：API返回空内容"

                # 检查是否是错误信息
                if "生成内容失败" in base_content or "API返回的数据格式不正确" in base_content:
                    logger.warning(f"第一批次尝试 {attempt + 1}: 返回错误信息 - {base_content[:100]}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(2)  # 等待2秒后重试
                        continue
                    else:
                        return f"第一批次基础框架构建失败：{base_content}"

                # 验证第一批次内容的质量和长度
                if not TestService._validate_batch_content(base_content, 1, min_length=500):
                    logger.warning(f"第一批次尝试 {attempt + 1}: 内容质量验证失败")

                    # 提供详细的诊断信息
                    TestService._diagnose_batch_content_failure(base_content, 1)

                    if attempt < max_retries - 1:
                        import time
                        time.sleep(2)  # 等待2秒后重试
                        continue
                    else:
                        logger.error("第一批次基础框架构建失败：内容质量不符合要求")

                        # 最终失败时提供详细诊断
                        diagnosis = TestService._diagnose_batch_content_failure(base_content, 1)
                        logger.error(f"第一批次最终失败诊断：{diagnosis}")

                        return f"第一批次基础框架构建失败：内容质量不符合要求。{diagnosis}"

                # 成功生成有效内容
                logger.info(f"第一批次基础框架构建成功，尝试次数: {attempt + 1}")
                return base_content

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"第一批次尝试 {attempt + 1} 失败: {error_msg}")

                if attempt < max_retries - 1:
                    # 根据错误类型决定等待时间
                    if "timeout" in error_msg.lower() or "超时" in error_msg:
                        wait_time = 5  # 超时错误等待5秒
                    elif "500" in error_msg or "服务器" in error_msg:
                        wait_time = 3  # 服务器错误等待3秒
                    else:
                        wait_time = 2  # 其他错误等待2秒

                    logger.info(f"等待 {wait_time} 秒后重试...")
                    import time
                    time.sleep(wait_time)
                else:
                    logger.error(f"第一批次基础框架构建最终失败: {error_msg}")
                    return f"第一批次基础框架构建失败：{error_msg}"

        # 理论上不会到达这里
        return "第一批次基础框架构建失败：所有重试尝试都失败"

    @staticmethod
    def _build_core_writing_segment(novel: Novel, chapter: Chapter,
                                  book_analysis: Dict[str, Dict[str, str]],
                                  chapter_analysis: Dict[str, Dict[str, str]],
                                  prompt_template: str = "default") -> str:
        """
        构建核心写作指令段落（分段策略的第一段）
        """
        # 提取关键分析结果
        language_style = book_analysis.get("language_style", {}).get("content", "")
        character_development = book_analysis.get("character_development", {}).get("content", "")
        hot_memes = book_analysis.get("hot_meme_statistics", {}).get("content", "")

        core_segment = f"""
# 📚 九猫写作系统 - 核心写作指令

## 📖 小说信息
- 标题：《{novel.title}》
- 章节：第{chapter.chapter_number}章 - {chapter.title}
- 目标字数：2000字左右
- 使用模板：{prompt_template}版

## 🎯 核心创作要求

### 1. 语言风格要求（必须严格遵循）
{language_style[:500] if language_style else "使用通俗口语化表达，多用短句，避免华丽辞藻"}

### 2. 人物塑造要求
{character_development[:500] if character_development else "保持人物性格一致，对话要符合人物特征"}

### 3. 热梗融入策略（自然融入，不强求）
{hot_memes[:300] if hot_memes else "可适当融入网络流行用语，但要符合剧情需要"}

### 4. 基础创作原则
1. **完全基于分析结果创作**：严格按照15个维度的分析结果进行创作
2. **学习原文风格**：深度模仿原文的叙述方式、语言特色、节奏把握
3. **创新性要求**：更换人物名称，创作全新情节，避免抄袭原文
4. **质量优先**：优先保证剧情质量和逻辑性，字数可适当调整
"""

        return core_segment

    @staticmethod
    def _execute_second_batch_content_enhancement(base_content: str, novel: Novel, chapter: Chapter,
                                                book_analysis: Dict[str, Dict[str, str]],
                                                chapter_analysis: Dict[str, Dict[str, str]],
                                                prompt_template: str = "default") -> str:
        """
        第二批次：内容深化优化（基于第一批次结果）

        专注于：
        1. 基于第一批次的基础框架进行深化
        2. 融入更多15个维度的特色
        3. 优化语言表达和对话质量
        4. 扩展情节细节和人物互动
        """
        logger.info("🔧 第二批次开始：基于第一批次结果进行内容深化优化")

        # 分析第一批次内容的特点
        content_analysis = TestService._analyze_base_content_features(base_content)

        # 构建第二批次优化提示词（强化四个强制要求，确保承接上一轮结果）
        enhancement_prompt = f"""
# 🔧 第二批次任务：基于第一批次结果的内容深化优化

## ⚠️ 重要提醒：必须基于第一批次结果进行优化，不是重新写作！

### 🎯 第二批次核心任务
**你的任务是对第一批次的内容进行深化和优化，而不是重新创作！**

## 📋 第一批次基础框架（必须在此基础上优化）
以下是第一批次构建的基础框架，你必须在此基础上进行深化优化：

```
{base_content}
```

## 🚨 四个强制执行要求（第二批次必须继续严格遵守）
**在深化优化第一批次内容时，以下四点必须继续100%执行：**

1. **直接套用原文样本的句式结构、语言特征、句式节奏**
   - 继续深度学习和模仿原文样本的语言风格
   - 在深化内容时保持原文样本的句式特征
   - 绝不复制情节，但必须复制原文样本的语言模式
   - 重点关注句式之间的连贯方式和逻辑连接
   - 学习原文样本的表达习惯和语言节奏

2. **更偏向于短句、日常化通俗口语化**
   - 在扩展内容时优先使用短句
   - 深化部分必须保持通俗易懂
   - 避免因为深化而使用复杂表达
   - 减少修辞手法，多用日常化口语
   - 保持句子简洁明了，避免冗长复杂句式

3. **要有十分严密的逻辑性、因果联系**
   - 深化的每个细节都必须有逻辑支撑
   - 新增内容必须与第一批次的逻辑完全吻合
   - 确保因果关系链的完整性
   - 任何新增道具都要解释来源、用途和对主角的作用
   - 主角面临的新情况都要有前因后果
   - 人物的新决定都要有合理的动机

4. **特别注意生成内容章节之间的联系与继承**
   - 深化内容必须与前序章节完全连贯
   - 绝对不能改变第一批次已确定的人物设定
   - 新增情节必须自然承接第一批次的内容
   - 保持人物名称的一致性和优质性

## 🔧 第二批次具体优化要求
**重要：你必须保持第一批次的所有核心内容，只是在此基础上进行深化和优化！**

## 🎯 第二批次优化目标

### 1. 内容深化要求
1. **保持基础框架不变**：不要改变第一批次已确定的核心情节和人物设定
2. **深化情节发展**：在现有框架基础上，丰富情节细节和转折
3. **优化语言表达**：提升语言的生动性和可读性，但保持通俗风格
4. **增强人物互动**：丰富人物对话和心理描写，增强代入感
5. **严格执行四要求**：在深化过程中必须100%执行四个强制要求

### 2. 15个维度的自然融入
基于第一批次的基础，自然融入以下维度特色：

#### 语言风格深化（必须符合四要求）
- 进一步优化口语化表达，使对话更加自然
- 调整句式节奏，确保阅读流畅性
- 减少书面语，增加生活化词汇

#### 人物关系深化（必须符合四要求）
- 丰富人物间的互动细节
- 增强人物性格的表现力
- 优化对话的个性化特征

#### 节奏把控优化（必须符合四要求）
- 调整情节发展的节奏感
- 平衡对话与叙述的比例
- 增强章节的紧凑感

#### 热梗自然融入（必须符合四要求）
- 在合适的场景中自然融入网络用语
- 确保热梗使用符合人物身份
- 避免生硬套用，保持自然性

### 3. 具体优化指令
**⚠️ 字数扩展策略（目标：从{len(base_content)}字扩展到1500-2000字）**

**✅ 推荐的扩写方式：**
1. **逻辑性强化扩写**：补充事件发生的原因和过程说明，增加人物行为的逻辑依据
2. **语言通俗化扩写**：用更多短句和简单句替换复杂表达，增加日常化词汇
3. **句式结构优化扩写**：学习原文样本的句式节奏，保持短句为主
4. **情节连贯性扩写**：补充章节间的过渡和衔接内容，完善人物关系描述

**❌ 避免的扩写方式：**
- 不要增加无意义的环境细节描写
- 不要添加冗长的人物心理独白
- 不要为了凑字数而描写无关紧要的动作
- 不要使用华丽词藻和复杂修辞手法

**🎯 扩写重点：**
1. **逻辑完善**：确保情节发展的逻辑性和合理性，解释清楚因果关系
2. **语言优化**：让表达更加通俗易懂，符合原文样本的语言特征
3. **四要求检查**：每个新增部分都必须符合四个强制要求

### 🚫 严格禁止事项
1. 不要改变第一批次确定的人物名称
2. 不要改变核心情节走向
3. 不要添加与第一批次矛盾的设定
4. 不要使用过于华丽的辞藻
5. 不要为了融入维度而强行添加不合适的内容
6. **绝对不能违反四个强制要求**

### 📝 第二批次优化要求
请基于第一批次的基础框架，进行深化优化，生成完整的章节内容。
优化应该是在原框架基础上的自然扩展和改进，而不是重新创作。
**必须严格执行四个强制要求，不得有任何松懈！**

⚠️ 重要提醒：四个强制要求在第二批次中同样是不可妥协的底线！

请开始第二批次的内容深化优化：
"""

        try:
            # 使用原始生成方法调用API进行内容深化
            enhanced_content = TestService._generate_chapter_content_original(
                prompt=enhancement_prompt,
                prompt_template=prompt_template
            )

            logger.info(f"第二批次内容深化完成，长度: {len(enhanced_content)}")

            # 验证第二批次内容的质量和长度
            if not TestService._validate_batch_content(enhanced_content, 2, min_length=800):
                logger.error("第二批次内容深化优化失败：内容质量不符合要求")
                return base_content  # 失败时返回第一批次结果

            return enhanced_content

        except Exception as e:
            logger.error(f"第二批次内容深化失败: {str(e)}")
            return base_content  # 失败时返回第一批次结果

    @staticmethod
    def _execute_third_batch_detail_refinement(enhanced_content: str, novel: Novel, chapter: Chapter,
                                             book_analysis: Dict[str, Dict[str, str]],
                                             chapter_analysis: Dict[str, Dict[str, str]],
                                             prompt_template: str = "default") -> str:
        """
        第三批次：细节完善精炼（基于第二批次结果）

        专注于：
        1. 最终质量检查和细节完善
        2. 确保所有维度的和谐统一
        3. 优化语言流畅性和可读性
        4. 进行最终的逻辑性验证
        """
        logger.info("✨ 第三批次开始：基于第二批次结果进行细节完善精炼")

        # 检查第二批次内容的质量
        quality_issues = TestService._analyze_content_quality_issues(enhanced_content)

        # 构建第三批次精炼提示词（最终强化四个强制要求，确保承接第二批次结果）
        refinement_prompt = f"""
# ✨ 第三批次任务：基于第二批次结果的细节完善精炼

## ⚠️ 重要提醒：必须基于第二批次结果进行精炼，不是重新写作！

### 🎯 第三批次核心任务
**你的任务是对第二批次的内容进行细节完善和精炼，而不是重新创作！**

## 📋 第二批次深化内容（必须在此基础上精炼）
以下是第二批次深化的内容，你必须在此基础上进行细节完善和精炼：

```
{enhanced_content}
```

## 🚨 四个强制执行要求（最终检查，必须100%达标）
**在第三批次最终精炼第二批次内容时，以下四点必须达到完美执行：**

1. **直接套用原文样本的句式结构、语言特征**
   - 最终检查：是否完美模仿了原文样本的语言风格
   - 精炼调整：确保每个句子都符合原文样本特征
   - 绝对禁止：任何偏离原文样本语言模式的表达

2. **更偏向于短句、日常化通俗口语化**
   - 最终检查：是否所有句子都简洁明了
   - 精炼调整：将任何复杂句子改为短句
   - 绝对禁止：文绉绉或过于书面化的表达

3. **要有十分严密的逻辑性、因果联系**
   - 最终检查：每个情节是否都有清晰的因果关系
   - 精炼调整：修复任何逻辑漏洞或矛盾
   - 绝对禁止：任何突兀或缺乏逻辑的情节

4. **特别注意生成内容章节之间的联系与继承**
   - 最终检查：是否与前序章节完美连贯
   - 精炼调整：确保人物设定和情节发展一致
   - 绝对禁止：任何与前文矛盾的内容

## ✨ 第三批次具体精炼要求
**重要：你必须保持第二批次的所有核心内容，只是在此基础上进行细节完善和精炼！**

## 🎯 第三批次精炼目标

### 1. 最终质量检查
1. **逻辑一致性检查**：确保情节发展逻辑合理，无矛盾之处
2. **人物一致性验证**：确保人物性格、对话风格前后一致
3. **语言流畅性优化**：消除语言表达中的生硬之处
4. **细节完善**：补充必要的细节，删除冗余内容
5. **四要求达标检查**：确保四个强制要求100%达标

### 2. 15个维度的最终统一
确保所有维度特色和谐统一，不生硬：

#### 最终语言风格检查（必须符合四要求）
- 确保全文语言风格统一
- 消除过于书面化的表达
- 优化句式的自然性

#### 人物关系最终确认（必须符合四要求）
- 验证人物关系的一致性
- 确保对话符合人物身份
- 检查人物行为的合理性

#### 节奏感最终调整（必须符合四要求）
- 优化章节整体节奏
- 平衡紧张与舒缓的节奏
- 确保阅读体验流畅

### 3. 发现的质量问题
{TestService._format_quality_issues(quality_issues)}

### 4. 精炼要求
1. **保持内容完整性**：不要删除重要情节
2. **优化表达方式**：让语言更加自然流畅
3. **完善细节描写**：适当增加必要的细节
4. **确保逻辑严密**：修复任何逻辑漏洞
5. **四要求完美执行**：确保四个强制要求100%达标

### 🚫 最终禁止事项
1. 不要改变已确定的情节结构
2. 不要修改人物名称和基本设定
3. 不要添加新的主要情节线
4. 不要使用过于复杂的表达
5. **绝对不能违反四个强制要求**

### 🔍 最终检查清单
在输出前，必须逐一检查：
□ 是否完美模仿了原文的句式和语言特征？
□ 是否全部使用短句和通俗口语化表达？
□ 是否每个情节都有严密的逻辑和因果关系？
□ 是否与前序章节完全连贯一致？

### 📝 第三批次精炼要求
请基于第二批次的深化内容，进行最终的细节完善和精炼，
生成高质量的完整章节内容。
**必须严格执行四个强制要求，确保100%达标！**

⚠️ 最终警告：四个强制要求是质量底线，任何一项不达标都不能输出！

请开始第三批次的细节完善精炼：
"""

        try:
            # 使用原始生成方法调用API进行细节精炼
            final_content = TestService._generate_chapter_content_original(
                prompt=refinement_prompt,
                prompt_template=prompt_template
            )

            logger.info(f"第三批次细节精炼完成，长度: {len(final_content)}")

            # 验证第三批次内容的质量和长度
            if not TestService._validate_batch_content(final_content, 3, min_length=1000):
                logger.error("第三批次细节精炼失败：内容质量不符合要求")
                return enhanced_content  # 失败时返回第二批次结果

            return final_content

        except Exception as e:
            logger.error(f"第三批次细节精炼失败: {str(e)}")
            return enhanced_content  # 失败时返回第二批次结果

    @staticmethod
    def _validate_batch_content(content: str, batch_number: int, min_length: int = 500) -> bool:
        """
        验证批次内容的质量和长度 - 修复版本

        Args:
            content: 生成的内容
            batch_number: 批次编号（1、2、3）
            min_length: 最小长度要求

        Returns:
            True如果内容符合要求，False如果不符合
        """
        if not content or not isinstance(content, str):
            logger.warning(f"第{batch_number}批次内容为空或格式错误")
            return False

        # 检查内容长度 - 放宽第一批次的长度要求
        content_length = len(content.strip())

        # 第一批次大幅放宽长度要求，因为是基础框架
        if batch_number == 1:
            actual_min_length = max(50, min_length * 0.2)  # 第一批次最少50字符，或要求长度的20%
        else:
            actual_min_length = min_length

        if content_length < actual_min_length:
            logger.warning(f"第{batch_number}批次内容长度不足：{content_length}字符，要求至少{actual_min_length}字符")
            return False

        # 特别检查21字符的无效内容
        if content_length <= 21:
            logger.error(f"第{batch_number}批次检测到21字符或更短的无效内容：{content.strip()}")
            return False

        # 检查是否是错误信息
        error_indicators = [
            "生成内容失败",
            "API调用失败",
            "请求超时",
            "服务器错误",
            "内部错误",
            "网络错误",
            "连接失败",
            "API返回的数据格式不正确",
            "无法从API响应中提取",
            "API响应异常",
            "状态码",
            "HTTP错误"
        ]

        for indicator in error_indicators:
            if indicator in content:
                logger.warning(f"第{batch_number}批次内容包含错误信息：{indicator}")
                return False

        # 检查是否是提示词内容而不是写作结果 - 放宽判断
        if TestService._is_prompt_content_strict(content):
            logger.warning(f"第{batch_number}批次返回的是提示词内容而不是写作结果")
            return False

        # 检查是否包含基本的写作元素 - 放宽要求
        has_chapter_title = bool(re.search(r'#\s*第\d+章|第\d+章|章节\d+', content))
        has_substantial_content = len(content.strip()) > actual_min_length * 0.6  # 降低实质内容要求

        # 检查是否包含实际的故事内容 - 扩大检测范围
        story_indicators = [
            r'[。！？]',  # 中文标点
            r'[.!?]',     # 英文标点
            r'".*?"',     # 对话
            r'".*?"',     # 中文引号对话
            r'说道?[:：]',  # 说话动作
            r'[走跑看听想看到听到感到]',  # 动作词
            r'[他她它][们]?[的地得]',  # 人称代词
            r'[这那][个些]',  # 指示词
            r'[时候时间地方]',  # 时空词
            r'[突然忽然马上立刻]',  # 时间副词
        ]

        has_story_content = any(re.search(pattern, content) for pattern in story_indicators)

        # 第一批次放宽章节标题要求
        if not has_chapter_title and batch_number == 1:
            # 检查是否有其他形式的标题或开头
            alternative_titles = [
                r'^\s*[第一二三四五六七八九十\d]+[章节回]',
                r'^\s*Chapter\s*\d+',
                r'^\s*[A-Za-z\u4e00-\u9fa5]{2,10}[：:]',  # 简单标题
                r'^\s*[A-Za-z\u4e00-\u9fa5]{2,20}$',  # 单行标题
            ]
            has_alternative_title = any(re.search(pattern, content, re.MULTILINE) for pattern in alternative_titles)

            if not has_alternative_title:
                logger.info(f"第{batch_number}批次内容缺少标准章节标题，但可能是有效的基础框架")
                # 不直接返回False，继续检查其他条件

        if not has_substantial_content:
            logger.warning(f"第{batch_number}批次内容实质性内容不足")
            return False

        if not has_story_content:
            logger.warning(f"第{batch_number}批次内容缺少实际的故事内容")
            return False

        # 检查内容是否过于重复
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        if len(non_empty_lines) < 5:
            logger.warning(f"第{batch_number}批次内容行数过少：{len(non_empty_lines)}行")
            return False

        # 检查是否包含明显的API错误响应
        api_error_patterns = [
            r'{"error"',
            r'"status":\s*\d+',
            r'"message":\s*"',
            r'HTTP/\d\.\d',
            r'Content-Type:',
            r'application/json'
        ]

        for pattern in api_error_patterns:
            if re.search(pattern, content):
                logger.warning(f"第{batch_number}批次内容包含API错误响应格式")
                return False

        logger.info(f"第{batch_number}批次内容验证通过：长度{content_length}字符，符合质量要求")
        return True

    @staticmethod
    def _is_prompt_content_strict(content: str) -> bool:
        """
        更严格的提示词内容检测，减少误判

        Args:
            content: 要检测的内容

        Returns:
            True如果确定是提示词内容，False如果是写作结果或不确定
        """
        # 首先检查明确的写作结果特征
        writing_indicators = [
            r'^#\s*第\d+章',  # 章节标题开头
            r'第\d+章\s*[：:]',  # 章节标题格式
            r'^\s*第\d+章',  # 简单章节标题
            r'"[^"]*"[，。！？]',  # 对话内容
            r'[。！？]\s*[A-Za-z\u4e00-\u9fa5]{2,}说',  # 对话标签
            r'[A-Za-z\u4e00-\u9fa5]{2,}[走跑看听想]',  # 动作描述
            r'[他她它][们]?[走跑看听想]',  # 人物动作
            r'[突然忽然马上立刻][，。]',  # 时间副词
        ]

        # 如果包含明确的写作特征，直接判断为写作结果
        for indicator in writing_indicators:
            if re.search(indicator, content):
                logger.debug(f"检测到写作结果特征: {indicator}")
                return False

        # 检测明确的提示词特征（需要多个特征才判断为提示词）
        prompt_indicators = [
            '## 分析思路说明',
            '## 详细分析',
            '### 分析方法',
            '### 分析过程',
            '我需要分析以下',
            '让我来分析',
            '根据提供的文本进行',
            '基于以上分析结果',
            '通过分析可以看出',
            '综合以上各维度分析',
            '请分析以下文本的',
            '我将从以下维度进行分析',
            '# 九猫写作系统 - 核心写作指令',
            '## 📖 小说信息',
            '## 🎯 核心创作要求',
            '### 1. 语言风格要求',
            '### 2. 人物塑造要求',
        ]

        # 检查是否包含多个明确的提示词特征
        matches = 0
        for indicator in prompt_indicators:
            if indicator in content:
                matches += 1
                logger.debug(f"检测到提示词特征: {indicator}")

        # 需要至少3个明确特征才判断为提示词内容（提高阈值）
        if matches >= 3:
            logger.info(f"检测到 {matches} 个明确提示词特征，判断为提示词内容")
            return True

        # 检查内容长度 - 只有非常短的内容才可能是提示词片段
        if len(content) < 100:
            logger.info("内容长度过短，可能是提示词片段或错误内容")
            return True

        # 默认判断为写作结果（减少误判）
        return False

    @staticmethod
    def _diagnose_batch_content_failure(content: str, batch_number: int) -> str:
        """
        诊断批次内容验证失败的具体原因

        Args:
            content: 失败的内容
            batch_number: 批次编号

        Returns:
            诊断结果字符串
        """
        diagnosis = []

        if not content:
            diagnosis.append("内容为空")
            return "诊断结果：" + "；".join(diagnosis)

        content_length = len(content.strip())
        diagnosis.append(f"内容长度：{content_length}字符")

        # 检查长度要求
        if batch_number == 1:
            min_length = max(50, 500 * 0.2)  # 与验证逻辑保持一致
        else:
            min_length = 500

        if content_length < min_length:
            diagnosis.append(f"长度不足（要求至少{min_length}字符）")

        # 检查是否包含错误信息
        error_indicators = [
            "生成内容失败", "API调用失败", "请求超时", "服务器错误",
            "内部错误", "网络错误", "连接失败", "API返回的数据格式不正确"
        ]

        found_errors = []
        for indicator in error_indicators:
            if indicator in content:
                found_errors.append(indicator)

        if found_errors:
            diagnosis.append(f"包含错误信息：{', '.join(found_errors)}")

        # 检查是否被误判为提示词
        if TestService._is_prompt_content_strict(content):
            diagnosis.append("被识别为提示词内容")

        # 检查章节标题
        has_chapter_title = bool(re.search(r'#\s*第\d+章|第\d+章|章节\d+', content))
        if not has_chapter_title and batch_number == 1:
            diagnosis.append("缺少章节标题")

        # 检查故事内容
        story_indicators = [
            r'[。！？]', r'[.!?]', r'".*?"', r'".*?"',
            r'[走跑看听想看到听到感到]', r'[他她它][们]?[的地得]'
        ]
        has_story_content = any(re.search(pattern, content) for pattern in story_indicators)
        if not has_story_content:
            diagnosis.append("缺少故事内容特征")

        # 检查内容行数
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        if len(non_empty_lines) < 5:
            diagnosis.append(f"有效行数过少（{len(non_empty_lines)}行）")

        # 提供内容预览
        preview = content[:200] + "..." if len(content) > 200 else content
        diagnosis.append(f"内容预览：{preview}")

        return "诊断结果：" + "；".join(diagnosis)

    @staticmethod
    def _analyze_base_content_features(content: str) -> Dict:
        """分析第一批次基础内容的特点"""
        features = {
            "word_count": len(content),
            "paragraph_count": len(content.split('\n\n')),
            "dialogue_count": content.count('"') + content.count('"'),
            "has_characters": bool(re.search(r'[\u4e00-\u9fa5]{2,4}(?=说|道|想)', content)),
            "style_features": []
        }

        # 分析语言风格特征
        if len(content.split('。')) > len(content.split('，')) * 2:
            features["style_features"].append("短句为主")
        if features["dialogue_count"] > len(content) * 0.1:
            features["style_features"].append("对话丰富")

        return features

    @staticmethod
    def _analyze_content_quality_issues(content: str) -> List[str]:
        """分析内容质量问题"""
        issues = []

        # 检查常见问题
        if len(content) < 1000:
            issues.append("内容长度偏短，需要适当扩展")
        if content.count('"') < len(content) * 0.05:
            issues.append("对话偏少，建议增加人物互动")
        if re.search(r'突然.*?出现', content):
            issues.append("存在突兀的情节转折")
        if re.search(r'[\u4e00-\u9fa5]{10,}', content):
            issues.append("存在过长的句子，建议拆分")

        return issues

    @staticmethod
    def _format_quality_issues(issues: List[str]) -> str:
        """格式化质量问题"""
        if not issues:
            return "未发现明显质量问题，内容整体良好。"

        formatted = "发现以下需要改进的问题：\n"
        for i, issue in enumerate(issues, 1):
            formatted += f"{i}. {issue}\n"
        return formatted

    @staticmethod
    def _optimize_content_language_and_logic(content: str, optimization_round: int,
                                           logic_feedback: str, target_requirements: str) -> str:
        """
        对已生成的内容进行语言和逻辑优化（这里才是真正的优化位置）

        Args:
            content: 待优化的内容
            optimization_round: 优化轮次
            logic_feedback: 逻辑问题反馈
            target_requirements: 优化目标要求

        Returns:
            优化后的内容
        """
        logger.info(f"开始第{optimization_round}轮内容优化")

        # 构建优化提示词
        optimization_prompt = f"""
# 内容优化任务（第{optimization_round}轮）

## 🎯 优化目标
{target_requirements}

## 📝 待优化内容
{content}

## 🔍 逻辑问题反馈
{logic_feedback}

## 📋 优化要求

### 第{optimization_round}轮优化重点：
"""

        if optimization_round == 2:
            optimization_prompt += """
1. **语言表达优化**：
   - 保持原文的短句风格，避免长句和复杂从句
   - 使用生活化词汇，禁止华丽辞藻和过度修辞
   - 确保对话与叙述的自然衔接
   - 减少环境描写，专注于剧情推进

2. **逻辑性修复**：
   - 修复检测到的逻辑问题
   - 确保事件有完整的因果关系
   - 人物反应要真实自然
   - 避免突兀的情节转折

3. **15个维度自然融入**：
   - 根据实际内容需要自然应用各个维度
   - 不要为了体现某个维度而强行添加内容
   - 热梗等元素要符合人物身份和场景
   - 避免生硬套用和八股文式应用

4. **严格要求**：
   - 保持人物名称绝对一致
   - 保持剧情连贯性
   - 使用通俗口语化表达
   - 禁止编造前文未提及的情节
"""
        elif optimization_round == 3:
            optimization_prompt += """
1. **最终质量检查**：
   - 确保章节连贯性和人物一致性
   - 检查语言流畅性和可读性
   - 验证15个维度的自然融入效果
   - 确保没有生硬套用现象

2. **细节完善**：
   - 完善细节描写，但不过度
   - 确保对话真实自然
   - 检查句式连贯性
   - 优化段落过渡

3. **最终验证**：
   - 人物名称是否保持一致
   - 剧情是否逻辑合理
   - 语言是否通俗易懂
   - 是否避免了AI化表达
"""

        optimization_prompt += f"""

## ⚠️ 严格禁止事项
1. 不要改变基本剧情结构和人物设定
2. 不要添加新的主要情节
3. 不要更改人物名称
4. 不要破坏原有的逻辑连贯性
5. 不要使用过于华丽的辞藻
6. 不要生硬套用15个维度

## 📤 输出要求
请直接输出优化后的完整章节内容，保持原有的章节标题格式。
优化应该是在原内容基础上的改进，而不是重新创作。

请开始优化：
"""

        try:
            # 使用API进行内容优化
            api_client = DeepSeekClient(model="deepseek-r1")
            optimized_content = api_client.analyze_text(
                optimization_prompt,
                "content_optimization",
                temperature=0.3  # 使用较低的温度确保稳定性
            )

            # 提取优化后的内容
            if isinstance(optimized_content, dict) and "content" in optimized_content:
                return optimized_content["content"]
            elif isinstance(optimized_content, str):
                return optimized_content
            else:
                logger.warning(f"第{optimization_round}轮优化返回格式异常，使用原内容")
                return content

        except Exception as e:
            logger.error(f"第{optimization_round}轮内容优化失败: {str(e)}")
            return content  # 优化失败时返回原内容

    @staticmethod
    def _analyze_logic_issues(content: str) -> List[str]:
        """
        分析内容中的逻辑性问题

        Args:
            content: 生成的内容

        Returns:
            逻辑问题列表
        """
        issues = []

        # 检查常见的逻辑问题
        logic_patterns = [
            (r'死亡预兆|前文未提及', "编造前文未提及的情节"),
            (r'突然.*?炸|突然.*?爆|突然.*?裂', "突发事件缺乏原因"),
            (r'量子|玄学|赛博|朋克', "使用了不符合剧情的词汇"),
            (r'——.*?——', "使用了破折号句式"),
            (r'如.*?般.*?如.*?般', "过度使用比喻修辞"),
            (r'系统.*?出现.*?但.*?没有.*?反应', "系统出现缺乏人物反应"),
        ]

        for pattern, description in logic_patterns:
            if re.search(pattern, content):
                issues.append(description)

        # 检查段落长度和结构
        paragraphs = content.split('\n\n')
        long_paragraphs = [p for p in paragraphs if len(p) > 300]
        if len(long_paragraphs) > len(paragraphs) * 0.3:
            issues.append("段落过长，影响阅读体验")

        # 检查对话与叙述的比例
        dialogue_count = content.count('"') + content.count('"') + content.count('"')
        if dialogue_count < len(content) * 0.1:
            issues.append("对话过少，缺乏人物互动")

        return issues

    @staticmethod
    def _format_logic_feedback(issues: List[str]) -> str:
        """
        格式化逻辑问题反馈

        Args:
            issues: 逻辑问题列表

        Returns:
            格式化的反馈信息
        """
        if not issues:
            return "内容逻辑性良好，无明显问题。"

        feedback = "发现以下逻辑性问题需要改进：\n"
        for i, issue in enumerate(issues, 1):
            feedback += f"{i}. {issue}\n"

        feedback += "\n请在下一轮优化中重点解决这些问题。"
        return feedback

    @staticmethod
    def _extract_chapter_continuity_data(content: str, chapter_number: int) -> Dict:
        """
        提取章节连贯性数据，用于下一章节继承

        优化策略：
        1. 提取核心连贯性要素，避免冗余信息
        2. 压缩存储，控制数据量增长
        3. 重点关注写作连贯性必需的信息

        Args:
            content: 章节内容
            chapter_number: 章节编号

        Returns:
            连贯性数据字典
        """
        continuity_data = {
            "chapter_number": chapter_number,
            "characters": [],
            "plot_points": [],
            "style_features": {},
            "hot_memes": [],
            "sentence_patterns": [],
            "dialogue_style": {},
            "scene_setting": "",
            "emotional_tone": "",
            "narrative_perspective": "",
            "chapter_ending": "",  # 新增：章节结尾状态
            "key_events": [],      # 新增：关键事件
            "compressed_summary": ""  # 新增：压缩摘要
        }

        # 提取人物名称（中文姓名模式）
        import re
        character_patterns = [
            r'[A-Za-z][a-z]+',  # 英文名
            r'[\u4e00-\u9fa5]{2,4}(?=说|道|想|看|听|感到|觉得|认为)',  # 中文名（动作前）
            r'(?<=")[\u4e00-\u9fa5]{2,4}(?=")',  # 对话中的称呼
        ]

        for pattern in character_patterns:
            matches = re.findall(pattern, content)
            continuity_data["characters"].extend(matches)

        # 去重并保留最常见的人物名
        from collections import Counter
        char_counter = Counter(continuity_data["characters"])
        continuity_data["characters"] = [name for name, _ in char_counter.most_common(5)]

        # 提取句式模式（前3个句子的结构）
        sentences = re.split(r'[。！？]', content)[:3]
        continuity_data["sentence_patterns"] = [
            len(s.strip()) for s in sentences if s.strip()
        ]

        # 提取对话风格
        dialogues = re.findall(r'"([^"]+)"', content)
        if dialogues:
            avg_dialogue_length = sum(len(d) for d in dialogues) / len(dialogues)
            continuity_data["dialogue_style"] = {
                "average_length": avg_dialogue_length,
                "count": len(dialogues),
                "style": "简洁" if avg_dialogue_length < 20 else "详细"
            }

        # 提取情感基调
        emotion_keywords = {
            "紧张": ["紧张", "焦虑", "担心", "害怕"],
            "轻松": ["轻松", "愉快", "开心", "笑"],
            "严肃": ["严肃", "认真", "庄重", "沉重"],
            "幽默": ["幽默", "搞笑", "有趣", "逗"]
        }

        for emotion, keywords in emotion_keywords.items():
            if any(keyword in content for keyword in keywords):
                continuity_data["emotional_tone"] = emotion
                break

        # 提取章节结尾状态（最后200字符）
        if len(content) > 200:
            continuity_data["chapter_ending"] = content[-200:].strip()
        else:
            continuity_data["chapter_ending"] = content.strip()

        # 提取关键事件（包含动作词的句子）
        action_keywords = ["发生", "出现", "决定", "发现", "遇到", "离开", "到达", "开始", "结束"]
        key_events = []
        sentences = re.split(r'[。！？]', content)
        for sentence in sentences:
            if any(keyword in sentence for keyword in action_keywords) and len(sentence.strip()) > 10:
                key_events.append(sentence.strip())
                if len(key_events) >= 3:  # 最多保留3个关键事件
                    break
        continuity_data["key_events"] = key_events

        # 生成压缩摘要（控制在300字以内）
        continuity_data["compressed_summary"] = TestService._generate_chapter_compressed_summary(content)

        return continuity_data

    @staticmethod
    def _generate_chapter_compressed_summary(content: str) -> str:
        """
        生成章节压缩摘要，用于连贯性传递

        策略：
        1. 提取关键句子（包含重要信息的句子）
        2. 保留人物对话的核心内容
        3. 压缩到300字以内
        4. 保持剧情连贯性

        Args:
            content: 章节完整内容

        Returns:
            压缩后的摘要
        """
        if not content or len(content) < 100:
            return content

        import re

        # 分割成句子
        sentences = re.split(r'[。！？]', content)
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 5]

        # 重要性评分
        important_sentences = []

        # 高优先级关键词（剧情推进相关）
        high_priority_keywords = [
            "决定", "发现", "突然", "终于", "原来", "没想到", "竟然",
            "开始", "结束", "离开", "到达", "遇到", "发生", "出现"
        ]

        # 中优先级关键词（人物和对话）
        medium_priority_keywords = [
            "说", "道", "想", "看", "听", "感到", "觉得", "认为", "明白"
        ]

        # 为每个句子评分
        for sentence in sentences:
            score = 0

            # 高优先级关键词加分
            for keyword in high_priority_keywords:
                if keyword in sentence:
                    score += 3

            # 中优先级关键词加分
            for keyword in medium_priority_keywords:
                if keyword in sentence:
                    score += 1

            # 对话内容加分
            if '"' in sentence:
                score += 2

            # 长度适中的句子加分
            if 10 <= len(sentence) <= 50:
                score += 1

            if score > 0:
                important_sentences.append((sentence, score))

        # 按分数排序，取前面的重要句子
        important_sentences.sort(key=lambda x: x[1], reverse=True)

        # 选择句子，控制总长度
        selected_sentences = []
        total_length = 0
        max_length = 300

        for sentence, score in important_sentences:
            if total_length + len(sentence) <= max_length:
                selected_sentences.append(sentence)
                total_length += len(sentence)
            else:
                break

        # 如果没有选中足够的句子，补充开头和结尾
        if len(selected_sentences) < 3:
            # 添加开头句子
            if sentences and sentences[0] not in [s for s, _ in important_sentences]:
                selected_sentences.insert(0, sentences[0])

            # 添加结尾句子
            if sentences and sentences[-1] not in [s for s, _ in important_sentences]:
                selected_sentences.append(sentences[-1])

        # 重新排序（尽量保持原文顺序）
        result_sentences = []
        for sentence in sentences:
            if sentence in selected_sentences:
                result_sentences.append(sentence)

        summary = "。".join(result_sentences)

        # 最终长度控制
        if len(summary) > max_length:
            summary = summary[:max_length-3] + "..."

        return summary

    @staticmethod
    def _is_prompt_content(content: str) -> bool:
        """
        检测内容是否是提示词而不是写作结果

        Args:
            content: 要检测的内容

        Returns:
            True如果是提示词内容，False如果是写作结果
        """
        # 首先检查明确的写作结果特征
        writing_indicators = [
            r'^#\s*第\d+章',  # 章节标题开头
            r'第\d+章\s*[：:]',  # 章节标题格式
            r'^\s*第\d+章',  # 简单章节标题
            r'"[^"]*"[，。！？]',  # 对话内容
            r'[。！？]\s*[A-Za-z\u4e00-\u9fa5]{2,}说',  # 对话标签
            r'[A-Za-z\u4e00-\u9fa5]{2,}[走跑看听想]',  # 动作描述
        ]

        # 如果包含明确的写作特征，直接判断为写作结果
        for indicator in writing_indicators:
            if re.search(indicator, content):
                logger.info(f"检测到写作结果特征: {indicator}")
                return False

        # 检测明确的提示词特征（更严格的判断）
        prompt_indicators = [
            'type.*batch_processing',
            'content.*根据您的要求',
            '## 分析思路说明',
            '## 详细分析',
            '### 分析方法',
            '### 分析过程',
            'reasoning_content.*:',
            'analysis_type.*:',
            'prompt_template.*:',
            '我需要分析以下',
            '让我来分析',
            '根据提供的文本进行',
            '基于以上分析结果',
            '通过分析可以看出',
            '综合以上各维度分析',
            '请分析以下文本的',
            '我将从以下维度进行分析'
        ]

        # 检查是否包含多个明确的提示词特征
        matches = 0
        for indicator in prompt_indicators:
            if re.search(indicator, content, re.IGNORECASE):
                matches += 1
                logger.debug(f"检测到提示词特征: {indicator}")

        # 如果匹配超过2个明确特征，判断为提示词内容
        if matches >= 2:
            logger.info(f"检测到 {matches} 个明确提示词特征，判断为提示词内容")
            return True

        # 检查内容长度
        if len(content) < 300:
            logger.info("内容长度过短，可能是提示词片段或错误内容")
            return True

        # 检查是否主要是分析性语言而非叙述性语言
        analysis_patterns = [
            '分析.*结果',
            '从.*维度.*来看',
            '可以看出.*特点',
            '具有.*特征',
            '表现出.*风格',
            '体现了.*特色'
        ]

        analysis_matches = 0
        for pattern in analysis_patterns:
            if re.search(pattern, content):
                analysis_matches += 1

        # 如果分析性语言过多，可能是分析结果而非写作结果
        if analysis_matches >= 3 and len(content) < 1000:
            logger.info(f"检测到 {analysis_matches} 个分析性语言特征，可能是分析结果")
            return True

        # 默认判断为写作结果
        logger.info("未检测到明确的提示词特征，判断为写作结果")
        return False

    @staticmethod
    def _extract_writing_result_from_response(response: dict) -> str:
        """
        从API响应中提取写作结果

        Args:
            response: API响应字典

        Returns:
            提取的写作结果，如果没有找到则返回空字符串
        """
        logger.info("开始从API响应中提取写作结果")

        # 尝试从不同字段提取写作结果（按优先级排序）
        possible_fields = [
            'content',  # 最常见的字段
            'generated_content',
            'writing_result',
            'chapter_content',
            'story_content',
            'novel_content',
            'text',
            'result',
            'output',
            'response'
        ]

        # 首先尝试直接字段
        for field in possible_fields:
            if field in response and response[field]:
                content = response[field]
                if isinstance(content, str):
                    logger.info(f"从字段 {field} 中找到内容，长度: {len(content)}")

                    # 检查内容长度是否异常短
                    if len(content) < 50:
                        logger.warning(f"字段 {field} 中的内容过短({len(content)}字符)，可能是错误响应: {content}")
                        continue

                    # 对于写作任务，要求更高的最小长度
                    if len(content) < 100:
                        logger.warning(f"字段 {field} 中的内容长度不足({len(content)}字符)，可能不是完整的写作结果")
                        # 但仍然检查是否是有效内容
                        if not TestService._is_prompt_content(content):
                            logger.warning(f"虽然内容较短，但被识别为写作结果，将返回: {content}")
                            return TestService._clean_writing_content(content)
                        else:
                            logger.warning(f"字段 {field} 中的短内容被识别为提示词，跳过")
                            continue

                    # 检查是否是写作结果而不是提示词
                    if not TestService._is_prompt_content(content):
                        logger.info(f"从字段 {field} 中提取到有效写作结果")
                        return TestService._clean_writing_content(content)
                    else:
                        logger.warning(f"字段 {field} 中的内容被识别为提示词，跳过")

        # 尝试从嵌套结构中提取
        nested_paths = [
            ['data'],
            ['result'],
            ['response'],
            ['output'],
            ['content']
        ]

        for path in nested_paths:
            current = response
            for key in path:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    current = None
                    break

            if isinstance(current, dict):
                for field in possible_fields:
                    if field in current and current[field]:
                        content = current[field]
                        if isinstance(content, str) and len(content) > 100:
                            logger.info(f"从嵌套路径 {'.'.join(path)}.{field} 中找到内容，长度: {len(content)}")

                            if not TestService._is_prompt_content(content):
                                logger.info(f"从嵌套路径 {'.'.join(path)}.{field} 中提取到有效写作结果")
                                return TestService._clean_writing_content(content)
                            else:
                                logger.warning(f"嵌套路径 {'.'.join(path)}.{field} 中的内容被识别为提示词，跳过")

        # 如果都没找到，尝试直接使用response中的字符串内容
        if isinstance(response, str) and len(response) > 100:
            logger.info("尝试直接使用响应字符串内容")
            if not TestService._is_prompt_content(response):
                logger.info("响应字符串内容被识别为有效写作结果")
                return TestService._clean_writing_content(response)

        logger.warning("无法从API响应中提取到有效的写作结果")
        logger.debug(f"API响应结构: {list(response.keys()) if isinstance(response, dict) else type(response)}")
        return ""

    @staticmethod
    def _clean_writing_content(content: str) -> str:
        """
        清理写作内容，移除不必要的前缀和后缀

        Args:
            content: 原始写作内容

        Returns:
            清理后的写作内容
        """
        # 移除常见的AI回复前缀
        prefixes_to_remove = [
            r'^好的[，。].*?[\n\r]+',
            r'^根据.*?要求[，。].*?[\n\r]+',
            r'^我将.*?[\n\r]+',
            r'^以下是.*?[\n\r]+',
            r'^这是.*?[\n\r]+',
            r'^```[\w]*\n',  # 代码块开始
            r'\n```$',       # 代码块结束
        ]

        cleaned_content = content
        for prefix in prefixes_to_remove:
            cleaned_content = re.sub(prefix, '', cleaned_content, flags=re.MULTILINE)

        # 移除开头和结尾的空白
        cleaned_content = cleaned_content.strip()

        # 确保章节标题格式正确
        if not re.match(r'^#?\s*第\d+章', cleaned_content):
            # 如果没有章节标题，尝试添加
            lines = cleaned_content.split('\n')
            if lines and len(lines[0]) < 50:  # 第一行可能是标题
                first_line = lines[0].strip()
                if '第' in first_line and '章' in first_line:
                    lines[0] = f"# {first_line}"
                    cleaned_content = '\n'.join(lines)

        return cleaned_content

    @staticmethod
    def _build_continuity_prompt_enhancement(previous_chapters: List[str],
                                           continuity_data: Dict,
                                           current_chapter_number: int,
                                           book_analysis: Dict) -> str:
        """
        构建章节连贯性提示词增强

        Args:
            previous_chapters: 前序章节内容列表
            continuity_data: 连贯性数据
            current_chapter_number: 当前章节编号
            book_analysis: 整本书分析结果

        Returns:
            连贯性提示词
        """
        if not previous_chapters or current_chapter_number <= 1:
            return ""

        # 提取热梗数据
        hot_memes = []
        if "hot_meme_statistics" in book_analysis:
            meme_content = book_analysis["hot_meme_statistics"].get("content", "")
            # 简单提取热梗关键词
            meme_keywords = re.findall(r'[\u4e00-\u9fa5]{2,6}(?=梗|热梗|网梗)', meme_content)
            hot_memes = meme_keywords[:3]  # 取前3个

        enhancement = f"""

## 🔗 章节连贯性继承指导（第{current_chapter_number}章）

### 📋 必须严格继承的要素
1. **人物名称一致性**（绝对不能改变）
   - 主要人物：{', '.join(continuity_data.get('characters', [])[:3])}
   - ⚠️ 警告：绝对禁止更改任何已出现的人物姓名！

2. **剧情连贯性**
   - 前章结尾状态：{previous_chapters[-1][-200:] if previous_chapters else "无"}
   - 本章必须自然承接上述情节发展

3. **对话风格继承**
   - 对话长度：{continuity_data.get('dialogue_style', {}).get('style', '自然')}
   - 对话频率：适中（每段适当穿插）

4. **情感基调延续**
   - 前章基调：{continuity_data.get('emotional_tone', '自然')}
   - 本章应保持或自然过渡

### 🎯 15个维度的自然融入指导

#### 语言风格维度
- 使用通俗口语化表达，避免文绉绉的书面语
- 多用短句，少用长句和复杂从句
- 例：说"他很生气"而不是"他怒火中烧"

#### 热梗统计维度（自然融入，不生硬）
{f"- 可适当融入的网络热梗：{', '.join(hot_memes)}" if hot_memes else "- 根据剧情需要，可自然融入当下流行的网络用语"}
- ⚠️ 注意：只在符合人物性格和剧情需要时使用，不要为了用而用

#### 句式变化维度
- 继承前章的句式节奏：{continuity_data.get('sentence_patterns', [])}
- 保持对话与叙述的自然交替

#### 人物关系维度
- 严格维护已建立的人物关系
- 人物性格和说话方式保持一致

#### 其他维度自然应用原则
- 世界构建：保持已设定的环境和背景
- 节奏节拍：与前章保持协调
- 视角变化：保持叙述视角的一致性
- 段落流畅：自然过渡，避免突兀跳转

### 🚫 严格禁止事项
1. 更改任何已出现的人物姓名
2. 编造前文未提及的背景设定
3. 突然改变人物性格或关系
4. 生硬套用热梗或网络用语
5. 为了体现某个维度而强行添加不合适的内容
6. 使用过于文艺或高级的表达方式

### ✅ 正确的维度应用方式
- 根据实际剧情需要自然融入各个维度
- 优先保证剧情连贯性和人物一致性
- 语言要接地气，符合网文读者习惯
- 热梗等元素要符合人物身份和场景

### 📝 本章写作重点
1. 从前章结尾自然承接
2. 保持人物名称和性格一致
3. 使用通俗易懂的语言
4. 适当融入相关维度特色（但不强求）
5. 确保剧情逻辑合理
"""

        return enhancement

    @staticmethod
    def _update_writing_progress(task_id: str, chapter_number: int, current_text: str):
        """
        更新写作进度

        Args:
            task_id: 任务ID
            chapter_number: 章节编号
            current_text: 当前生成的文本
        """
        if task_id in test_tasks:
            # 计算当前文本的字数
            word_count = len(current_text) if current_text else 0

            # 更新任务状态
            test_tasks[task_id]["status"] = f"正在写作第 {chapter_number} 章: {word_count}字"

            # 记录日志
            if word_count % 100 == 0 and word_count > 0:  # 每100字记录一次日志，避免日志过多
                logger.info(f"写作进度更新: 第 {chapter_number} 章已生成 {word_count} 字")

    @staticmethod
    def _generate_chapter_content_with_retry(prompt: str, callback=None, prompt_template: str = "default",
                                           max_retries: int = 3, chapter_number: int = 1) -> str:
        """
        带重试机制的章节内容生成方法，提高连续性写作的稳定性

        Args:
            prompt: 提示词
            callback: 回调函数
            prompt_template: 提示词模板
            max_retries: 最大重试次数
            chapter_number: 章节编号

        Returns:
            生成的章节内容
        """
        import time

        for attempt in range(max_retries):
            try:
                logger.info(f"[连续性写作优化] 开始生成第{chapter_number}章内容，尝试 {attempt + 1}/{max_retries}")

                # 为章节生成添加延迟，避免API服务器过载
                if attempt > 0:
                    delay = attempt * 10  # 10秒、20秒、30秒
                    logger.info(f"[连续性写作优化] 第{chapter_number}章生成重试，等待 {delay} 秒")
                    time.sleep(delay)

                result = TestService._generate_chapter_content(
                    prompt=prompt,
                    callback=callback,
                    prompt_template=prompt_template
                )

                # 验证生成结果
                if result and len(result.strip()) > 100:  # 至少100字符
                    # 使用新的内容提取函数提取正确的章节内容
                    extracted_content = TestService._extract_chapter_content(result, chapter_number)
                    logger.info(f"[连续性写作优化] 第{chapter_number}章内容生成成功，原始长度: {len(result)}字符，提取后长度: {len(extracted_content)}字符")
                    return extracted_content
                else:
                    logger.warning(f"[连续性写作优化] 第{chapter_number}章内容生成结果过短或为空，尝试 {attempt + 1}/{max_retries}")

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"[连续性写作优化] 第{chapter_number}章内容生成失败，尝试 {attempt + 1}/{max_retries}，错误: {error_msg}")

                # 检查是否是服务器错误
                if "500" in error_msg or "服务器" in error_msg or "server" in error_msg.lower():
                    # 服务器错误时增加延迟
                    delay = (attempt + 1) * 15  # 15秒、30秒、45秒
                    logger.info(f"[连续性写作优化] 第{chapter_number}章检测到服务器错误，等待 {delay} 秒后重试")
                    time.sleep(delay)
                elif "timeout" in error_msg.lower() or "超时" in error_msg:
                    # 超时错误时增加延迟
                    delay = (attempt + 1) * 10  # 10秒、20秒、30秒
                    logger.info(f"[连续性写作优化] 第{chapter_number}章检测到超时错误，等待 {delay} 秒后重试")
                    time.sleep(delay)
                elif "max_tokens" in error_msg.lower() or "token" in error_msg.lower():
                    # Token相关错误时减少max_tokens重试
                    logger.info(f"[连续性写作优化] 第{chapter_number}章检测到token错误，将在重试时调整参数")
                    time.sleep(5)
                else:
                    # 其他错误时短暂延迟
                    delay = (attempt + 1) * 5  # 5秒、10秒、15秒
                    logger.info(f"[连续性写作优化] 第{chapter_number}章其他错误，等待 {delay} 秒后重试")
                    time.sleep(delay)

                # 最后一次尝试失败时返回错误信息
                if attempt == max_retries - 1:
                    logger.error(f"[连续性写作优化] 第{chapter_number}章内容生成最终失败，已重试 {max_retries} 次")
                    return f"第{chapter_number}章内容生成失败: {error_msg}"

        # 理论上不会到达这里
        return f"第{chapter_number}章内容生成失败，已重试 {max_retries} 次"

    @staticmethod
    def _generate_chapter_content_with_batch_optimization(prompt: str, callback=None, prompt_template: str = "default") -> str:
        """
        使用分批次优化策略生成章节内容，充分利用DeepSeek R1思考能力
        """
        logger.info("🚀 启用分批次优化策略生成章节内容")

        try:
            from src.api.batch_prompt_optimizer import BatchPromptOptimizer
            from src.api.deepseek_client import DeepSeekClient

            # 检查提示词长度，决定是否使用分批次策略
            if len(prompt) > 3000:  # 超过3000字符使用分批次策略
                logger.info(f"提示词长度 {len(prompt)} 字符，启用分批次优化策略")

                # 优化提示词为分批次执行
                batches = BatchPromptOptimizer.optimize_long_prompt_for_deepseek_r1(
                    base_prompt=prompt,
                    analysis_type="writing"
                )

                # 创建API客户端
                api_client = DeepSeekClient(model="deepseek-r1")

                # 执行分批次序列
                result = BatchPromptOptimizer.execute_batch_sequence(
                    batches=batches,
                    api_client=api_client,
                    delay_between_batches=2  # 2秒延迟确保API稳定
                )

                logger.info(f"分批次优化策略完成，生成内容长度: {len(result)}")
                return result

            else:
                # 短提示词直接处理，但仍然添加思考激活
                logger.info(f"提示词长度 {len(prompt)} 字符，使用增强单批次策略")
                thinking_prompt = BatchPromptOptimizer._build_thinking_activation_prompt()
                enhanced_prompt = thinking_prompt + "\n\n" + prompt

                return TestService._generate_chapter_content_original(
                    prompt=enhanced_prompt,
                    callback=callback,
                    prompt_template=prompt_template
                )

        except Exception as e:
            logger.error(f"分批次优化策略失败: {str(e)}，回退到原始方法")
            return TestService._generate_chapter_content_original(
                prompt=prompt,
                callback=callback,
                prompt_template=prompt_template
            )

    @staticmethod
    def _generate_chapter_content(prompt: str, callback=None, prompt_template: str = "default") -> str:
        """
        智能选择生成章节内容的方法

        根据提示词长度和复杂度，自动选择使用分批次优化策略或原始方法
        """
        try:
            # 检查提示词长度和复杂度
            if len(prompt) > 2500 or prompt.count('\n') > 20:
                logger.info(f"检测到复杂提示词（长度: {len(prompt)}），使用分批次优化策略")
                return TestService._generate_chapter_content_with_batch_optimization(
                    prompt=prompt,
                    callback=callback,
                    prompt_template=prompt_template
                )
            else:
                logger.info(f"使用原始生成方法（长度: {len(prompt)}）")
                return TestService._generate_chapter_content_original(
                    prompt=prompt,
                    callback=callback,
                    prompt_template=prompt_template
                )
        except Exception as e:
            logger.error(f"智能生成方法选择失败: {str(e)}，回退到原始方法")
            return TestService._generate_chapter_content_original(
                prompt=prompt,
                callback=callback,
                prompt_template=prompt_template
            )

    @staticmethod
    def _generate_chapter_content_original(prompt: str, callback=None, prompt_template: str = "default") -> str:
        """
        原始的生成章节内容方法（作为分批次优化的备选方案）

        Args:
            prompt: 提示词
            callback: 回调函数，用于更新生成进度
            prompt_template: 提示词模板，默认为 default

        Returns:
            生成的章节内容
        """
        # 添加调试日志
        logger.info(f"开始生成章节内容 - 提示词模板: {prompt_template}")
        logger.info(f"开始生成章节内容 - 提示词长度: {len(prompt) if prompt else 0}")
        logger.info(f"开始生成章节内容 - 提示词前100字符: {prompt[:100] if prompt else 'None'}")

        # 检查提示词是否为空
        if not prompt or prompt.strip() == "":
            logger.error("生成章节内容失败: 传入的提示词为空")
            return "生成内容失败: 提示词为空"

        # 创建字数追踪器
        word_count_tracker = None
        try:
            # 使用默认模型
            model = "deepseek-r1"

            # 尝试从任务中获取模型（但不覆盖传入的prompt_template参数）
            try:
                for _, task in test_tasks.items():
                    if task.get("status", "").startswith("正在写作第"):
                        model = task.get("model", "deepseek-r1")
                        # 不覆盖传入的prompt_template参数，保持用户选择
                        if prompt_template == "default":  # 只有在默认值时才从任务中获取
                            prompt_template = task.get("prompt_template", "default")
                        break
            except Exception as e:
                logger.warning(f"获取任务模型时出错: {str(e)}，使用默认值: model={model}, prompt_template={prompt_template}")

            # 创建API客户端，使用指定的模型
            api_client = DeepSeekClient(model=model)
            logger.info(f"生成章节内容使用模型: {model}，提示词模板: {prompt_template}")

            # 模拟生成过程中的进度更新
            if callback:
                # 初始化空内容
                callback("")

            # 添加一些热梗提示和创新性提示
            current_year = datetime.now().year

            # 从提示词中提取目标字数，用于设置合适的max_tokens
            target_word_count = None
            try:
                # 查找字数要求
                word_count_match = re.search(r'章节长度应与原文章节相当，约(\d+)字', prompt)
                if word_count_match:
                    target_word_count = int(word_count_match.group(1))
                    logger.info(f"从提示词中提取到目标字数: {target_word_count}")
                else:
                    # 如果没有找到目标字数，使用默认值（调整为2000字，以剧情质量为主）
                    target_word_count = 2000  # 设置一个默认值，降低到2000字
                    logger.info(f"未找到目标字数，使用默认值: {target_word_count}字（已调整为2000字，优先保证剧情流畅连贯）")

                # 根据模型获取max_tokens限制
                model_config = config.SUPPORTED_MODELS.get(model, {})
                model_max_tokens = model_config.get("max_tokens", 12000)  # 默认值为12000

                # 针对通义千问-QVQ-Max模型的特殊处理
                if model == "qwen-qvq-max":
                    # 通义千问-QVQ-Max模型的max_tokens限制是8192，需要更保守的设置
                    # 考虑到提示词本身占用的tokens，为生成内容预留足够空间
                    if target_word_count > 0:
                        max_tokens = min(6000, int(target_word_count * 2.5))  # 使用更保守的比例
                    else:
                        max_tokens = 4000  # 默认值，避免除零错误
                    logger.info(f"通义千问-QVQ-Max模型：根据目标字数设置max_tokens: {max_tokens}，模型限制: {model_max_tokens}")
                else:
                    # 其他模型使用原有逻辑
                    if target_word_count > 0:
                        max_tokens = min(model_max_tokens, max(4000, int(target_word_count * 4)))
                    else:
                        max_tokens = 8000  # 默认值，避免除零错误
                    logger.info(f"根据目标字数设置max_tokens: {max_tokens}，模型限制: {model_max_tokens}")
            except Exception as e:
                # 如果提取出错，使用默认值（调整为2000字，以剧情质量为主）
                target_word_count = 2000  # 设置一个默认值，降低到2000字

                # 根据模型获取max_tokens限制
                model_config = config.SUPPORTED_MODELS.get(model, {})
                model_max_tokens = model_config.get("max_tokens", 12000)  # 默认值为12000

                # 针对通义千问-QVQ-Max模型的特殊处理
                if model == "qwen-qvq-max":
                    max_tokens = 6000  # 使用保守的设置
                    logger.warning(f"通义千问-QVQ-Max模型：提取目标字数时出错: {str(e)}，使用保守设置: max_tokens={max_tokens}")
                else:
                    max_tokens = model_max_tokens  # 使用模型允许的最大值
                    logger.warning(f"提取目标字数时出错: {str(e)}，使用默认值: {target_word_count}，max_tokens: {max_tokens}，模型限制: {model_max_tokens}")

            # 从提示词中提取章节编号（需要在使用前定义）
            chapter_number = 1  # 设置默认值
            try:
                # 查找章节编号
                chapter_number_match = re.search(r'你正在创作的是第(\d+)章', prompt)
                if chapter_number_match:
                    chapter_number = int(chapter_number_match.group(1))
                    logger.info(f"从提示词中提取到章节编号: {chapter_number}")
                else:
                    # 如果没有找到章节编号，尝试其他模式
                    chapter_number_match2 = re.search(r'第(\d+)章', prompt)
                    if chapter_number_match2:
                        chapter_number = int(chapter_number_match2.group(1))
                        logger.info(f"从提示词中提取到章节编号（模式2）: {chapter_number}")
                    else:
                        # 如果都没有找到，使用默认值
                        chapter_number = 1
                        logger.warning(f"未找到章节编号，使用默认值: {chapter_number}")
            except Exception as e:
                # 如果提取出错，使用默认值
                chapter_number = 1
                logger.warning(f"提取章节编号时出错: {str(e)}，使用默认值: {chapter_number}")

            # 获取当前章节编号
            current_chapter_number = chapter_number

            # 检查是否有前序章节
            has_previous_chapter = current_chapter_number > 1

            # 初始化字数追踪器
            try:
                word_count_tracker = WordCountTracker(target_word_count)
            except Exception as e:
                logger.warning(f"初始化字数追踪器失败: {str(e)}，继续执行")
                word_count_tracker = None

            # 添加前章框架继承信息（暂时使用空字典，后续可以传入实际的分析结果）
            previous_framework_info = ""
            if has_previous_chapter:
                # 这里应该传入实际的book_analysis和chapter_analysis，暂时使用空字典
                try:
                    previous_framework_info = TestService._build_previous_chapter_framework_inheritance(
                        current_chapter_number, {}, {}
                    )
                except Exception as e:
                    logger.warning(f"构建前章框架继承信息失败: {str(e)}，使用空字符串")
                    previous_framework_info = ""

            # 创建创新性写作指南
            creativity_prompt = f"""
## 创新性写作执行指南 - 基于完整分析和原文样本的故事重构

### 🎓 学习指导核心原则
**重要提醒：基于分析结果和推理过程+全部原文样本（学习句子结构/语言/15个维度，不复制情节）指导创作**

#### 📚 三层学习体系
1. **分析结果层**：理解每个维度的分析结论，作为创作的指导方向
2. **推理过程层**：学习分析的思维逻辑，应用到新内容的构思中
3. **原文样本层**：学习写作技巧和表达方式，但绝不复制情节内容

#### 🎯 学习与创新平衡
- ✅ **学习形式**：句子结构、语言风格、叙述技巧、15个维度的表现方式
- ✅ **创新内容**：全新的故事情节、人物关系、事件发展
- ❌ **禁止复制**：原文的具体情节、事件序列、对话内容

### 核心原则：脱胎换骨，风格传承

1. **情节完全创新**：
   - 绝对禁止模仿原文的情节发展路径
   - 必须构建与原文完全不同的故事主线
   - 运用你的创造力，构思独特的故事类型和发展方向
   - 情节发展要有独立的逻辑性和创新性

2. **场景环境重构**：
   - 完全更换故事发生的时间、地点、背景
   - 自由创造全新的环境设定，与原文形成鲜明对比
   - 环境描写要体现原文的描写风格，但内容完全不同

3. **人物关系创新**：
   - 创造全新的人物名称和身份设定
   - 自主设计与原文不同的人物关系网络
   - 人物互动方式要学习原文的风格，但关系内容全新

4. **冲突设置重构**：
   - 设计与原文完全不同的矛盾冲突
   - 根据你的故事需要，自由选择冲突类型和发展方式
   - 冲突解决方式要体现原文的处理风格

### 维度应用的创新性重组：

**语言风格传承**：学习原文的表达习惯、语言节奏、修辞特色，但用于描述全新的内容
**结构特色应用**：学习原文的章节架构、段落组织方式，但构建全新的故事结构
**节奏控制学习**：掌握原文的节奏变化规律，但应用到不同的情节发展中
**人物塑造技巧**：学习原文的人物刻画方法，但创造全新的人物形象
**世界构建理念**：学习原文的背景设定思路，但创造全新的故事世界
**情感表达方式**：学习原文的情感渲染技巧，但表达不同的情感内容

### 具体执行要求：

1. **字数控制**：目标{target_word_count}字（已调整为2000字，以剧情流畅连贯为主），容忍范围{int(target_word_count*0.80)}至{int(target_word_count*1.20)}字
2. **章节编号**：必须为"# 第{chapter_number}章 [全新标题]"
3. **人物命名**：创造有特色但不过于奇怪的新人名
4. **开头设计（重点强化）**：与原文有明显差异但同样吸睛的开场方式
   - 必须在前200字内建立强烈的阅读欲望
   - 可使用：冲突开场、悬念开场、对话开场、动作开场等技巧
   - 避免平淡的环境描写或背景介绍开场
   - 开篇要有戏剧张力，让读者想继续阅读
5. **情节主线**：与原文完全不同的故事发展路径
6. **语言风格**：保持原文的语言特色但内容全新
7. **时代背景**：可以适当融入{current_year}年的时代元素

### 严格禁止事项：

1. **禁止情节模仿**：严禁使用原文的任何情节发展脉络
2. **禁止场景复制**：严禁使用原文的具体场景和事件
3. **禁止人名重复**：严禁使用原文中的任何人物名称
4. **禁止直接引用**：严禁直接引用原文的任何段落或句子
5. **题材完全自由**：可以创作任何题材的故事（古代、现代、未来、玄幻、仙侠、都市、校园、职场等），根据原文风格和你的创意自由选择
6. **禁止AI化表达**：严禁使用以下AI化的表达方式：
   - 过度的暗喻和象征（如"钥匙齿痕与条形码重合"等莫名其妙的暗喻）
   - 过度修辞和华丽辞藻
   - 不自然的巧合和神秘元素
   - 过于复杂的比喻和排比句
   - 故作深沉的哲理性表达
   - 不符合日常逻辑的奇怪描述
7. **逻辑性和合理性要求**：
   - 任何事件的出现都必须有合理的原因和解释
   - 人物的行为必须符合逻辑，有明确的目的和动机
   - 避免莫名其妙的动作（如"用咖啡画阴阳鱼"等无意义行为）
   - 每个情节都要对故事发展有实际作用
   - 人物对重要事件的反应要真实自然
8. **通俗生活化要求**：
   - 避免使用专业术语和生僻词汇（如量子、基因、算法等）
   - 禁止使用玄学词汇（如玄学、风水、法器、往生、殡葬等）
   - 避免奇怪的描述组合（如"殡葬部设计往生法器宣传册"等别扭表达）
   - 使用日常生活中常见的表达方式
   - 描述要贴近现实生活，易于理解
   - 人物对话要自然，符合日常交流习惯
   - **使用现代网文句式风格**：简洁直接，避免复杂从句和过度修辞
   - **禁止奇怪句式结构**：如"突然想起...的...——这..."等不符合网文风格的句式
   - **严格禁止破折号**：绝对不要使用破折号（——）连接句子
   - **禁止编造前文内容**：不要提及前文没有的人物、事件、物品等
9. **逻辑性和因果关系强化要求**：
   - **任何事物出现都要有明确原因**：为什么会出现？是什么导致的？
   - **详细描述出现过程**：怎么出现的？有什么征兆或前奏？
   - **明确实际作用和影响**：对剧情有什么推动作用？对人物有什么影响？
   - **人物反应必须真实**：看到、听到、感受到时的具体反应和情绪变化
   - **后续影响要写清楚**：这件事对后续剧情有什么影响？
   - **避免无意义内容**：每个情节、对话、行为都要对故事发展有实际作用
   - **因果链条完整**：原因→过程→结果→影响，形成完整的逻辑链条
   - **杜绝突然出现**：任何新元素都不能突然出现，必须有铺垫和解释

### 创作目标：

创作出一个让读者感受到"这个作者的写作风格很像原文作者，但故事内容完全不同，很有创意和新意"的全新章节。

### 质量检验标准：

- 风格相似度：语言表达、节奏控制、情感渲染等方面与原文相似
- 内容创新度：情节、人物、场景、冲突等方面与原文完全不同
- 逻辑连贯性：新故事的发展逻辑清晰合理
- 字数准确性：符合目标字数范围
- 章节连贯性：与前后章节（如有）保持人物和情节的一致性

现在开始创作，展现"学我者生"的创新精神，创造一个全新的故事！
"""

            # 添加创新性章节框架规划步骤，强调脱胎换骨的创新
            planning_prompt = f"""
## 创新性章节框架规划 - 基于维度分析的故事重构

在开始写作之前，请先详细规划本章节的创新性框架，必须完成以下所有步骤：

{previous_framework_info}

### 第一步：故事主线创新规划
1. **完全脱离原文情节**：确保本章节的故事主线与原文完全不同
2. **自由创造故事类型**：基于你的创造力和想象力，构思一个与原文截然不同的故事方向，不要局限于任何预设类型
3. **独立思考核心冲突**：深入思考什么样的矛盾冲突能够与原文形成鲜明对比，同时又能承载原文的风格特色
4. **自主规划情节发展**：运用你的创作智慧，设计一条独特的、有逻辑的故事发展路径
{"5. 重要：本章节是第" + str(current_chapter_number) + "章，必须与前一章节的新故事线保持连贯" if has_previous_chapter else ""}

### 第二步：场景环境重构规划
1. **完全更换故事背景**：运用你的想象力，创造与原文截然不同的时间、地点、环境设定
2. **自由构思场景设定**：不要局限于任何预设选项，根据你的新故事需要，创造独特的场景环境
3. **场景功能设计**：让每个场景都能自然地服务于你构思的新故事情节
4. **创新场景转换**：设计符合新故事逻辑的场景过渡方式
5. **环境描写创新**：学习原文的描写技巧和风格特色，但用来描述你创造的全新环境

### 第三步：人物创新规划
1. **创造全新人物**：运用你的创造力，设计与原文完全不同的人物角色
2. **自由设定人物身份**：根据你构思的新故事，自主决定人物的身份、职业、背景等，不要受任何预设选项限制
3. **人物命名创新（网络小说命名法）**：
   - 绝对避免使用原文中的任何人物名称
   - 请自行学习和研究男频、女频网络小说的人物命名规律和特点
   - 根据你创作的故事类型和人物性格，创造符合网络小说风格的人物名称
   - 名字要有网络小说的韵味，既不要太普通也不要太奇怪
   - 可以灵活运用单姓、复姓等不同形式
4. **独创人物关系网络**：设计与原文完全不同的人物关系结构
5. **人物性格创新**：学习原文的人物刻画技巧，但创造全新的性格特点和人物深度
{"6. 重要：必须与前一章节的人物设定保持一致，确保故事连贯性" if has_previous_chapter else ""}
7. **互动方式创新**：学习原文的对话风格和互动技巧，但应用到你创造的新人物关系中

### 第四步：维度创新性重组规划（基于15个维度的分析结果进行创新应用）

**核心原则：学习精髓，创新应用**

1. **语言风格传承**：
   - 学习原文的表达习惯、语言节奏、修辞特色
   - 将这些语言特点应用到全新的内容和场景中
   - 保持语言的韵味，但描述完全不同的事物

2. **结构特色重组**：
   - 学习原文的章节架构、段落组织方式
   - 将结构特点应用到新的故事框架中
   - 保持结构的美感，但承载全新的内容

3. **节奏控制创新**：
   - 掌握原文的节奏变化规律和情节推进速度
   - 将节奏控制技巧应用到不同的情节发展中
   - 保持节奏的韵律，但推进全新的故事

4. **人物关系重构**：
   - 学习原文的人物互动模式和关系发展方式
   - 将互动技巧应用到全新的人物关系网络中
   - 保持关系的深度，但创造全新的人物

5. **世界构建革新**：
   - 学习原文的背景设定思路和世界观构建方法
   - 将构建理念应用到全新的故事世界中
   - 保持世界的丰富性，但创造全新的背景

6. **情感表达转化**：
   - 学习原文的情感渲染技巧和氛围营造方法
   - 将表达技巧应用到不同的情感内容中
   - 保持情感的深度，但表达全新的感受

7. **开篇技巧应用（重点强化）**：
   - 深度学习原文的开场方式和吸引力技巧
   - 分析原文开篇的节奏、悬念、人物引入等技巧
   - 将开篇技巧应用到全新的故事开头，确保开篇足够吸睛
   - 开篇必须在前200字内抓住读者注意力
   - 可以使用冲突开场、对话开场、悬念开场等技巧
   - 避免平淡的环境描写开场，要有戏剧性
   - 保持开篇的吸引力，但开启全新的故事

8. **高潮设计借鉴**：
   - 学习原文的高潮设计和冲突处理方式
   - 将设计技巧应用到不同的冲突和高潮中
   - 保持高潮的张力，但处理全新的冲突

9. **风格特色融合**：
   - 学习原文的独特风格和写作特点
   - 将风格特色融入到全新的故事表达中
   - 保持风格的独特性，但表达全新的内容

10. **句式技巧运用**：
    - 学习原文的句式多样性和表达技巧
    - 将句式技巧运用到新的内容表达中
    - 保持句式的丰富性，但表达全新的意思

11. **视角处理创新**：
    - 学习原文的叙述视角和视角转换技巧
    - 将视角技巧应用到新的故事叙述中
    - 保持视角的灵活性，但叙述全新的故事

12. **段落流畅度保持**：
    - 学习原文的段落转换和连接方式
    - 将流畅度技巧应用到新的内容组织中
    - 保持段落的流畅性，但组织全新的内容
    - 重点学习对话与叙述之间的自然过渡技巧
    - 确保句式连贯，避免跳脱式叙述

### 第五步：创新性结构与节奏规划
1. **新故事的起承转合**：为全新的故事情节设计完整的结构
2. **节奏变化创新**：学习原文的节奏控制技巧，但应用到新的情节发展中
3. **高潮设计重构**：设计与原文不同但同样精彩的高潮部分
4. **结构层次创新**：保持原文的结构美感，但承载全新的内容
{"5. 新故事的连贯性：确保本章节与前一章节的新故事线保持连贯" if has_previous_chapter else ""}

### 第六步：字数分配与控制规划
1. **目标字数**：{target_word_count}字（已调整为2000字，以剧情流畅连贯为主），容忍范围{int(target_word_count*0.80)}至{int(target_word_count*1.20)}字
2. **结构分配**：
   - 开头部分：约{int(target_word_count*0.15)}字（新故事的开场）
   - 中间发展部分：约{int(target_word_count*0.6)}字（新情节的展开）
   - 结尾部分：约{int(target_word_count*0.25)}字（新故事的收尾）
3. **实时追踪**：在写作过程中使用注释标记字数进度
4. **风格比例**：学习原文的内容分配比例，但应用到新内容中

### 第七步：创新性语言风格规划
1. **语言风格传承**：学习原文的表达习惯和语言特色
2. **叙述视角创新**：选择适合新故事的叙述视角
3. **对话与描写平衡**：学习原文的比例，但用于新的内容
4. **句式技巧应用**：运用原文的句式变化技巧
{"5. 风格一致性：与前一章节的语言风格保持一致" if has_previous_chapter else ""}
6. **通俗易懂原则**：使用简单句和短句，避免复杂的从句结构
7. **时代元素融入**：适当加入{current_year}年的流行元素
8. **反AI化表达**：严禁使用AI化的表达方式：
   - 不要写莫名其妙的暗喻（如"钥匙齿痕与条形码重合"）
   - 不要过度使用修辞手法和华丽辞藻
   - 不要制造不自然的巧合和神秘元素
   - 使用自然、生活化的语言表达
   - 避免故作深沉的哲理性描述
9. **句式连贯性要求**：
   - 对话和叙述之间必须有自然的过渡，不能突然跳跃
   - 环境描写要适度，不要过多，且要与情节自然融合
   - 段落之间要有逻辑连接，避免跳脱式叙述
   - 学习原文样本的句式流畅性和段落过渡技巧
   - 确保整体叙述的连贯性和流畅性
10. **逻辑性和合理性强化**：
    - 任何新出现的元素（如系统、道具、人物等）都要有合理的出现原因
    - 人物的每个行为都要有明确的目的和动机
    - 避免无意义的动作和描述（如"用咖啡画阴阳鱼"等）
    - 重要事件要有前因后果，人物反应要真实自然
    - 每个情节都要推动故事发展，不能为了写而写
11. **通俗生活化表达**：
    - 使用简单易懂的日常用语，避免专业术语（量子、基因、算法等）
    - 禁止使用玄学词汇（玄学、风水、法器、往生、殡葬等）
    - 避免奇怪的描述组合（如"殡葬部设计往生法器宣传册"等别扭表达）
    - 描述要贴近现实生活，让读者容易理解
    - 人物对话要自然，符合日常交流方式
    - 避免过于文艺或晦涩的表达
    - **使用现代爆火网文的句式风格**：简洁、直接、易读
    - **禁止复杂句式结构**：避免"突然想起...的...——这..."等奇怪句式
    - **避免过度修辞**：少用破折号、省略号等复杂标点，保持句式简洁
12. **逻辑性和因果关系强化**：
    - **完整的因果链条**：任何事件都要有"原因→触发→过程→结果→影响"的完整链条
    - **明确出现原因**：新事物为什么会出现？是什么条件触发的？
    - **详细出现过程**：怎么出现的？有什么前兆？过程是怎样的？
    - **具体功能作用**：这个事物有什么具体功能？能解决什么问题？
    - **真实人物反应**：人物的惊讶、困惑、恐惧、兴奋等具体情绪和行为
    - **身体感受描述**：对人物身体、心理的具体影响和变化
    - **后续剧情影响**：这件事对后续故事发展有什么推动作用？
    - **实际意义明确**：每个情节都要对整体故事有实际推进作用
    - **避免无效内容**：杜绝为了凑字数而添加的无意义情节
    - **逻辑自洽性**：确保所有内容在逻辑上自洽，没有矛盾

### 第八步：创新性连贯规划
1. **新故事的连贯性**：确保新故事情节的逻辑连贯
2. **人物一致性**：保持新人物的名称和性格一致
3. **风格连贯性**：在语言风格上保持与原文的相似性
4. **情感发展连贯**：确保新故事的情感发展合理
{"5. 章节间衔接：确保与前一章节的新故事线自然衔接" if has_previous_chapter else ""}
6. **质量检验**：检查是否达到"学我者生"的创新标准

### 最终创作要求：

**必须完成以上所有规划步骤，形成一个详细的创新性章节框架，再开始写作。**

**核心目标**：创作出一个在风格上传承原文精髓，但在内容上完全创新的全新章节。

**质量标准**：读者看后会说"这个作者的风格很像原文，但故事完全不同，很有创意"。

**重要提醒**：章节框架规划仅用于指导写作过程，不应包含在最终输出的内容中。最终输出应该只包含章节标题和正文，不包含任何规划内容。
"""

            # 将规划提示和创新性提示添加到原始提示的末尾
            enhanced_prompt = prompt + "\n" + planning_prompt + "\n" + creativity_prompt

            # 添加字数实时追踪提示
            word_count_tracking_prompt = f"""
在写作过程中，请实时追踪字数：

1. 每写完一个主要段落，检查一下当前总字数，并在段落后用注释标记当前字数，例如：<!-- 当前字数：1200字 -->
2. 如果发现字数增长过快，及时调整后续内容的详略程度
3. 如果发现字数增长过慢，适当增加细节描写
4. 在接近目标字数的90%时，开始规划结尾
5. 确保最终字数在目标范围内：{int(target_word_count*0.80)}至{int(target_word_count*1.20)}字之间（已放宽范围，优先保证剧情质量）
6. 注意：字数统计不包括章节框架规划部分，只计算实际的章节内容
7. 在写作过程中，可以使用以下格式实时记录字数：<!-- 当前进度：约X字，目标{target_word_count}字，完成度Y% -->
8. 这些字数注释将在最终内容处理时自动移除，不会影响最终输出
9. 系统会实时分析你的字数，并在需要时提供反馈，请注意查看反馈信息并相应调整内容

请在写作过程中时刻关注字数控制，确保最终字数符合要求。如果收到字数反馈，请立即按照建议调整内容。
"""



            # 创建分段执行的指导指令（第一阶段：基础要求）
            stage1_instructions = f"""
【第一阶段执行指令 - 基础创作要求】

请首先严格执行以下基础要求：

1. 人物名称连贯性：
   - 必须创建全新的人物名称，严禁使用"姜晚"或原文中的任何人名
   - 一旦确定人物名称，在整个章节中必须保持完全一致，禁止中途更改
   - 人物性格和行为模式必须与其名称和身份设定保持一致

2. 章节过渡自然性：
   - 本章节开头必须与前一章节结尾自然衔接，避免突兀的场景跳转
   - 时间、地点、情境的转换要合理流畅，有明确的逻辑关系
   - 人物情感状态的变化要有合理的过渡过程，不能突然改变性格

3. 开头差异：开头部分必须与原文明显不同，但保持风格一致
4. 情节创新：必须创建全新情节，避免与原文情节重叠
5. 章节编号：必须使用正确的章节编号"# 第{chapter_number}章 [标题]"
6. 章节编号验证：绝对禁止使用第{chapter_number + 1}章、第{chapter_number - 1}章或任何其他编号
7. HTML标签禁止：严禁在标题中使用<h1>、<h2>、<h3>等HTML标签，只能使用Markdown的#格式
8. 字数参考：目标字数约{target_word_count}字（已调整为2000字），剧情质量绝对优先，宁可字数少也不要为了凑字数而影响内容流畅性

请确认您已理解并将严格执行以上第一阶段要求。
"""

            # 第二阶段：内容质量要求
            stage2_instructions = f"""
【第二阶段执行指令 - 内容质量要求】

在完成第一阶段要求的基础上，请严格执行以下内容质量要求：

9. 题材完全自由：可以创作任何题材（古代、现代、未来、玄幻、仙侠、都市、校园、职场等），根据原文风格自由选择
10. 语言通俗：使用通俗易懂的语言，不要使用大量的比喻、排比等修辞手法
11. 反AI化表达：严禁使用AI化的表达方式，如莫名其妙的暗喻、过度修辞、不自然的巧合等
12. 人物命名：自行学习男频女频网络小说命名法，创造符合网络小说风格的人物名称
13. 句式连贯性：对话和叙述之间必须自然过渡，减少环境描写，确保整体流畅性
14. 学习原文流畅性：重点学习原文样本的句式连贯、段落过渡、对话衔接等技巧
15. 逻辑性要求：任何事件出现都要有合理原因，人物行为要有明确动机，避免无意义动作
16. 通俗生活化：使用日常用语，避免专业术语（量子、基因、算法）和玄学词汇（玄学、风水、法器、往生、殡葬），描述贴近现实生活

请确认您已理解并将严格执行以上第二阶段要求。
"""

            # 第三阶段：高级质量要求
            stage3_instructions = f"""
【第三阶段执行指令 - 高级质量要求】

在完成前两阶段要求的基础上，请严格执行以下高级质量要求：

17. 禁止奇怪表达：避免别扭的描述组合（如"殡葬部设计往生法器宣传册"等不自然表达）
18. 因果关系完整：新事物出现要有解释，人物反应要真实，详细描述功能作用和影响
19. 现代网文句式：使用简洁直接的句式，避免复杂从句和奇怪结构（如"突然想起...的...——这..."）
20. 严格禁止破折号：绝对不要使用破折号（——）连接句子，保持句式简洁易读
21. 禁止编造内容：不要提及前文没有的人物、事件、物品，确保逻辑一致性
22. 完整因果链条：任何事件都要有"原因→触发→过程→结果→影响"的完整逻辑链条
23. 实际作用明确：每个情节、对话、行为都要对故事发展有实际推动作用，杜绝无意义内容
24. 人物反应真实：对任何事件的反应要具体、真实、符合人物性格和情境

在开始写作前，必须先完成详细的章节框架规划，特别注意：
- 确认主要人物的名称和基本特征（必须保持一致）
- 规划与前序章节的自然衔接方式
- 设计合理的情节发展和人物互动

在写作过程中，必须实时检查：
- 人物名称是否保持一致
- 章节过渡是否自然流畅
- 人物行为是否符合其性格设定

我将严格检查以上所有要求的执行情况，如果有任何一条未执行，内容将被拒绝。
"""

            # 合并所有阶段的指令
            critical_instructions = stage1_instructions + "\n\n" + stage2_instructions + "\n\n" + stage3_instructions

            # 将所有提示组合在一起，将最重要的指令放在最前面
            final_prompt = critical_instructions + "\n\n" + enhanced_prompt + "\n" + word_count_tracking_prompt

            # 添加强制执行提示词的指令（增强版）
            execution_reminder = f"""
【最终执行检查 - 必须逐项确认】

在提交内容前，请逐项确认你已完全执行了所有要求：

✅ 基础要求检查：
1. 使用了全新的人物名称（不是姜晚或原文中的名字）
2. 开头部分与原文明显不同
3. 创建了全新情节，避免与原文重叠
4. 章节编号正确（第{chapter_number}章）
5. 字数在合理范围内（目标{target_word_count}字，剧情质量优先）
6. 不包含未来科技或赛博朋克内容
7. 使用通俗易懂的语言，避免大量的比喻、排比等修辞手法

✅ 连贯性要求检查：
8. 人物名称在整个章节中保持一致
9. 章节过渡自然流畅，与前章衔接合理
10. 人物性格和行为符合设定

✅ 质量要求检查：
11. 没有使用破折号（——）连接句子
12. 没有奇怪的表达和别扭的描述
13. 所有事件都有明确的因果关系
14. 人物反应真实自然
15. 内容逻辑自洽，没有矛盾

✅ 内容提取检查：
16. 最终输出只包含章节标题和正文
17. 没有包含任何规划内容或AI回复前缀
18. 内容格式正确，使用Markdown格式

如果发现任何一条未执行，请立即修改。只有全部确认后才能提交内容。
"""

            # 🎯 AI指令理解增强（不限制版本，保证质量）
            # 获取全面的指令检查清单（保持完整，不简化）
            comprehensive_checklist = TestService._create_comprehensive_instruction_checklist()

            # 构建AI指令理解增强提醒（适用于所有版本）
            ai_enhancement_reminder = f"""

## 🧠 AI指令理解增强执行提醒

### 🎯 核心原则：写作功能完全不限制
**无论是精简版还是默认版，写作过程中完全不做token限制，只要保证质量！**

### 📋 完整指令检查清单
{comprehensive_checklist}

### 🚨 执行优先级说明
1. **内容质量第一**：一切以实际生成内容的质量为最终考量标准
2. **指令完整执行**：所有检查清单中的指令都必须深刻理解并全部执行
3. **不限制token使用**：写作过程中token随便用，专注质量提升
4. **分段微调配合**：本指令与后续的分段微调不冲突，各司其职
5. **协同增效**：检查清单确保理解，分段微调确保质量，两者协同工作

### 🔧 执行机制说明
- **当前阶段**：基础内容生成，重点执行检查清单中的所有要求
- **后续阶段**：如果内容达标，会进行分段学习指导微调进一步提升质量
- **资源分配**：检查清单和分段微调不争夺资源，各自专注不同阶段
- **质量保证**：两个机制协同工作，确保最终输出的内容质量最佳

### 🚨 最终执行提醒
- 写作功能完全不限制，请全力发挥创作能力
- 如有任何疑问，优先保证内容质量
- 确保每个指令都得到完整执行
- 所有写作相关的提示词都十分重要，必须深刻理解并全部执行
- 为后续的分段微调打好基础，确保内容质量达到最佳状态

请确认已完全理解并将严格执行所有指令后开始创作。
"""

            # 添加到最终提示词（所有版本都使用完整的检查清单）
            final_prompt = final_prompt + "\n" + execution_reminder + "\n" + ai_enhancement_reminder

            # 调用API生成内容 - 使用analyze_text方法
            # 传递提示词模板参数以支持精简版模式
            response = api_client.analyze_text(
                text=final_prompt,
                analysis_type="chapter_content_generation",  # 确保使用正确的分析类型
                max_tokens=max_tokens,
                prompt_template=prompt_template,  # 传递提示词模板参数
                temperature=0.5 if prompt_template == "simplified" else None  # 精简版使用较低的temperature
            )

            # 详细记录API响应用于调试
            logger.info(f"API响应类型: {type(response)}")
            if response:
                logger.info(f"API响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
                if isinstance(response, dict):
                    for key, value in response.items():
                        if isinstance(value, str):
                            logger.info(f"响应字段 {key}: {value[:200]}..." if len(value) > 200 else f"响应字段 {key}: {value}")
                        else:
                            logger.info(f"响应字段 {key}: {type(value)} - {str(value)[:100]}...")

            # 检查API响应是否包含错误
            if response and "error" in response:
                error_msg = response["error"]
                logger.error(f"API调用失败: {error_msg}")

                # 检查是否是超时错误
                if "Request timed out" in error_msg or "RequestTimeOut" in error_msg:
                    return f"生成内容失败: API请求超时，请稍后重试。建议使用精简版模式以提高成功率。"
                elif "状态码=500" in error_msg:
                    return f"生成内容失败: 服务器内部错误，请稍后重试。"
                elif "内容过短" in error_msg:
                    return f"生成内容失败: API返回内容过短，可能是API响应异常。详细错误: {error_msg}"
                else:
                    return f"生成内容失败: {error_msg}"

            # 检查响应是否为空或None
            if not response:
                logger.error("API返回空响应")
                return "生成内容失败: API返回空响应，请检查网络连接或API服务状态"

            # 检查响应是否为字典类型
            if not isinstance(response, dict):
                logger.error(f"API响应不是字典类型: {type(response)}, 内容: {str(response)[:500]}")
                return f"生成内容失败: API响应格式异常，期望字典类型但收到{type(response)}"

            # 提取生成的内容 - 增强版本
            content = ""

            # 方法1: 优先尝试从专门的写作结果提取函数获取内容
            try:
                content = TestService._extract_writing_result_from_response(response)
                if content:
                    logger.info(f"从专门提取函数获取到内容，长度: {len(content)}")
            except Exception as e:
                logger.warning(f"专门提取函数失败: {str(e)}")

            # 方法2: 如果专门函数没有提取到内容，尝试传统方式
            if not content and "content" in response:
                raw_content = response["content"]
                logger.info(f"从传统content字段获取到内容，长度: {len(raw_content)}")

                # 检查是否是提示词内容而不是写作结果
                if TestService._is_prompt_content(raw_content):
                    logger.warning("传统content字段中的内容被识别为提示词，尝试其他字段")
                    # 再次尝试从其他字段提取
                    try:
                        content = TestService._extract_writing_result_from_response(response)
                    except Exception as e:
                        logger.warning(f"二次提取失败: {str(e)}")
                else:
                    content = raw_content
                    logger.info("传统content字段中的内容被识别为有效写作结果")

            # 方法3: 尝试从其他可能的字段提取内容
            if not content:
                possible_content_fields = ["text", "result", "output", "data", "message", "response"]
                for field in possible_content_fields:
                    if field in response and response[field]:
                        potential_content = response[field]
                        if isinstance(potential_content, str) and len(potential_content.strip()) > 50:
                            logger.info(f"从字段 {field} 获取到潜在内容，长度: {len(potential_content)}")
                            # 检查是否是有效的写作内容
                            if not TestService._is_prompt_content(potential_content):
                                content = potential_content
                                logger.info(f"字段 {field} 中的内容被识别为有效写作结果")
                                break

            # 最终检查：如果仍然没有内容
            if not content:
                logger.error("无法从API响应中提取到任何写作内容")
                logger.error(f"完整API响应: {response}")

                # 使用诊断函数分析问题
                diagnosis = TestService._diagnose_api_response_issue(response)
                logger.error("=== API响应问题诊断 ===")
                logger.error(diagnosis)

                return "生成内容失败: 无法从API响应中提取到有效的写作结果。请检查API配置或联系技术支持。"

            # 检查提取到的内容长度是否合理
            content_length = len(content.strip())
            if content_length < 100:
                logger.error(f"提取到的内容过短: {content_length}字符")
                logger.error(f"内容预览: {content[:200]}")

                # 分析内容为什么过短
                if content_length <= 21:
                    logger.error("检测到21字符或更短的无效内容，这可能是API返回的错误信息或状态码")
                    return f"生成内容失败: API返回21字符的无效内容({content.strip()})，请检查API配置或重试"
                else:
                    return f"生成内容失败: API返回内容过短({content_length}字符)，可能是API响应异常"

            # 清理和处理内容
            processed_content = TestService._clean_writing_content(content)

            # 移除章节框架规划部分（如果存在）
            processed_content = TestService._remove_planning_content(processed_content)

            # 记录处理前后的字数差异
            original_length = len(content)
            processed_length = len(processed_content)
            if original_length != processed_length:
                logger.info(f"内容处理后，长度从 {original_length} 变为 {processed_length}，差异: {original_length - processed_length} 字符")
            else:
                logger.info(f"内容处理后长度未变化: {processed_length} 字符")

            # 使用准确的字数统计验证（温和模式，以剧情质量为主）
            actual_word_count = TestService._count_words_accurately(processed_content)
            # 放宽字数容忍范围：80%-120%，优先保证剧情流畅性
            min_words = int(target_word_count * 0.80)  # 从92%放宽到80%
            max_words = int(target_word_count * 1.20)  # 从108%放宽到120%

            logger.info(f"准确字数统计: {actual_word_count}字，目标字数: {target_word_count}字，容忍范围: {min_words}-{max_words}字")

            # 只在字数严重偏离时才进行调整，优先保证剧情质量
            if actual_word_count < min_words and actual_word_count < target_word_count * 0.70:
                # 字数不足，需要补充内容
                shortage = min_words - actual_word_count
                logger.info(f"字数不足，需要补充约{shortage}字")

                adjustment_prompt = f"""
当前内容的准确字数为{actual_word_count}字，目标字数为{target_word_count}字。字数偏少，但请严格以剧情质量为主。

【AI指令理解确认】请确认您已理解以下核心要求：
✓ 优先改善语言表达质量，而非增加无意义描写
✓ 重点优化句式流畅度，使用简短有力的生活化词汇，学习原文短句风格
✓ 绝对禁止华丽辞藻，绝对禁止为凑字数而添加拖沓的环境描写或无关内容
✓ 所有扩展必须基于现有内容的自然延伸，有明确逻辑依据

请在保持剧情完整性和逻辑性的前提下，适当扩展以下内容：

{processed_content}

扩展要求（剧情质量绝对优先）：
1. **剧情完整性第一**：确保故事情节完整自然，绝不为凑字数而破坏剧情逻辑
2. **优化语言表达**：重点改善句式流畅度，使用简短有力的生活化词汇，学习原文短句风格，避免华丽辞藻和无意义的环境描写
3. **自然情节延伸**：只在逻辑允许的情况下延伸情节，不强行添加无关内容
4. **保持故事节奏**：维持原有的故事节奏和氛围，不为字数而拖沓
5. **严格逻辑性**：所有扩展都要有逻辑依据，不编造前文没有的内容
6. **禁止破折号**：绝对不要使用破折号（——）连接句子
7. **现代网文风格**：使用简洁直接的句式，避免复杂从句
8. **因果关系明确**：任何新增内容都要有明确的前因后果

【语言优化核心策略】：
- **句式连贯性**：改善句子间的逻辑连接，减少突兀跳转，确保叙述流畅自然
- **词汇生活化**：使用简短有力、通俗易懂的生活化词汇，避免华丽辞藻和重复用词
- **语言风格统一**：严格学习原文的语言风格和句式特点，确保文体协调
- **对话自然度**：优化对话的真实感和自然度，符合人物性格特征
- **短句节奏感**：优先使用短句，学习原文的句式长短搭配，营造良好的阅读节奏

【严格禁止的扩展方式】：
❌ 添加无意义的环境描写（如"阳光透过窗户洒在地上"等拖沓描述）
❌ 增加冗长的心理活动描写（如大段内心独白）
❌ 添加无关的回忆或联想内容
❌ 为凑字数而重复描述同一事物
❌ 添加与剧情无关的细节描写

【推荐的优化方式】：
✓ 优化现有对话的表达方式，使其更自然流畅
✓ 改善句子间的连接，提升整体叙述的连贯性
✓ 使用简短有力的生活化词汇，避免华丽辞藻
✓ 学习原文的短句风格，优先使用短句表达
✓ 严格模仿原文的句式特点和语言节奏
✓ 确保语言风格与原文完全一致

重要提醒：
- 如果当前内容已经完整且流畅，宁可字数少也不要强行扩展
- 剧情质量和逻辑性比字数更重要
- 优先改善语言表达质量，而非增加无意义的描述
- 保持内容的可控性和连贯性

请输出完整的内容：
"""

                try:
                    adjustment_response = api_client.analyze_text(
                        text=adjustment_prompt,
                        analysis_type="chapter_content_adjustment",
                        max_tokens=max_tokens,
                        prompt_template=prompt_template,
                        temperature=0.5
                    )

                    if adjustment_response and "content" in adjustment_response:
                        adjusted_content = adjustment_response["content"]
                        processed_content = TestService._remove_planning_content(adjusted_content)
                        actual_word_count = TestService._count_words_accurately(processed_content)
                        logger.info(f"扩展后准确字数: {actual_word_count}字")
                except Exception as e:
                    logger.warning(f"字数调整失败: {str(e)}")

            elif actual_word_count > max_words and actual_word_count > target_word_count * 1.30:
                # 字数过多且严重超标（超过30%），需要适当精简
                excess = actual_word_count - target_word_count
                logger.info(f"字数过多且严重超标，需要适当精简约{excess}字")

                adjustment_prompt = f"""
当前内容的准确字数为{actual_word_count}字，目标字数为{target_word_count}字。字数偏多，但请优先保证剧情完整性。

请在保持剧情完整性和流畅性的前提下，适当精简以下内容：

{processed_content}

精简要求（剧情完整性优先）：
1. **保持剧情完整**：确保故事情节完整，不要为了减字数而破坏剧情结构
2. **保留核心内容**：保持所有重要的对话、关键情节和人物发展
3. **精简冗余描述**：可以适当精简过于详细的环境描述或重复性内容
4. **保持节奏感**：维持故事的节奏和氛围，不要让精简影响阅读体验
5. **逻辑连贯性**：确保精简后的内容逻辑连贯，没有突兀的跳跃
6. **人物一致性**：保持人物性格和行为的一致性

注意：如果当前内容虽然字数多但剧情完整流畅，宁可保持现状也不要强行精简。剧情质量比字数更重要。

请输出完整的内容：
"""

                try:
                    adjustment_response = api_client.analyze_text(
                        text=adjustment_prompt,
                        analysis_type="chapter_content_adjustment",
                        max_tokens=max_tokens,
                        prompt_template=prompt_template,
                        temperature=0.5
                    )

                    if adjustment_response and "content" in adjustment_response:
                        adjusted_content = adjustment_response["content"]
                        processed_content = TestService._remove_planning_content(adjusted_content)
                        actual_word_count = TestService._count_words_accurately(processed_content)
                        logger.info(f"精简后准确字数: {actual_word_count}字")
                except Exception as e:
                    logger.warning(f"字数调整失败: {str(e)}")

            # 最终字数验证（温和模式）
            final_word_count = TestService._count_words_accurately(processed_content)
            word_difference_percent = abs(final_word_count - target_word_count) / target_word_count * 100 if target_word_count > 0 else 0

            if word_difference_percent <= 30:
                logger.info(f"✅ 字数在合理范围内: {final_word_count}字（目标{target_word_count}字，差异{word_difference_percent:.1f}%）")
            else:
                logger.warning(f"⚠️ 字数偏差较大: {final_word_count}字（目标{target_word_count}字，差异{word_difference_percent:.1f}%），但已优先保证剧情质量")

            logger.info("📖 字数验证完成，已优先保证剧情流畅性和连贯性")

            # 使用字数追踪器分析内容
            if word_count_tracker:
                stats = word_count_tracker.update_content(processed_content)
                feedback = word_count_tracker.generate_feedback()

                # 记录字数统计信息
                logger.info(f"字数统计: {stats}")
                logger.info(f"字数反馈: {feedback}")

                # 如果字数不在目标范围内，添加反馈信息
                if feedback["status"] == "out_of_target":
                    processed_content += f"\n\n<!-- 字数反馈: {feedback['message']} -->"

                    # 如果有建议，添加到内容中
                    if feedback["suggestions"]:
                        suggestions = "\n".join([f"- {s}" for s in feedback["suggestions"]])
                        processed_content += f"\n\n<!-- 建议: \n{suggestions}\n -->"

            # 验证内容是否符合要求
            content_valid, validation_message = TestService._validate_content(processed_content, chapter_number, target_word_count)

            # 如果内容不符合要求，添加警告信息
            if not content_valid:
                logger.warning(f"生成的内容不符合要求: {validation_message}")
                processed_content = f"{processed_content}\n\n> **警告：** {validation_message}"

            # 后处理内容，确保不会直接复制原文，并尝试调整字数
            final_content = TestService._post_process_content(processed_content, target_word_count)

            # 如果有回调函数，更新最终内容
            if callback:
                callback(final_content)

            return final_content
        except Exception as e:
            logger.error(f"生成章节内容时出错: {str(e)}", exc_info=True)
            return f"生成内容失败: {str(e)}"

    @staticmethod
    def _diagnose_api_response_issue(response: Any) -> str:
        """
        诊断API响应问题，特别是21字符无效内容的根本原因

        Args:逐步接收
            response: API响应

        Returns:
            诊断结果和建议
        """
        diagnosis = []

        # 基本类型检查
        diagnosis.append(f"响应类型: {type(response)}")

        if response is None:
            diagnosis.append("问题: API返回None，可能的原因:")
            diagnosis.append("  1. 网络连接问题")
            diagnosis.append("  2. API服务器无响应")
            diagnosis.append("  3. 请求超时")
            diagnosis.append("建议: 检查网络连接，重试请求")

        elif isinstance(response, str):
            content_length = len(response.strip())
            diagnosis.append(f"响应长度: {content_length}字符")
            diagnosis.append(f"响应内容: '{response.strip()}'")

            if content_length <= 21:
                diagnosis.append("问题: 检测到21字符或更短的无效响应")
                diagnosis.append("可能的原因:")
                diagnosis.append("  1. API密钥错误或过期")
                diagnosis.append("  2. API配置错误（URL、模型名称等）")
                diagnosis.append("  3. API服务器返回错误状态码")
                diagnosis.append("  4. 请求格式不正确")
                diagnosis.append("  5. API配额已用完")

                # 分析具体内容
                if "401" in response or "Unauthorized" in response:
                    diagnosis.append("具体问题: 认证失败，请检查API密钥")
                elif "403" in response or "Forbidden" in response:
                    diagnosis.append("具体问题: 访问被拒绝，请检查API权限")
                elif "404" in response or "Not Found" in response:
                    diagnosis.append("具体问题: API端点不存在，请检查URL配置")
                elif "429" in response or "Rate Limit" in response:
                    diagnosis.append("具体问题: 请求频率过高，请稍后重试")
                elif "500" in response or "Internal Server Error" in response:
                    diagnosis.append("具体问题: 服务器内部错误，请稍后重试")
                else:
                    diagnosis.append("具体问题: 未知的短响应，可能是API配置问题")

        elif isinstance(response, dict):
            diagnosis.append(f"响应字段: {list(response.keys())}")

            # 检查错误字段
            if "error" in response:
                diagnosis.append(f"错误信息: {response['error']}")
                diagnosis.append("问题: API返回错误响应")

            # 检查content字段
            if "content" in response:
                content = response["content"]
                if isinstance(content, str):
                    content_length = len(content.strip())
                    diagnosis.append(f"content字段长度: {content_length}字符")

                    if content_length <= 21:
                        diagnosis.append("问题: content字段包含21字符或更短的无效内容")
                        diagnosis.append(f"content内容: '{content.strip()}'")

            # 检查其他可能的内容字段
            content_fields = ["text", "result", "output", "data", "message"]
            for field in content_fields:
                if field in response:
                    field_value = response[field]
                    if isinstance(field_value, str):
                        field_length = len(field_value.strip())
                        diagnosis.append(f"{field}字段长度: {field_length}字符")
                        if field_length <= 21:
                            diagnosis.append(f"问题: {field}字段包含短内容: '{field_value.strip()}'")

        else:
            diagnosis.append(f"问题: 意外的响应类型 {type(response)}")
            diagnosis.append("建议: 检查API客户端实现")

        return "\n".join(diagnosis)

    @staticmethod
    def _analyze_dimension_with_retry(analyzer, text: str, dimension: str, title: str,
                                    prompt_template: str, temperature: float = None,
                                    max_retries: int = 3) -> Dict[str, Any]:
        """
        带重试机制的维度分析方法，提高连续性写作的稳定性

        Args:
            analyzer: 分析器实例
            text: 要分析的文本
            dimension: 分析维度
            title: 标题
            prompt_template: 提示词模板
            temperature: 温度参数
            max_retries: 最大重试次数

        Returns:
            分析结果
        """
        import time

        for attempt in range(max_retries):
            try:
                logger.info(f"[连续性写作优化] 开始分析维度 {dimension}，尝试 {attempt + 1}/{max_retries}")

                result = analyzer.analyze_dimension(
                    text=text,
                    dimension=dimension,
                    title=title,
                    prompt_template=prompt_template,
                    temperature=temperature
                )

                # 验证结果
                if result and "content" in result:
                    logger.info(f"[连续性写作优化] 维度 {dimension} 分析成功")
                    return result
                else:
                    logger.warning(f"[连续性写作优化] 维度 {dimension} 分析结果格式不正确，尝试 {attempt + 1}/{max_retries}")

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"[连续性写作优化] 维度 {dimension} 分析失败，尝试 {attempt + 1}/{max_retries}，错误: {error_msg}")

                # 检查是否是服务器错误
                if "500" in error_msg or "服务器" in error_msg or "server" in error_msg.lower():
                    # 服务器错误时增加延迟
                    delay = (attempt + 1) * 5  # 5秒、10秒、15秒
                    logger.info(f"[连续性写作优化] 检测到服务器错误，等待 {delay} 秒后重试")
                    time.sleep(delay)
                elif "timeout" in error_msg.lower() or "超时" in error_msg:
                    # 超时错误时增加延迟
                    delay = (attempt + 1) * 3  # 3秒、6秒、9秒
                    logger.info(f"[连续性写作优化] 检测到超时错误，等待 {delay} 秒后重试")
                    time.sleep(delay)
                else:
                    # 其他错误时短暂延迟
                    delay = attempt + 1  # 1秒、2秒、3秒
                    logger.info(f"[连续性写作优化] 其他错误，等待 {delay} 秒后重试")
                    time.sleep(delay)

                # 最后一次尝试失败时抛出异常
                if attempt == max_retries - 1:
                    logger.error(f"[连续性写作优化] 维度 {dimension} 分析最终失败，已重试 {max_retries} 次")
                    raise e

        # 理论上不会到达这里
        raise Exception(f"维度 {dimension} 分析失败，已重试 {max_retries} 次")

    @staticmethod
    def _build_previous_chapter_framework_inheritance(current_chapter_number: int, book_analysis: Dict, chapter_analysis: Dict) -> str:
        """
        构建前章框架继承信息，确保章节间的连贯性

        Args:
            current_chapter_number: 当前章节编号
            book_analysis: 整本书分析结果
            chapter_analysis: 章节分析结果

        Returns:
            前章框架继承信息字符串
        """
        if current_chapter_number <= 1:
            return ""

        previous_chapter_number = current_chapter_number - 1

        # 构建前章框架继承信息
        inheritance_info = f"""
## 前章框架继承与连贯性分析（第{previous_chapter_number}章 → 第{current_chapter_number}章）

### 重要：必须严格遵循前章设定，确保连贯性

1. **人物名称和形象继承**：
   - 必须使用与第{previous_chapter_number}章完全相同的人物名称
   - 人物性格特征必须与前章保持一致
   - 人物关系发展必须基于前章的基础上自然演进
   - 禁止随意更改人物设定或引入与前章矛盾的人物特征

2. **情节自然过渡要求**：
   - 本章开头必须与第{previous_chapter_number}章结尾自然衔接
   - 时间线必须连贯，避免突兀的时间跳跃
   - 场景转换必须有合理的逻辑关系
   - 情感氛围的变化要有自然的过渡过程

3. **15个维度连贯性检查**：
   - 语言风格：保持与前章一致的表达方式和语言特色
   - 节奏节拍：确保节奏变化与前章形成良好衔接
   - 结构分析：章节结构要与整体结构协调
   - 人物关系：基于前章的人物关系基础上发展
   - 世界构建：保持世界观设定的一致性
   - 情感基调：情感发展要有连贯性和合理性

4. **每两章检查机制**：
   {"- 本章是第" + str(current_chapter_number) + "章，" + ("需要与第" + str(previous_chapter_number) + "章进行全面连贯性检查" if current_chapter_number % 2 == 0 else "将与下一章一起进行检查，请为下一章的连贯性做好准备")}
   - 检查要点：人物名称一致性、情节自然过渡、语言风格连贯、情感发展合理、节奏协调性

### 前章关键信息提取（基于分析结果）：
"""

        # 如果有具体的分析结果，提取关键信息
        if book_analysis and isinstance(book_analysis, dict):
            # 提取语言风格信息
            if "language_style" in book_analysis:
                inheritance_info += f"""
- **语言风格特征**：{book_analysis.get('language_style', {}).get('analysis_result', '需要保持一致的语言表达方式')}
"""

            # 提取人物关系信息
            if "character_relationships" in book_analysis:
                inheritance_info += f"""
- **人物关系模式**：{book_analysis.get('character_relationships', {}).get('analysis_result', '需要保持人物关系的连贯发展')}
"""

            # 提取情感基调信息
            if "emotional_tone" in book_analysis:
                inheritance_info += f"""
- **情感基调特征**：{book_analysis.get('emotional_tone', {}).get('analysis_result', '需要保持情感表达的连贯性')}
"""

        inheritance_info += f"""

### 本章创作指导原则：
1. **严格继承前章设定**：所有人物、情节、世界观设定必须与前章保持一致
2. **自然过渡发展**：在前章基础上自然发展，避免突兀变化
3. **15个维度全面应用**：确保所有维度都与前章保持连贯性
4. **质量与连贯并重**：在保证内容质量的同时，确保与前章的无缝衔接

---
"""

        return inheritance_info

    @staticmethod
    def _validate_content(content: str, chapter_number: int, target_word_count: int) -> Tuple[bool, str]:
        """
        验证生成的内容是否符合要求

        Args:
            content: 生成的内容
            chapter_number: 章节编号
            target_word_count: 目标字数

        Returns:
            (是否符合要求, 验证消息)
        """
        issues = []

        # 检查章节编号是否正确
        chapter_title_pattern = rf'# 第{chapter_number}章'
        if not re.search(chapter_title_pattern, content):
            issues.append(f"章节编号不正确，应为'# 第{chapter_number}章'")

        # 检查是否包含"姜晚"这个名字
        if "姜晚" in content:
            issues.append("内容包含'姜晚'这个名字，应使用全新的人物名称")

        # 检查是否包含HTML标签
        html_tag_pattern = r'<h[1-6][^>]*>.*?</h[1-6]>'
        if re.search(html_tag_pattern, content, re.IGNORECASE):
            issues.append("内容包含HTML标题标签（如<h1>、<h2>等），应使用Markdown格式的#标题")

        # 检查章节编号是否错误（如第2章、第0章等）
        wrong_chapter_patterns = [
            rf'第{chapter_number + 1}章',
            rf'第{chapter_number - 1}章' if chapter_number > 1 else None,
            rf'第0章',
            rf'第{chapter_number + 2}章',
            rf'第{chapter_number - 2}章' if chapter_number > 2 else None
        ]
        for pattern in wrong_chapter_patterns:
            if pattern and re.search(pattern, content):
                issues.append(f"内容包含错误的章节编号'{pattern}'，应使用'第{chapter_number}章'")

        # 检查是否包含莫名其妙的行为描述
        strange_behaviors = [
            r'用.*画.*阴阳鱼',
            r'.*齿痕.*条形码.*重合',
            r'突然.*系统.*出现',
            r'莫名.*获得.*能力',
            r'神秘.*力量.*觉醒'
        ]
        for pattern in strange_behaviors:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"内容包含莫名其妙的行为描述，缺乏逻辑性和合理性")

        # 检查是否使用了不合适的词汇和表达
        inappropriate_terms = [
            # 科技类词汇
            r'量子', r'基因', r'DNA', r'RNA', r'蛋白质',
            r'算法', r'数据结构', r'机器学习', r'人工智能',
            r'区块链', r'加密货币', r'虚拟现实',
            # 玄学类词汇
            r'玄学', r'风水', r'八卦', r'阴阳', r'五行',
            r'法器', r'往生', r'殡葬', r'超度', r'轮回',
            # 奇怪的组合词汇
            r'宣传册.*精准度', r'表盘.*落点', r'设计.*法器',
            r'殡葬.*设计', r'往生.*宣传', r'法器.*表盘'
        ]

        for term in inappropriate_terms:
            if re.search(term, content, re.IGNORECASE):
                issues.append(f"内容包含不合适的词汇或表达'{term}'，应使用更贴近日常生活的表达")

        # 检查是否有奇怪的描述组合
        strange_combinations = [
            r'突然想起.*设计.*宣传册',
            r'帮.*部.*设计.*法器',
            r'表盘.*落点.*精准度',
            r'殡葬.*设计.*往生',
            r'法器.*宣传册.*表盘'
        ]

        for pattern in strange_combinations:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"内容包含奇怪的描述组合，表达别扭不自然")

        # 检查是否有奇怪的句式结构（不符合现代网文风格）
        strange_sentence_patterns = [
            r'突然想起.*——.*',  # "突然想起...——..."句式
            r'.*想起.*的.*——.*',  # "想起...的...——..."句式
            r'.*帮.*设计.*——.*',  # "帮...设计...——..."句式
            r'.*上周.*的.*——.*',  # "上周...的...——..."句式
            r'.*隔壁.*的.*——.*',  # "隔壁...的...——..."句式
            r'.*部.*设计.*——.*',  # "...部设计...——..."句式
            r'.*宣传册.*——.*',  # "宣传册——..."句式
            r'.*法器.*——.*',  # "法器——..."句式
            r'.*这.*的.*度$',  # "这...的...度"结尾句式
            r'.*这.*落点.*精准度',  # "这...落点...精准度"句式
        ]

        for pattern in strange_sentence_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"内容包含不符合现代网文风格的奇怪句式结构，应使用简洁直接的表达方式")

        # 检查是否有破折号句式（严格禁止）
        dash_patterns = [
            r'.*——.*',  # 任何包含破折号的句式
        ]

        for pattern in dash_patterns:
            if re.search(pattern, content):
                issues.append(f"内容包含破折号句式，严格禁止使用破折号连接句子")

        # 检查是否编造了前文没有的内容（逻辑一致性检查）
        fabricated_content_patterns = [
            r'昨夜.*发现.*密信',
            r'之前.*提到.*',
            r'刚才.*说过.*',
            r'前面.*讲到.*',
            r'上次.*遇到.*',
            r'早先.*见过.*',
            r'先前.*经历.*',
            r'此前.*得知.*'
        ]

        for pattern in fabricated_content_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"内容可能编造了前文没有的情节，请确保所有内容都有逻辑依据")

        # 检查是否缺乏逻辑因果关系
        logical_issues_patterns = [
            r'突然.*出现',  # 突然出现的事物
            r'莫名.*获得',  # 莫名获得的能力
            r'不知.*为何',  # 不知为何的情况
            r'无缘无故.*',  # 无缘无故的事件
            r'.*发布.*任务',  # 发布任务但没有解释原因
            r'系统.*激活',  # 系统激活但没有说明原因
            r'.*出现.*但.*没有.*解释',  # 出现但没有解释
        ]

        for pattern in logical_issues_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"内容可能缺乏逻辑因果关系，需要说明事件的原因、过程和影响")

        # 检查是否有无意义的内容
        meaningless_content_patterns = [
            r'为了.*而.*',  # 可能是为了凑字数的内容
            r'突然想到.*',  # 突然想到的无关内容
            r'顺便.*',  # 顺便做的无关事情
            r'随便.*',  # 随便的行为
            r'无聊.*',  # 无聊的描述
        ]

        for pattern in meaningless_content_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(f"内容可能包含无意义的情节，每个内容都应该对故事发展有实际作用")

        # 检查是否包含章节框架规划内容
        planning_markers = [
            "章节框架规划",
            "第一步：章节定位与主题",
            "第二步：情节与场景规划",
            "第三步：人物规划",
            "第四步：维度应用规划",
            "第五步：结构与节奏规划",
            "第六步：字数分配规划",
            "第七步：风格与语言规划",
            "第八步：章节间连贯性规划",
            "检查要点：",
            "情节连贯和人物形象一致"
        ]

        for marker in planning_markers:
            if marker in content:
                issues.append(f"内容包含章节框架规划内容'{marker}'，应移除所有规划内容")
                break

        # 计算实际内容字数（使用准确的字数统计）
        actual_content = TestService._remove_planning_content(content)
        current_word_count = TestService._count_words_accurately(actual_content)

        # 记录字数信息
        logger.info(f"验证内容字数: 原始内容 {len(content)} 字符，处理后内容 {len(actual_content)} 字符，准确字数 {current_word_count} 字")

        # 检查字数是否在目标范围内（放宽要求，以剧情质量为主）
        min_word_count = int(target_word_count * 0.70)  # 放宽到70%
        max_word_count = int(target_word_count * 1.30)  # 放宽到130%

        # 只在字数严重偏离时才报告问题
        if current_word_count < min_word_count:
            issues.append(f"准确字数({current_word_count})严重不足，建议适当扩展内容（目标{target_word_count}字）")
        elif current_word_count > max_word_count:
            issues.append(f"准确字数({current_word_count})严重超标，建议适当精简内容（目标{target_word_count}字）")
        else:
            # 在合理范围内，记录信息但不作为问题
            logger.info(f"字数在合理范围内: {current_word_count}字（目标{target_word_count}字，容忍范围{min_word_count}-{max_word_count}字）")

        # 检查是否包含未来科技或赛博朋克内容
        sci_fi_keywords = ["赛博", "芯片", "人工智能", "AI", "机器人", "全息", "虚拟现实", "VR", "AR", "量子", "纳米", "基因编辑", "太空站", "星际"]
        for keyword in sci_fi_keywords:
            if keyword in content:
                issues.append(f"内容包含未来科技或赛博朋克相关词汇'{keyword}'")
                break

        # 返回验证结果
        if issues:
            return False, "、".join(issues)
        else:
            return True, "内容符合所有要求"

    @staticmethod
    def _remove_planning_content(content: str) -> str:
        """
        移除生成内容中的章节框架规划部分和字数注释

        Args:
            content: 生成的原始内容

        Returns:
            移除章节框架规划和字数注释后的内容
        """
        # 如果内容为空，直接返回
        if not content:
            return content

        # 定义可能的章节框架规划标记
        planning_markers = [
            "章节框架规划",
            "第一步：章节定位与主题",
            "第二步：情节与场景规划",
            "第三步：人物规划",
            "第四步：维度应用规划",
            "第五步：结构与节奏规划",
            "第六步：字数分配规划",
            "第七步：风格与语言规划",
            "第八步：章节间连贯性规划",
            "检查要点：",
            "情节连贯和人物形象一致"
        ]

        # 查找章节标题的位置
        chapter_title_match = re.search(r'# 第\d+章', content)
        if chapter_title_match:
            # 如果找到章节标题，检查标题前的内容是否包含规划内容
            title_pos = chapter_title_match.start()
            pre_title_content = content[:title_pos].strip()

            # 检查标题前的内容是否包含规划标记
            contains_planning = any(marker in pre_title_content for marker in planning_markers)
            if contains_planning:
                # 如果包含规划内容，只保留从章节标题开始的部分
                content = content[title_pos:].strip()

        # 尝试查找规划部分的开始和结束位置
        for marker in planning_markers:
            if marker in content:
                # 找到规划部分的开始位置
                planning_start = content.find(marker)

                # 查找规划部分之后的第一个章节标题
                chapter_match = re.search(r'# 第\d+章', content[planning_start:])
                if chapter_match:
                    # 找到章节标题的位置
                    chapter_pos = planning_start + chapter_match.start()

                    # 移除规划部分，只保留章节标题及之后的内容
                    content = content[chapter_pos:].strip()
                    break

                # 如果没有找到章节标题，尝试查找规划部分的结束位置
                # 可能的结束标记
                end_markers = ["开始写作", "正文开始", "章节内容", "# "]
                for end_marker in end_markers:
                    end_pos = content.find(end_marker, planning_start + len(marker))
                    if end_pos > planning_start:
                        # 找到结束位置，移除规划部分
                        content = (content[:planning_start] + content[end_pos:]).strip()
                        break

        # 移除字数注释 <!-- 当前字数：1200字 -->
        content = re.sub(r'<!--\s*当前字数：\d+字\s*-->', '', content)

        # 移除进度注释 <!-- 当前进度：约X字，目标Y字，完成度Z% -->
        content = re.sub(r'<!--\s*当前进度：约\d+字，目标\d+字，完成度\d+%\s*-->', '', content)

        # 移除任何其他HTML注释
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)

        # 移除多余的空行（连续的两个以上换行符替换为两个换行符）
        content = re.sub(r'\n{3,}', '\n\n', content)

        return content.strip()

    @staticmethod
    def _post_process_content(content: str, target_word_count: int = None) -> str:
        """
        对生成的内容进行后处理，确保内容的创新性和字数合适

        Args:
            content: 生成的原始内容
            target_word_count: 目标字数，如果提供则尝试调整内容字数

        Returns:
            处理后的内容
        """
        # 如果内容太短，直接返回
        if len(content) < 100:
            return content

        # 确保内容以章节标题开始
        if not content.startswith("#"):
            # 尝试提取第一行作为标题
            first_line = content.split("\n")[0]
            content = f"# {first_line}\n\n" + content

        # 如果提供了目标字数，使用准确的字数统计
        current_word_count = TestService._count_words_accurately(content)

        # 记录字数信息
        logger.info(f"生成内容准确字数: {current_word_count}")
        if target_word_count:
            logger.info(f"目标字数: {target_word_count}")

            # 计算字数差异百分比，避免除零错误
            if target_word_count > 0:
                difference_percent = abs(current_word_count - target_word_count) / target_word_count * 100
                logger.info(f"准确字数差异: {difference_percent:.2f}%")

                # 如果字数差异超过8%，记录警告
                if difference_percent > 8:
                    logger.warning(f"生成内容字数与目标字数差异超过8%: {current_word_count} vs {target_word_count}")

                    # 如果生成的内容字数小于目标字数的92%，添加警告信息
                    if current_word_count < target_word_count * 0.92:
                        content += f"\n\n> **注意：** 生成的内容字数({current_word_count}字)小于原文章节字数({target_word_count}字)的92%，可能需要重新生成或手动补充内容。"
                    # 如果生成的内容字数大于目标字数的108%，添加警告信息
                    elif current_word_count > target_word_count * 1.08:
                        content += f"\n\n> **注意：** 生成的内容字数({current_word_count}字)大于原文章节字数({target_word_count}字)的108%，可能需要重新生成或手动精简内容。"

                    # 如果字数差异超过30%，添加更严重的警告
                    if difference_percent > 30:
                        logger.error(f"生成内容字数与目标字数差异过大: {current_word_count} vs {target_word_count}")
                        content += f"\n\n> **严重警告：** 生成的内容字数与原文章节字数差异过大，强烈建议重新生成！"
                else:
                    logger.info(f"生成内容字数与目标字数差异在8%以内，符合要求: {current_word_count} vs {target_word_count}")
                    content += f"\n\n> **字数控制良好：** 生成的内容字数({current_word_count}字)与原文章节字数({target_word_count}字)的差异在8%以内，符合要求。"
            else:
                # 目标字数为0或无效时的处理
                logger.warning(f"目标字数无效({target_word_count})，无法计算字数差异")
                content += f"\n\n> **注意：** 目标字数无效，无法进行字数对比。生成内容字数：{current_word_count}字"

        # 添加一个提示，表明这是AI生成的创新内容
        footer = f"\n\n---\n*本章内容为AI根据原文风格创作的全新内容，人物名称和情节均为创新创作。字数：{current_word_count}字"

        # 如果有目标字数，添加对比信息
        if target_word_count:
            footer += f"，原文章节字数：{target_word_count}字"

            # 添加字数差异百分比，避免除零错误
            if target_word_count > 0:
                difference_percent = abs(current_word_count - target_word_count) / target_word_count * 100
                footer += f"，字数差异：{difference_percent:.1f}%"
            else:
                footer += f"，字数差异：无法计算（目标字数无效）"

        footer += "*"

        # 返回处理后的内容
        return content + footer

    @staticmethod
    def _save_to_content_repository(title: str, author: str, generated_chapters: List[Dict[str, str]], novel_id: int = None) -> int:
        """
        将生成的内容保存到内容仓库

        Args:
            title: 原小说标题
            author: 原小说作者
            generated_chapters: 生成的章节列表

        Returns:
            生成的小说ID
        """
        try:
            # 创建新的小说标题，标明这是AI生成的内容
            new_title = f"AI生成: {title}"

            # 合并所有章节内容
            full_content = f"# {new_title}\n\n"
            full_content += f"*基于《{title}》风格创作的全新内容，共{len(generated_chapters)}章*\n\n"

            # 在内容生成完成后，先生成整本书的标题，再生成简介
            generated_title = TestService._generate_book_title(title, author, generated_chapters)
            content_summary = TestService._generate_content_summary(title, author, generated_chapters, generated_title)

            # 更新新标题
            new_title = f"AI生成: {generated_title}"
            full_content = f"# {new_title}\n\n"
            full_content += f"*基于《{title}》风格创作的全新内容，共{len(generated_chapters)}章*\n\n"
            full_content += f"## 内容简介\n\n{content_summary}\n\n---\n\n"

            # 添加章节信息
            for i, chapter in enumerate(generated_chapters):
                # 计算章节字数
                chapter_word_count = len(chapter['content'])

                # 添加章节标题和内容
                full_content += f"## {chapter['title']} (字数：{chapter_word_count}字)\n\n{chapter['content']}\n\n"

                # 添加章节分隔线
                if i < len(generated_chapters) - 1:
                    full_content += "---\n\n"

            # 创建数据库会话
            session = Session()
            try:
                # 获取原小说ID
                original_novel_id = novel_id  # 直接使用传入的novel_id

                # 如果没有传入novel_id，尝试从test_tasks中查找
                if not original_novel_id and 'test_tasks' in globals():
                    for _, task in test_tasks.items():  # 使用_忽略未使用的变量
                        if task.get("results") and task["results"].get("novel_id") and str(task["results"].get("novel_id")) == str(novel_id):
                            original_novel_id = int(novel_id)
                            break

                # 创建章节字数对比信息
                chapter_word_count_comparison = []
                for i, chapter in enumerate(generated_chapters):
                    # 计算章节字数
                    generated_word_count = len(chapter['content'])
                    original_word_count = 0

                    # 尝试获取原章节字数
                    if original_novel_id:
                        original_chapter = session.query(Chapter).filter_by(
                            novel_id=original_novel_id,
                            chapter_number=i+1
                        ).first()
                        if original_chapter:
                            original_word_count = len(original_chapter.content) if original_chapter.content else 0

                    # 计算字数差异百分比，避免除零错误
                    if original_word_count > 0:
                        difference_percent = abs(generated_word_count - original_word_count) / original_word_count * 100
                    else:
                        difference_percent = 0  # 原文字数为0时，差异百分比设为0

                    chapter_word_count_comparison.append({
                        "chapter_number": i + 1,
                        "chapter_title": chapter['title'] or f"第{i+1}章",
                        "original_word_count": original_word_count,
                        "generated_word_count": generated_word_count,
                        "difference_percent": round(difference_percent, 1)
                    })

                # 创建新的Novel对象
                new_novel = Novel(
                    title=new_title,
                    author=f"AI基于{author or '未知作者'}",
                    content=full_content,
                    metadata={
                        "is_generated": True,
                        "generated_at": datetime.now().isoformat(),
                        "original_title": title,
                        "original_author": author,
                        "original_id": original_novel_id,
                        "generation_method": "test_service",
                        "chapter_count": len(generated_chapters),
                        "chapter_word_count_comparison": chapter_word_count_comparison
                    }
                )

                # 保存到数据库
                session.add(new_novel)
                session.flush()  # 获取新小说的ID

                # 分章节保存
                for i, chapter_data in enumerate(generated_chapters):
                    # 创建章节对象
                    chapter = Chapter(
                        novel_id=new_novel.id,
                        chapter_number=i + 1,
                        content=chapter_data["content"],
                        title=chapter_data["title"]
                    )
                    session.add(chapter)

                # 提交事务
                session.commit()

                logger.info(f"生成的内容已保存到内容仓库，ID: {new_novel.id}")
                return new_novel.id
            except Exception as e:
                session.rollback()
                logger.error(f"保存生成内容到内容仓库失败: {str(e)}", exc_info=True)
                raise
            finally:
                session.close()
        except Exception as e:
            logger.error(f"保存生成内容到内容仓库时出错: {str(e)}", exc_info=True)
            return 0

    @staticmethod
    def _generate_content_summary(original_title: str, original_author: str, generated_chapters: List[Dict[str, str]], generated_title: str = None) -> str:
        """
        根据生成的内容生成简介 - 仿照网络小说标准格式

        Args:
            original_title: 原小说标题
            original_author: 原小说作者
            generated_chapters: 生成的章节列表

        Returns:
            生成的简介
        """
        try:
            # 检查参数
            if not generated_chapters:
                logger.warning("生成内容简介失败: 章节列表为空")
                return "内容简介生成失败，章节列表为空。"

            # 创建API客户端
            api_client = DeepSeekClient()

            # 提取每个章节的核心内容作为摘要（更详细）
            chapter_summaries = []
            for i, chapter in enumerate(generated_chapters):
                if "content" not in chapter or not chapter["content"]:
                    logger.warning(f"第{i+1}章内容为空，跳过")
                    continue

                content = chapter["content"]
                # 移除Markdown标记和格式符号
                content = re.sub(r'#+ ', '', content)
                content = re.sub(r'\*\*|\*|__|\||>|`', '', content)
                content = re.sub(r'---.*?---', '', content, flags=re.DOTALL)  # 移除分隔线
                content = re.sub(r'\n+', ' ', content)  # 合并换行

                # 提取前500个字符作为摘要（增加信息量）
                summary = content[:500] + "..." if len(content) > 500 else content
                chapter_summaries.append(f"第{i+1}章内容: {summary}")

            # 如果所有章节都没有内容，返回默认简介
            if not chapter_summaries:
                logger.warning("生成内容简介失败: 所有章节内容为空")
                return "这是一部基于《" + original_title + "》风格创作的全新小说，融合了原作的精髓，但创造了全新的人物和情节。"

            # 将所有章节摘要合并
            all_summaries = "\n\n".join(chapter_summaries)

            # 构建网络小说风格的简介生成提示词
            prompt = f"""
# 🎯 网络小说简介生成任务

## 📚 参考优秀简介格式案例
**案例1格式：**
[双重生换命格+对照组+宫斗+疯批君王+钓系美人+白月光+人间清醒]
温云眠和庶妹一起重生了。
前世她吃了生子丹，生下未来帝王，一路成为太后。庶妹选择貌美丹，却落得冷宫幽禁。
重来一世，庶妹毫不犹豫抢走生子丹。
温云眠冷笑：真以为只要生下孩子就是未来帝王吗？笑话，她才是那个塑造未来帝王的人！
这次，她淡定的选择貌美丹，孩子谁爱生谁生，她可不受那个苦了！
吃下貌美丹，艳绝后宫、勾引帝王做妖妃不香吗？
这一世的目标很简单，荣华和富贵，一样不能少！爱和孩子谁爱要谁要！她不稀罕。
本以为做了妖妃就能一心夺权要钱了，可榻上的帝王怎么看她的眼神越来越深情了？
"眠眠，亲朕一口，后位就给你。"

**案例2格式：**
"亲朕要成，但洞房朕不想入，你说该怎么办？"
曹陌穿越大周王朝，竟成了一个假太监，开局还被女儿身的皇帝下旨去与皇后洞房。
好在此时，面板浮现：
【女帝萧如珑向你发出了一个任务，代替她与皇后洞房，完成瞒天过海。】
【任务完成，可获得奖励：天阶功法《葵元内经》，三十年修为。】
【是否接取？】
"赴汤蹈火啊，陛下！"
......
【反贼贵妃向你发出了一个任务......】
......
【妖娆太后向你发出了一个任务......】
......
从一件件任务开始，曹陌声名渐起，直至势力遍布朝野，庙堂江湖皆是闻之色变。
目光望向了龙椅之上的皇帝。
"陛下，今日该翻谁的牌子了？"
"今日啊？"
女儿身的皇帝倚在龙椅上，龙袍微敞："是不是该轮到朕了？"
【女帝萧如珑再次向你发出一个任务......】

## 📋 网络小说简介核心特征分析
**从优秀案例中提取的关键特征：**
1. **类型标签在前**：[标签1+标签2+标签3+...] 必须放在开头
2. **短句节奏**：多用句号，避免长句，节奏感强
3. **通俗口语化**：用词简单直白，生活化表达
4. **立即入戏**：第一句就进入核心冲突
5. **对比反转**：前世今生、预期vs现实的强烈对比
6. **人物对话**：用对话增加代入感和情感张力
7. **爽点突出**：重生、逆袭、打脸等网文爽点
8. **悬念结尾**：留下想象空间，不透露结局

## 📖 生成内容信息
原小说标题: {original_title}
原小说作者: {original_author}
生成的新书标题: {generated_title if generated_title else "待生成"}

实际生成的章节内容摘要:
{all_summaries}

## 🎯 严格执行要求
你必须严格按照上述案例的格式和风格，为实际生成的内容创作网络小说简介。

**重要提醒：**
- 生成的新书标题是: {generated_title if generated_title else "待生成"}
- 简介内容必须与这个标题高度契合
- 简介要体现标题中的核心元素和人物设定

**格式检查清单（必须100%执行）：**
□ 开头必须有类型标签 [标签1+标签2+标签3+...]
□ 使用短句，多用句号分段，避免长句
□ 语言通俗生活化，避免文绉绉的书面语
□ 第一句话就要有冲击力，立即进入情节
□ 包含主要人物的对话或内心独白
□ 突出主要冲突和爽点（逆袭、打脸、反转等）
□ 结尾留悬念，不透露结局
□ 整体风格模仿案例，通俗易懂有吸引力
□ 简介内容与生成的标题高度契合

**语言风格要求：**
- 多用"竟然"、"没想到"、"原来"、"可是"等转折词
- 多用"！"感叹号增加情感张力
- 多用"？"疑问句增加悬念
- 避免"然而"、"因此"、"由于"等书面语
- 用"他/她心想"、"他/她冷笑"等简单表达

**标题契合要求：**
- 如果标题提到身份（如九爷、摄政王），简介要体现这个身份
- 如果标题提到特点（如重生、系统），简介要突出这些元素
- 如果标题有疑问或感叹，简介要呼应这种语气
- 简介的主角设定要与标题完全一致

请严格按照以上要求生成简介：
"""

            # 调用API生成简介
            response = api_client.analyze_text(
                text=prompt,
                analysis_type="content_summary_generation",
                max_tokens=1200  # 增加token数量以生成更完整的简介
            )

            # 提取生成的简介
            if response and "content" in response:
                summary = response["content"].strip()
                if not summary:
                    logger.warning("生成内容简介失败: API返回的内容为空")
                    return "这是一部基于《" + original_title + "》风格创作的全新小说，融合了原作的精髓，但创造了全新的人物和情节。"

                # 后处理和格式验证
                summary = TestService._post_process_summary(summary, original_title)

                logger.info(f"成功生成网络小说风格简介，长度: {len(summary)}字")
                return summary
            else:
                logger.error(f"生成内容简介失败: API返回的数据格式不正确")
                return "这是一部基于《" + original_title + "》风格创作的全新小说，融合了原作的精髓，但创造了全新的人物和情节。"
        except Exception as e:
            logger.error(f"生成内容简介时出错: {str(e)}", exc_info=True)
            return f"这是一部基于《{original_title}》风格创作的全新小说，融合了原作的精髓，但创造了全新的人物和情节。"

    @staticmethod
    def _generate_book_title(original_title: str, original_author: str, generated_chapters: List[Dict[str, str]]) -> str:
        """
        根据生成的内容生成整本书的标题 - 仿照网络小说标准格式

        Args:
            original_title: 原小说标题
            original_author: 原小说作者
            generated_chapters: 生成的章节列表

        Returns:
            生成的书名
        """
        try:
            # 检查参数
            if not generated_chapters:
                logger.warning("生成书名失败: 章节列表为空")
                return f"基于《{original_title}》的全新创作"

            # 创建API客户端
            api_client = DeepSeekClient()

            # 提取每个章节的核心内容作为摘要
            chapter_summaries = []
            for i, chapter in enumerate(generated_chapters):
                if "content" not in chapter or not chapter["content"]:
                    continue

                content = chapter["content"]
                # 移除格式符号，提取纯文本
                content = re.sub(r'#+ ', '', content)
                content = re.sub(r'\*\*|\*|__|\||>|`', '', content)
                content = re.sub(r'---.*?---', '', content, flags=re.DOTALL)
                content = re.sub(r'\n+', ' ', content)

                # 提取前300个字符作为摘要
                summary = content[:300] + "..." if len(content) > 300 else content
                chapter_summaries.append(f"第{i+1}章: {summary}")

            # 如果所有章节都没有内容，返回默认标题
            if not chapter_summaries:
                return f"基于《{original_title}》的全新创作"

            # 将所有章节摘要合并
            all_summaries = "\n\n".join(chapter_summaries)

            # 构建网络小说风格的标题生成提示词
            prompt = f"""
# 🎯 网络小说标题生成任务

## 📚 优秀标题案例参考
**女频标题案例：**
- 京圈九爷的重生小玫瑰
- 又欲又乖，摄政王殿下
- 你家杀手缺根筋！
- 裸辞做保姆，她成上流社会香饽饽
- 末世文恶毒女配？嚣张一日是一日
- 恶毒师妹不洗白在宗门创死所有人
- 穿到荒年后，我成了极品恶婆婆
- 穿成大佬恶毒前妻后，被全家团宠

**男频标题案例：**
- 满门恋爱脑，唯有小师弟正的发邪
- 特效修仙：从被母女误认大佬开始
- 不是，我电子女友咋修成剑仙了
- 无限末世：开局一座地窖安全屋
- 婴儿的我，获得大器晚成逆袭系统
- 开局撕婚书，我成就最强镇国公！
- 开局长生不死，谁都以为我无敌
- 我说大明要完，老朱夸我千古一相
- 抗战：我谋士入局，觉醒小地图！

## 📋 网络小说标题核心特征
**从优秀案例中提取的关键特征：**
1. **身份标签**：九爷、摄政王、杀手、师弟、镇国公等
2. **人设特点**：重生、恶毒、嚣张、正的发邪、长生不死等
3. **开局设定**：开局撕婚书、从被误认开始、获得系统等
4. **情感张力**：又欲又乖、缺根筋、嚣张一日是一日等
5. **对比反差**：满门恋爱脑vs小师弟、恶毒女配vs嚣张等
6. **疑问感叹**：你家杀手缺根筋！、咋修成剑仙了等
7. **时代背景**：末世、修仙、大明、抗战等
8. **通俗口语**：咋、不是、谁都以为等

## 📖 生成内容信息
原小说标题: {original_title}
原小说作者: {original_author}

实际生成的章节内容摘要:
{all_summaries}

## 🎯 标题生成要求
请严格按照上述优秀案例的风格，为实际生成的内容创作一个吸引人的网络小说标题。

**必须遵循的要求：**
□ 标题长度控制在8-20个字之间
□ 突出主角的身份、特点或开局设定
□ 使用通俗口语化的表达，避免文绉绉
□ 可以使用疑问句、感叹句增加吸引力
□ 体现小说的核心冲突或爽点
□ 如果有系统、重生、穿越等元素要体现出来
□ 避免过于复杂的词汇，要朗朗上口
□ 可以使用对比、反差等手法

**风格要求：**
- 优先使用简单直白的词汇
- 可以适当使用网络用语（咋、啥、不是等）
- 突出人物关系和身份对比
- 体现主角的特殊之处或逆袭过程

请只生成标题，不要其他内容：
"""

            # 调用API生成标题
            response = api_client.analyze_text(
                text=prompt,
                analysis_type="book_title_generation",
                max_tokens=200  # 标题不需要太多token
            )

            # 提取生成的标题
            if response and "content" in response:
                title = response["content"].strip()
                if not title:
                    logger.warning("生成书名失败: API返回的内容为空")
                    return f"基于《{original_title}》的全新创作"

                # 后处理标题
                title = TestService._post_process_title(title, original_title)

                logger.info(f"成功生成网络小说风格标题: {title}")
                return title
            else:
                logger.error(f"生成书名失败: API返回的数据格式不正确")
                return f"基于《{original_title}》的全新创作"
        except Exception as e:
            logger.error(f"生成书名时出错: {str(e)}", exc_info=True)
            return f"基于《{original_title}》的全新创作"

    @staticmethod
    def _post_process_title(title: str, original_title: str) -> str:
        """
        后处理标题，确保符合网络小说格式要求

        Args:
            title: 原始标题
            original_title: 原小说标题

        Returns:
            后处理后的标题
        """
        try:
            # 移除可能的AI回复前缀
            title = re.sub(r'^(标题[:：]|书名[:：]|生成的标题[:：])', '', title, flags=re.IGNORECASE)
            title = re.sub(r'^[《「『]', '', title)
            title = re.sub(r'[》」』]$', '', title)

            # 移除多余的空格和换行
            title = re.sub(r'\s+', '', title)
            title = title.strip()

            # 长度控制
            if len(title) > 25:
                logger.warning(f"标题过长({len(title)}字)，进行截取")
                # 在合适的位置截断
                if '，' in title[:20]:
                    title = title[:title.find('，', 15)]
                elif '：' in title[:20]:
                    title = title[:title.find('：', 15) + 1]
                else:
                    title = title[:20]
            elif len(title) < 5:
                logger.warning(f"标题过短({len(title)}字)，使用默认格式")
                return f"基于《{original_title}》的全新创作"

            # 确保标题不包含原标题
            if original_title in title:
                title = title.replace(original_title, "")
                if len(title) < 5:
                    return f"基于《{original_title}》的全新创作"

            logger.info(f"标题后处理完成: {title}")
            return title

        except Exception as e:
            logger.error(f"标题后处理失败: {str(e)}")
            return title  # 返回原始标题

    @staticmethod
    def _generate_chapter_title_from_content(chapter_content: str, chapter_number: int,
                                           novel_title: str, all_chapters: List[Dict[str, str]]) -> str:
        """
        基于完整章节内容智能生成网文风格章节标题

        Args:
            chapter_content: 章节完整内容
            chapter_number: 章节编号
            novel_title: 小说标题
            all_chapters: 所有章节列表（用于了解整体故事脉络）

        Returns:
            生成的章节标题
        """
        try:
            # 检查参数
            if not chapter_content or not chapter_content.strip():
                logger.warning(f"第{chapter_number}章内容为空，使用默认标题")
                return f"第{chapter_number}章"

            # 创建API客户端
            api_client = DeepSeekClient()

            # 清理章节内容，提取核心信息
            cleaned_content = re.sub(r'#+ ', '', chapter_content)
            cleaned_content = re.sub(r'\*\*|\*|__|\||>|`', '', cleaned_content)
            cleaned_content = re.sub(r'---.*?---', '', cleaned_content, flags=re.DOTALL)
            cleaned_content = re.sub(r'\n+', ' ', cleaned_content)

            # 提取章节核心内容（前800字，确保包含足够信息）
            core_content = cleaned_content[:800] + "..." if len(cleaned_content) > 800 else cleaned_content

            # 获取前后章节信息（如果有的话）
            context_info = ""
            if chapter_number > 1 and len(all_chapters) >= chapter_number - 1:
                prev_chapter = all_chapters[chapter_number - 2]
                if prev_chapter.get("content"):
                    prev_summary = prev_chapter["content"][:200] + "..."
                    context_info += f"上一章内容概要: {prev_summary}\n\n"

            if chapter_number < len(all_chapters):
                next_chapter = all_chapters[chapter_number]
                if next_chapter.get("content"):
                    next_summary = next_chapter["content"][:200] + "..."
                    context_info += f"下一章内容概要: {next_summary}\n\n"

            # 构建网文风格章节标题生成提示词
            prompt = f"""
# 🎯 网文章节标题生成任务

## 📚 优秀网文章节标题案例参考
**经典网文章节标题特点：**
- 突出本章核心事件：《初遇神秘少年》《意外的邂逅》《危机降临》
- 体现情感冲突：《愤怒的质问》《温柔的告白》《痛苦的选择》
- 突出转折点：《真相大白》《身份暴露》《绝地反击》
- 制造悬念：《神秘的来客》《意外的发现》《不速之客》
- 体现人物状态：《觉醒的力量》《迷茫的少女》《愤怒的复仇》
- 突出关键物品/地点：《神秘的盒子》《禁忌之地》《古老的传说》

## 📋 网文章节标题核心要求
1. **长度控制**：4-12个字，简洁有力
2. **突出重点**：体现本章最重要的事件或转折
3. **制造悬念**：让读者想要继续阅读
4. **情感色彩**：体现本章的情感基调
5. **通俗易懂**：避免过于文艺或复杂的词汇
6. **符合网文风格**：直白、有冲击力、吸引眼球

## 📖 章节信息
小说标题: {novel_title}
章节编号: 第{chapter_number}章
{context_info}

本章节完整内容:
{core_content}

## 🎯 标题生成要求
请基于本章节的完整内容，生成一个符合网文风格的章节标题。

**分析步骤：**
1. **识别核心事件**：本章最重要的事件是什么？
2. **找出转折点**：本章有什么重要的转折或发现？
3. **确定情感基调**：本章的主要情感是什么？（紧张、温馨、愤怒、悲伤等）
4. **提取关键元素**：本章出现的重要人物、物品、地点？
5. **制造悬念**：如何让标题吸引读者继续阅读？

**标题要求：**
□ 4-12个字长度
□ 突出本章核心事件或转折
□ 体现情感色彩
□ 制造悬念或吸引力
□ 通俗易懂，符合网文风格
□ 避免过于抽象或文艺的表达

请只生成标题，不要其他内容：
"""

            # 调用API生成标题
            response = api_client.analyze_text(
                text=prompt,
                analysis_type="chapter_title_generation",
                max_tokens=100  # 章节标题不需要太多token
            )

            # 提取生成的标题
            if response and "content" in response:
                title = response["content"].strip()
                if not title:
                    logger.warning(f"第{chapter_number}章标题生成失败: API返回的内容为空")
                    return f"第{chapter_number}章"

                # 后处理标题
                title = TestService._post_process_chapter_title(title, chapter_number)

                logger.info(f"成功为第{chapter_number}章基于内容生成标题: {title}")
                return title
            else:
                logger.error(f"第{chapter_number}章标题生成失败: API返回的数据格式不正确")
                return f"第{chapter_number}章"
        except Exception as e:
            logger.error(f"第{chapter_number}章标题生成时出错: {str(e)}", exc_info=True)
            return f"第{chapter_number}章"

    @staticmethod
    def _post_process_chapter_title(title: str, chapter_number: int) -> str:
        """
        后处理章节标题，确保符合网文格式要求

        Args:
            title: 原始标题
            chapter_number: 章节编号

        Returns:
            后处理后的标题
        """
        try:
            # 移除可能的AI回复前缀
            title = re.sub(r'^(标题[:：]|章节标题[:：]|第\d+章[:：])', '', title, flags=re.IGNORECASE)
            title = re.sub(r'^[《「『]', '', title)
            title = re.sub(r'[》」』]$', '', title)

            # 移除多余的空格和换行
            title = re.sub(r'\s+', '', title)
            title = title.strip()

            # 长度控制
            if len(title) > 15:
                logger.warning(f"第{chapter_number}章标题过长({len(title)}字)，进行截取")
                # 在合适的位置截断
                if '，' in title[:12]:
                    title = title[:title.find('，', 8)]
                elif '的' in title[:12]:
                    title = title[:title.find('的', 8) + 1]
                else:
                    title = title[:12]
            elif len(title) < 2:
                logger.warning(f"第{chapter_number}章标题过短({len(title)}字)，使用默认格式")
                return f"第{chapter_number}章"

            # 确保标题不包含章节编号（避免重复）
            title = re.sub(rf'^第{chapter_number}章\s*', '', title)

            # 组合最终标题
            final_title = f"第{chapter_number}章 {title}"

            logger.info(f"第{chapter_number}章标题后处理完成: {final_title}")
            return final_title

        except Exception as e:
            logger.error(f"第{chapter_number}章标题后处理失败: {str(e)}")
            return f"第{chapter_number}章 {title}"  # 返回带编号的原始标题

    @staticmethod
    def _post_process_summary(summary: str, original_title: str) -> str:
        """
        后处理简介内容，确保符合网络小说格式要求

        Args:
            summary: 原始简介内容
            original_title: 原小说标题

        Returns:
            后处理后的简介
        """
        try:
            # 移除可能的AI回复前缀
            summary = re.sub(r'^(好的，|我将|以下是|根据|按照).*?[:：]\s*', '', summary, flags=re.IGNORECASE)

            # 移除多余的换行和空格
            summary = re.sub(r'\n+', '\n', summary)
            summary = summary.strip()

            # 验证并修复类型标签
            if not summary.startswith('['):
                logger.warning("简介缺少类型标签，正在添加")
                # 根据内容推测类型标签
                tags = []
                if any(word in summary for word in ['穿越', '异世', '大陆', '修仙', '仙侠']):
                    tags.append('穿越')
                if any(word in summary for word in ['系统', '面板', '任务', '奖励']):
                    tags.append('系统')
                if any(word in summary for word in ['重生', '前世', '今生', '再来']):
                    tags.append('重生')
                if any(word in summary for word in ['逆袭', '打脸', '反击', '复仇']):
                    tags.append('逆袭')
                if any(word in summary for word in ['爽文', '装逼', '牛逼', '无敌']):
                    tags.append('爽文')
                if any(word in summary for word in ['宫斗', '后宫', '皇帝', '妃子']):
                    tags.append('宫斗')

                # 如果没有推测出标签，使用默认标签
                if not tags:
                    tags = ['穿越', '系统', '爽文']

                tag_string = '[' + '+'.join(tags) + ']'
                summary = f"{tag_string}\n{summary}"

            # 验证类型标签格式
            tag_match = re.match(r'\[([^\]]+)\]', summary)
            if tag_match:
                tag_content = tag_match.group(1)
                # 确保标签用+连接
                if '+' not in tag_content and len(tag_content.split()) > 1:
                    fixed_tags = '+'.join(tag_content.split())
                    summary = summary.replace(f'[{tag_content}]', f'[{fixed_tags}]')

            # 优化句子结构，确保短句多
            lines = summary.split('\n')
            processed_lines = []

            for line in lines:
                if line.strip():
                    # 将长句拆分为短句
                    line = re.sub(r'([。！？])([^。！？\n]{20,})', r'\1\n\2', line)
                    # 移除过于正式的连接词
                    line = re.sub(r'然而，', '可是，', line)
                    line = re.sub(r'因此，', '所以，', line)
                    line = re.sub(r'由于', '因为', line)
                    processed_lines.append(line)

            summary = '\n'.join(processed_lines)

            # 确保简介不会太短或太长
            if len(summary) < 100:
                logger.warning(f"简介过短({len(summary)}字)，可能需要重新生成")
            elif len(summary) > 800:
                logger.warning(f"简介过长({len(summary)}字)，进行适当截取")
                # 保留前600字，确保在句号处截断
                truncated = summary[:600]
                last_period = max(truncated.rfind('。'), truncated.rfind('！'), truncated.rfind('？'))
                if last_period > 300:  # 确保不会截取得太短
                    summary = truncated[:last_period + 1]

            logger.info(f"简介后处理完成，最终长度: {len(summary)}字")
            return summary

        except Exception as e:
            logger.error(f"简介后处理失败: {str(e)}")
            return summary  # 返回原始内容

    @staticmethod
    def _remove_ai_response_prefix(content: str) -> str:
        """
        移除AI回复的前缀内容，只保留实际的章节内容

        Args:
            content: 原始内容

        Returns:
            移除AI回复前缀后的内容
        """
        if not content:
            return content

        # 移除AI回复的常见前缀（增强版）
        ai_response_patterns = [
            # 基础回复模式
            r'^好的，我将.*?以下是.*?：\s*',
            r'^好的，我将.*?以下是.*?内容.*?：\s*',
            r'^好的，我将按照.*?要求.*?以下是.*?：\s*',
            r'^我将.*?以下是.*?：\s*',
            r'^以下是.*?内容.*?：\s*',
            r'^以下是.*?章节.*?：\s*',
            r'^以下是.*?扩展.*?内容.*?：\s*',
            r'^以下是.*?润色.*?内容.*?：\s*',
            r'^.*?字数扩展至\d+字.*?：\s*',
            r'^.*?在保持.*?基础上.*?：\s*',
            r'^.*?增加了.*?描写.*?：\s*',

            # 新增的AI回复模式
            r'^我将严格按照您的要求.*?：\s*',
            r'^我将严格按照.*?要求.*?：\s*',
            r'^按照您的要求.*?：\s*',
            r'^根据您的要求.*?：\s*',
            r'^我将按照要求.*?：\s*',
            r'^我将进行.*?：\s*',
            r'^我将.*?进行.*?：\s*',
            r'^现在我将.*?：\s*',
            r'^接下来我将.*?：\s*',
            r'^下面是.*?：\s*',
            r'^这是.*?：\s*',
            r'^为您.*?：\s*',
            r'^我来.*?：\s*',
            r'^让我.*?：\s*',

            # 专业化回复模式
            r'^我将严格按照您的要求进行.*?扩展.*?：\s*',
            r'^我将严格按照您的要求进行.*?深化.*?：\s*',
            r'^我将严格按照您的要求进行.*?调整.*?：\s*',
            r'^我将严格按照您的要求进行.*?优化.*?：\s*',
            r'^我将在保持.*?的前提下.*?：\s*',
            r'^我将在.*?基础上.*?：\s*',
            r'^我将对.*?进行.*?：\s*',
            r'^我将为您.*?：\s*',

            # 字数相关回复
            r'^.*?字数.*?扩展.*?：\s*',
            r'^.*?字数.*?调整.*?：\s*',
            r'^.*?字数.*?优化.*?：\s*',
            r'^.*?扩展到.*?字.*?：\s*',
            r'^.*?调整为.*?字.*?：\s*',

            # 质量相关回复
            r'^.*?剧情.*?扩展.*?：\s*',
            r'^.*?剧情.*?深化.*?：\s*',
            r'^.*?内容.*?丰富.*?：\s*',
            r'^.*?细节.*?增加.*?：\s*',
            r'^.*?描写.*?强化.*?：\s*',

            # 新增：分析相关的AI回复前缀
            r'^\（经分析.*?\）\s*',
            r'^\（.*?经分析.*?\）\s*',
            r'^\（.*?原文已达到.*?\）\s*',
            r'^\（.*?强行扩展.*?\）\s*',
            r'^\（.*?以下优化.*?\）\s*',
            r'^\（.*?在保持原文.*?\）\s*',
            r'^\（.*?重点优化.*?\）\s*',

            # 新增：括号内的分析说明
            r'^\（[^）]*分析[^）]*\）\s*',
            r'^\（[^）]*优化[^）]*\）\s*',
            r'^\（[^）]*扩展[^）]*\）\s*',
            r'^\（[^）]*调整[^）]*\）\s*',
        ]

        for pattern in ai_response_patterns:
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL | re.IGNORECASE)

        # 移除重复的AI回复前缀（如果有多行重复）
        lines = content.split('\n')
        cleaned_lines = []
        skip_next = False

        for i, line in enumerate(lines):
            line = line.strip()

            # 跳过包含AI回复特征的行（增强版）
            ai_phrases = [
                '好的，我将', '以下是', '字数扩展至', '在保持', '增加了', '润色后的',
                '我将严格按照', '按照您的要求', '根据您的要求', '我将按照要求',
                '我将进行', '现在我将', '接下来我将', '下面是', '这是', '为您',
                '我来', '让我', '我将在保持', '我将在', '我将对', '我将为您',
                '剧情扩展', '剧情深化', '内容丰富', '细节增加', '描写强化',
                '字数调整', '字数优化', '扩展到', '调整为'
            ]

            if any(phrase in line.lower() for phrase in ai_phrases) and len(line) > 15:
                skip_next = True
                continue

            # 如果上一行是AI回复，跳过空行
            if skip_next and not line:
                continue

            skip_next = False
            cleaned_lines.append(lines[i])  # 保持原始格式

        # 最后处理：将【】符号转换为网文常见的双引号
        result = '\n'.join(cleaned_lines).strip()

        # 将【】转换为""（网文常见格式）
        result = result.replace('【', '"').replace('】', '"')

        return result

    @staticmethod
    def _count_words_accurately(content: str) -> int:
        """
        准确统计字数，类似Word的统计方式

        Args:
            content: 要统计的文本内容

        Returns:
            准确的字数统计
        """
        if not content:
            return 0

        # 移除Markdown标题标记
        content = re.sub(r'^#+\s*', '', content, flags=re.MULTILINE)

        # 移除HTML标签
        content = re.sub(r'<[^>]+>', '', content)

        # 移除章节框架规划内容（如果存在）- 更全面的规划内容识别
        planning_patterns = [
            # 步骤式规划
            r'第一步：.*?(?=第二步：|第三步：|第四步：|第五步：|第六步：|第七步：|第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第二步：.*?(?=第三步：|第四步：|第五步：|第六步：|第七步：|第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第三步：.*?(?=第四步：|第五步：|第六步：|第七步：|第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第四步：.*?(?=第五步：|第六步：|第七步：|第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第五步：.*?(?=第六步：|第七步：|第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第六步：.*?(?=第七步：|第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第七步：.*?(?=第八步：|现在开始|请直接|#\s*第\d+章|$)',
            r'第八步：.*?(?=现在开始|请直接|#\s*第\d+章|$)',

            # 标题式规划
            r'### 第.*?步：.*?(?=###|##|现在开始|请直接|#\s*第\d+章|$)',
            r'## 创新性.*?(?=##|#\s*第\d+章|现在开始|请直接|$)',
            r'## 第.*?步.*?(?=##|#\s*第\d+章|现在开始|请直接|$)',

            # 关键词式规划
            r'章节框架规划.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'故事主线创新规划.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'场景环境重构规划.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'人物创新规划.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'维度.*?规划.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'创新性.*?规划.*?(?=现在开始|请直接|#\s*第\d+章|$)',

            # 其他可能的规划标记
            r'在开始写作前.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'写作要求：.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'创作指导.*?(?=现在开始|请直接|#\s*第\d+章|$)',
            r'框架.*?完成.*?(?=现在开始|请直接|#\s*第\d+章|$)',

            # 结束标记前的所有规划内容
            r'^.*?(?=现在开始创作|请直接开始|#\s*第\d+章)',
        ]

        # 先尝试找到章节标题的位置
        chapter_title_match = re.search(r'#\s*第\d+章', content)
        if chapter_title_match:
            # 如果找到章节标题，只保留从章节标题开始的内容
            content = content[chapter_title_match.start():]
        else:
            # 如果没有找到章节标题，使用规划模式移除
            for pattern in planning_patterns:
                content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)

        # 移除可能残留的规划标记（但不要清空整个内容）
        planning_keywords = [
            '章节框架规划', '第一步：', '第二步：', '第三步：', '第四步：', '第五步：',
            '第六步：', '第七步：', '第八步：', '故事主线创新', '场景环境重构',
            '人物创新规划', '维度应用规划', '现在开始创作',
            '请直接开始', '框架完成', '开始写作'
        ]

        # 只移除明确的规划标记，不要清空整个内容
        for keyword in planning_keywords:
            if keyword in content:
                # 移除包含规划关键词的行，但保留其他内容
                lines = content.split('\n')
                filtered_lines = []
                for line in lines:
                    # 只移除明确包含规划关键词的行
                    if not any(kw in line for kw in planning_keywords):
                        filtered_lines.append(line)
                content = '\n'.join(filtered_lines)
                break

        # 移除多余的空白字符（保留正常的空格和换行）
        content = re.sub(r'\n\s*\n', '\n\n', content)  # 规范化段落间距
        content = re.sub(r'[ \t]+', ' ', content)  # 规范化空格

        # 统计剩余字符数（不移除标点符号，这是错误的做法）
        word_count = len(content.strip())

        return word_count

    @staticmethod
    def test_word_count_accuracy(content: str) -> Dict[str, int]:
        """
        测试字数统计的准确性，提供多种统计方式的对比

        Args:
            content: 要测试的文本内容

        Returns:
            包含不同统计方式结果的字典
        """
        results = {}

        # 原始字符数（包含所有字符）
        results['原始字符数'] = len(content)

        # 移除空白字符后的字符数
        no_whitespace = re.sub(r'\s+', '', content)
        results['无空白字符数'] = len(no_whitespace)

        # 只统计中文字符
        chinese_only = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\ua000-\ua48f\ua490-\ua4cf]', '', content)
        results['纯中文字符数'] = len(chinese_only)

        # 使用我们的准确统计方法
        results['准确字数统计'] = TestService._count_words_accurately(content)

        # 移除规划内容后的原始字符数
        cleaned_content = TestService._remove_planning_content(content)
        results['移除规划后字符数'] = len(cleaned_content)

        # 记录详细信息
        logger.info(f"字数统计对比: {results}")

        return results

    @staticmethod
    def _extract_complete_generated_content(generated_content: str, chapter_number: int, stage_name: str) -> str:
        """
        完整提取生成的内容，像提取最终写作结果一样

        这是修复写作内容传递漏洞的核心函数：
        - 确保每轮生成的内容都被完整提取
        - 避免内容在轮次传递中丢失
        - 保持字数的完整性

        Args:
            generated_content: 生成的完整内容
            chapter_number: 章节编号
            stage_name: 阶段名称（用于日志）

        Returns:
            完整提取的内容
        """
        logger.info(f"🔧 [{stage_name}] 开始完整提取生成内容，原始长度: {len(generated_content)}")

        if not generated_content:
            logger.warning(f"⚠️ [{stage_name}] 生成内容为空")
            return ""

        # 首先移除规划内容，保留实际写作内容
        cleaned_content = TestService._remove_planning_content(generated_content)
        logger.info(f"🧹 [{stage_name}] 移除规划内容后长度: {len(cleaned_content)}")

        # 然后提取章节内容（包含标题和正文）
        extracted_content = TestService._extract_chapter_content(cleaned_content, chapter_number)
        logger.info(f"📝 [{stage_name}] 提取章节内容后长度: {len(extracted_content)}")

        # 统计准确字数
        word_count = TestService._count_words_accurately(extracted_content)
        logger.info(f"📊 [{stage_name}] 完整提取完成，最终字数: {word_count}字")

        return extracted_content

    @staticmethod
    def _extract_chapter_content(generated_content: str, chapter_number: int) -> str:
        """
        从生成的内容中提取正确的章节内容（包含标题）

        Args:
            generated_content: 生成的完整内容
            chapter_number: 期望的章节编号

        Returns:
            提取的章节内容（包含标题和正文）
        """
        if not generated_content:
            return ""

        # 定义章节标记模式，包含标题和内容
        chapter_patterns = [
            # 精确匹配期望的章节编号（包含标题）
            rf'(第\s*{chapter_number}\s*章[^\n]*\n.*?)(?=第\s*\d+\s*章|$)',
            rf'(第{chapter_number}章[^\n]*\n.*?)(?=第\d+章|$)',
            # 匹配任何章节标记，然后验证编号（包含标题）
            r'(第\s*(\d+)\s*章[^\n]*\n.*?)(?=第\s*\d+\s*章|$)',
            r'(第(\d+)章[^\n]*\n.*?)(?=第\d+章|$)',
            # 匹配中文数字章节（包含标题）
            r'(第[一二三四五六七八九十百千万]+章[^\n]*\n.*?)(?=第[一二三四五六七八九十百千万]+章|$)',
            # 匹配英文章节（包含标题）
            r'(Chapter\s*(\d+)[^\n]*\n.*?)(?=Chapter\s*\d+|$)',
        ]

        logger.info(f"开始提取第{chapter_number}章内容（包含标题），原始内容长度: {len(generated_content)}")

        # 尝试使用各种模式提取章节内容
        for pattern_index, pattern in enumerate(chapter_patterns):
            try:
                matches = re.findall(pattern, generated_content, re.DOTALL | re.IGNORECASE)

                if matches:
                    logger.info(f"模式{pattern_index + 1}找到{len(matches)}个匹配")

                    # 对于包含章节编号的模式，验证编号是否正确
                    if pattern_index in [2, 3, 5]:  # 这些模式包含章节编号捕获组
                        for match in matches:
                            if isinstance(match, tuple) and len(match) >= 3:
                                # 第一个捕获组是完整内容（包含标题），第二个是章节编号
                                full_content = match[0].strip()
                                extracted_chapter_num = int(match[1])

                                if extracted_chapter_num == chapter_number:
                                    logger.info(f"找到匹配的第{chapter_number}章内容（包含标题），长度: {len(full_content)}")
                                    return full_content
                                else:
                                    logger.debug(f"章节编号不匹配: 期望{chapter_number}，实际{extracted_chapter_num}")
                            elif isinstance(match, tuple) and len(match) >= 2:
                                # 兼容旧格式
                                full_content = match[0].strip()
                                logger.info(f"使用模式{pattern_index + 1}提取到章节内容（包含标题），长度: {len(full_content)}")
                                return full_content
                    else:
                        # 对于不包含编号捕获组的模式，直接返回内容
                        content = matches[0].strip() if isinstance(matches[0], str) else matches[0][0].strip()
                        logger.info(f"使用模式{pattern_index + 1}提取到章节内容（包含标题），长度: {len(content)}")
                        return content

            except Exception as e:
                logger.warning(f"模式{pattern_index + 1}匹配失败: {str(e)}")
                continue

        # 如果没有找到明确的章节标记，尝试其他方法
        logger.warning(f"未找到明确的第{chapter_number}章标记，尝试其他提取方法")

        # 方法2: 查找包含"第X章"的行，然后提取包含标题的内容
        lines = generated_content.split('\n')
        chapter_start_index = -1

        for i, line in enumerate(lines):
            if re.search(rf'第\s*{chapter_number}\s*章', line, re.IGNORECASE):
                chapter_start_index = i
                break

        if chapter_start_index >= 0:
            # 找到下一个章节的开始位置
            next_chapter_index = len(lines)
            for i in range(chapter_start_index + 1, len(lines)):
                if re.search(r'第\s*\d+\s*章', lines[i], re.IGNORECASE):
                    next_chapter_index = i
                    break

            # 提取章节内容（包含标题行）
            chapter_lines = lines[chapter_start_index:next_chapter_index]  # 包含标题行
            content = '\n'.join(chapter_lines).strip()

            if content:
                logger.info(f"通过行扫描找到第{chapter_number}章内容（包含标题），长度: {len(content)}")
                return content

        # 方法3: 如果仍然没有找到，检查是否整个内容就是目标章节
        # 尝试为内容添加标题
        if not re.search(rf'第\s*{chapter_number}\s*章', generated_content[:100], re.IGNORECASE):
            # 如果内容开头没有章节标题，尝试添加一个
            first_line = lines[0].strip() if lines else ""
            if first_line and len(first_line) < 50 and not first_line.startswith('第'):
                # 第一行可能是标题，但没有章节编号
                content_with_title = f"第{chapter_number}章 {first_line}\n" + '\n'.join(lines[1:])
                logger.info(f"为内容添加章节标题，长度: {len(content_with_title)}")
                return content_with_title.strip()
            else:
                # 直接添加默认标题
                content_with_title = f"第{chapter_number}章\n" + generated_content
                logger.info(f"为内容添加默认章节标题，长度: {len(content_with_title)}")
                return content_with_title.strip()

        # 如果所有方法都失败，返回原始内容
        logger.warning(f"所有提取方法都失败，返回原始内容（包含标题）")
        return generated_content.strip()

    @staticmethod
    def _extract_chapter_title(content: str, chapter_number: int) -> str:
        """
        从生成的内容中提取章节标题

        Args:
            content: 生成的章节内容
            chapter_number: 章节编号

        Returns:
            提取的章节标题
        """
        # 默认标题
        default_title = f"第{chapter_number}章"

        # 如果内容为空，返回默认标题
        if not content:
            return default_title

        # 清理HTML标签的辅助函数
        def clean_html_tags(text: str) -> str:
            """移除HTML标签"""
            # 移除所有HTML标签
            clean_text = re.sub(r'<[^>]+>', '', text)
            return clean_text.strip()

        # 尝试从内容中提取标题
        lines = content.split('\n')

        # 查找以#开头的标题行
        for line in lines[:10]:  # 只检查前10行
            line = line.strip()
            if line.startswith('# '):
                # 提取标题文本并清理HTML标签
                title_text = line.replace('# ', '').strip()
                title_text = clean_html_tags(title_text)

                # 检查标题中的章节编号是否与期望的章节编号一致
                chapter_match = re.search(r'第\s*(\d+)\s*章', title_text)
                if chapter_match:
                    extracted_chapter_num = int(chapter_match.group(1))
                    if extracted_chapter_num == chapter_number:
                        # 章节编号正确，直接返回
                        return title_text
                    else:
                        # 章节编号不正确，修正为正确的编号
                        logger.warning(f"提取的章节编号({extracted_chapter_num})与期望编号({chapter_number})不一致，已修正")
                        # 提取标题部分（去掉错误的章节编号）
                        title_part = re.sub(r'第\s*\d+\s*章\s*', '', title_text).strip()
                        if title_part:
                            return f"第{chapter_number}章 {title_part}"
                        else:
                            return f"第{chapter_number}章"
                else:
                    # 没有章节编号，添加正确的章节编号
                    return f"第{chapter_number}章 {title_text}"

        # 查找HTML标题标签（如<h1>、<h2>等）
        for line in lines[:10]:
            line = line.strip()
            # 匹配HTML标题标签
            html_title_match = re.search(r'<h[1-6][^>]*>(.*?)</h[1-6]>', line, re.IGNORECASE)
            if html_title_match:
                title_text = html_title_match.group(1).strip()
                title_text = clean_html_tags(title_text)

                # 检查标题中的章节编号是否与期望的章节编号一致
                chapter_match = re.search(r'第\s*(\d+)\s*章', title_text)
                if chapter_match:
                    extracted_chapter_num = int(chapter_match.group(1))
                    if extracted_chapter_num == chapter_number:
                        # 章节编号正确，直接返回
                        return title_text
                    else:
                        # 章节编号不正确，修正为正确的编号
                        logger.warning(f"HTML标题中的章节编号({extracted_chapter_num})与期望编号({chapter_number})不一致，已修正")
                        # 提取标题部分（去掉错误的章节编号）
                        title_part = re.sub(r'第\s*\d+\s*章\s*', '', title_text).strip()
                        if title_part:
                            return f"第{chapter_number}章 {title_part}"
                        else:
                            return f"第{chapter_number}章"
                else:
                    # 没有章节编号，添加正确的章节编号
                    return f"第{chapter_number}章 {title_text}"

        # 如果没有找到标题行，尝试使用第一行非空文本作为标题
        for line in lines[:10]:
            line = line.strip()
            # 清理HTML标签
            clean_line = clean_html_tags(line)
            if clean_line and not clean_line.startswith('#') and len(clean_line) < 50:  # 避免使用过长的段落作为标题
                return f"第{chapter_number}章 {clean_line}"

        # 如果仍然没有找到合适的标题，生成一个新标题
        return TestService._generate_chapter_title(content[:200], chapter_number)

    @staticmethod
    def _generate_chapter_title(content_summary: str, chapter_number: int) -> str:
        """
        根据内容摘要生成章节标题

        Args:
            content_summary: 内容摘要
            chapter_number: 章节编号

        Returns:
            生成的章节标题
        """
        try:
            # 如果内容摘要为空或太短，返回默认标题
            if not content_summary or len(content_summary) < 10:
                return f"第{chapter_number}章"

            # 创建API客户端
            api_client = DeepSeekClient()

            # 构建提示词
            prompt = f"""
请根据以下内容摘要，生成一个简短、吸引人的章节标题（不超过15个字）。
标题应该概括内容的主题或关键事件，不要包含章节编号。

内容摘要:
{content_summary}

只需返回标题文本，不要包含任何解释或额外内容。
"""

            # 调用API生成标题
            response = api_client.analyze_text(
                text=prompt,
                analysis_type="chapter_title_generation",
                max_tokens=50
            )

            # 提取生成的标题
            if response and "content" in response:
                title = response["content"].strip()

                # 移除可能的引号和多余的标点
                title = title.strip('"\'').strip()

                # 如果标题为空或过长，使用默认标题
                if not title or len(title) > 20:
                    return f"第{chapter_number}章"

                # 返回带章节编号的标题
                return f"第{chapter_number}章 {title}"
            else:
                logger.warning(f"生成章节标题失败: API返回的数据格式不正确")
                return f"第{chapter_number}章"
        except Exception as e:
            logger.error(f"生成章节标题时出错: {str(e)}", exc_info=True)
            return f"第{chapter_number}章"

    @staticmethod
    def _generate_analysis_summary(novel: Novel, book_analysis: Dict[str, Dict[str, str]]) -> str:
        """
        生成分析摘要

        Args:
            novel: 小说对象
            book_analysis: 整本书分析结果

        Returns:
            分析摘要
        """
        summary = f"""# 《{novel.title}》分析摘要

## 基本信息
- 标题: {novel.title}
- 作者: {novel.author or '未知'}
- 字数: {novel.word_count}字
- 章节数: {len(novel.chapters)}章

## 主要分析维度
"""

        # 添加主要维度的摘要
        key_dimensions = ["language_style", "rhythm_pacing", "structure",
                         "paragraph_flow", "character_relationships"]

        for dimension in key_dimensions:
            if dimension in book_analysis:
                # 获取维度名称
                dimension_name = next((dim["name"] for dim in config.ANALYSIS_DIMENSIONS if dim["key"] == dimension), dimension)

                # 提取内容的前300个字符作为摘要
                content = book_analysis[dimension]["content"]
                summary_content = content[:300] + "..." if len(content) > 300 else content

                summary += f"### {dimension_name}\n{summary_content}\n\n"

        return summary

    @staticmethod
    def get_task_status(task_id: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            (成功标志, 任务状态, 错误信息)
        """
        try:
            # 添加调试日志
            logger.info(f"正在查询任务状态，任务ID: {task_id}")
            logger.info(f"当前存在的任务: {list(test_tasks.keys())}")

            if task_id not in test_tasks:
                logger.warning(f"未找到任务 {task_id}，当前存在的任务: {list(test_tasks.keys())}")
                return False, {}, f"未找到任务 {task_id}"

            task = test_tasks[task_id]
            logger.info(f"找到任务 {task_id}，当前状态: {task.get('status', 'unknown')}")

            # 安全地获取任务数据
            start_time = task.get("start_time", time.time())
            elapsed_time = time.time() - start_time

            status_data = {
                "status": task.get("status", "未知状态"),
                "progress": task.get("progress", 0),
                "completed": task.get("completed", False),
                "elapsed_time": int(elapsed_time),
                "error": task.get("error", None)
            }

            # 如果任务已完成，添加结果（即使为None也要添加）
            if task.get("completed", False):
                status_data["results"] = task.get("results", None)
                logger.info(f"任务 {task_id} 已完成，结果数据大小: {len(str(status_data['results'])) if status_data['results'] else 0}")

            logger.info(f"成功获取任务 {task_id} 状态，进度: {status_data['progress']}%")
            return True, status_data, ""

        except Exception as e:
            logger.error(f"获取任务状态时出错: {str(e)}", exc_info=True)
            return False, {}, f"获取任务状态失败: {str(e)}"

    @staticmethod
    def get_dimension_detail(novel_id: int, dimension: str) -> Tuple[bool, str, str]:
        """
        获取维度详情

        Args:
            novel_id: 小说ID
            dimension: 维度名称

        Returns:
            (成功标志, 维度详情, 错误信息)
        """
        try:
            session = Session()
            try:
                # 添加调试日志
                logger.info(f"查询小说 {novel_id} 的 {dimension} 维度分析结果")

                # 查询分析结果
                analysis_result = session.query(AnalysisResult).filter(
                    AnalysisResult.novel_id == novel_id,
                    AnalysisResult.dimension == dimension
                ).first()

                if not analysis_result:
                    # 查询该小说的所有分析结果，用于调试
                    all_results = session.query(AnalysisResult).filter(
                        AnalysisResult.novel_id == novel_id
                    ).all()
                    available_dimensions = [r.dimension for r in all_results]

                    # 同时查询章节分析结果
                    chapter_results = session.query(ChapterAnalysisResult).filter(
                        ChapterAnalysisResult.novel_id == novel_id,
                        ChapterAnalysisResult.dimension == dimension
                    ).all()

                    logger.warning(f"未找到小说 {novel_id} 的 {dimension} 维度分析结果。可用维度: {available_dimensions}")
                    logger.info(f"找到 {len(chapter_results)} 个章节分析结果")

                    # 如果有章节分析结果，尝试汇总
                    if chapter_results:
                        logger.info(f"尝试从章节分析结果汇总 {dimension} 维度")
                        summary_content = TestService._summarize_chapter_analysis(chapter_results, dimension)
                        if summary_content:
                            return True, summary_content, ""

                    return False, "", f"未找到小说 {novel_id} 的 {dimension} 维度分析结果。可用维度: {available_dimensions}"

                # 获取分析内容和推理过程
                content = analysis_result.content
                reasoning = ""

                # 尝试从多个位置获取推理内容
                if analysis_result.reasoning_content:
                    reasoning = analysis_result.reasoning_content
                    logger.info(f"从reasoning_content字段获取到推理内容，长度: {len(reasoning)}")
                elif analysis_result.analysis_metadata and "reasoning_content" in analysis_result.analysis_metadata:
                    reasoning = analysis_result.analysis_metadata["reasoning_content"]
                    logger.info(f"从analysis_metadata获取到推理内容，长度: {len(reasoning)}")
                else:
                    logger.warning(f"未找到小说 {novel_id} 的 {dimension} 维度的推理内容")

                # 组合详情
                detail = f"# {dimension} 维度分析\n\n"

                if reasoning:
                    detail += f"## 分析思路\n{reasoning}\n\n"

                detail += f"## 分析结果\n{content}"

                logger.info(f"成功获取小说 {novel_id} 的 {dimension} 维度详情，总长度: {len(detail)}")
                return True, detail, ""
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取维度详情失败: {str(e)}", exc_info=True)
            return False, "", f"获取维度详情失败: {str(e)}"

    @staticmethod
    def _summarize_chapter_analysis(chapter_results: List, dimension: str) -> str:
        """
        从章节分析结果汇总维度分析

        Args:
            chapter_results: 章节分析结果列表
            dimension: 维度名称

        Returns:
            汇总的分析内容
        """
        try:
            if not chapter_results:
                return ""

            # 获取维度的中文名称
            dimension_mapping = {
                "language_style": "语言风格",
                "rhythm_pacing": "节奏节拍",
                "structure": "结构分析",
                "character_relationships": "人物关系",
                "world_building": "世界构建",
                "opening_effect": "开篇效果",
                "climax_pacing": "高潮节奏",
                "novel_features": "小说特点",
                "sentence_variation": "句式变化",
                "perspective_changes": "视角变化",
                "paragraph_flow": "段落流畅度",
                "chapter_outline": "章纲分析",
                "outline_analysis": "大纲分析",
                "hot_meme_statistics": "热梗统计"
            }

            dimension_name = dimension_mapping.get(dimension, dimension.replace("_", " ").title())

            # 构建汇总内容
            summary = f"# {dimension_name}维度分析汇总\n\n"
            summary += f"基于{len(chapter_results)}个章节的分析结果汇总\n\n"

            # 汇总各章节的分析内容
            for i, result in enumerate(chapter_results, 1):
                if result.content:
                    summary += f"## 第{i}章分析\n"
                    # 只取前200字符作为摘要
                    content_summary = result.content[:200] + "..." if len(result.content) > 200 else result.content
                    summary += f"{content_summary}\n\n"

            # 添加整体总结
            summary += "## 整体特征\n"
            summary += f"通过对{len(chapter_results)}个章节的{dimension_name}分析，可以看出该作品在此维度上具有一定的特色和规律。"
            summary += "具体的深度分析需要进行整本书的专门分析。\n\n"

            return summary

        except Exception as e:
            logger.error(f"汇总章节分析时出错: {str(e)}", exc_info=True)
            return ""

    @staticmethod
    def _get_analysis_progress_feedback(prompt_template: str) -> str:
        """
        获取分析进度反馈信息，区分精简版和默认版

        Args:
            prompt_template: 提示词模板类型

        Returns:
            进度反馈信息
        """
        if prompt_template == "simplified":
            return "💰 正在使用精简版模式进行分析（降本增效）..."
        else:
            return "📚 正在使用默认版模式进行分析（完整详细）..."

    @staticmethod
    def _get_template_status_info(prompt_template: str) -> Dict[str, str]:
        """
        获取模板状态信息，用于前端显示

        Args:
            prompt_template: 提示词模板类型

        Returns:
            模板状态信息字典
        """
        from src.api.prompt_template_manager import PromptTemplateManager

        template_type = PromptTemplateManager.get_template_type_from_string(prompt_template)
        template_info = PromptTemplateManager.get_template_info(template_type)

        return {
            "template_name": template_info['name'],
            "template_description": template_info['description'],
            "template_icon": template_info['icon'],
            "template_color": template_info['color'],
            "template_type": prompt_template
        }

    @staticmethod
    def _analyze_book_in_segments(analyzer, novel_data: Dict, dimensions: List[str],
                                 segment_size: int, api_delay: float, prompt_template: str,
                                 task_id: str, session) -> Dict[str, Dict[str, str]]:
        """
        分段分析整本书，避免高并发触发扩缩容机制（精简版专用降本增效策略）

        注意事项：
        1. 网络延迟影响：多次调用会引入额外延迟，通过本地预处理和合理分块减少调用次数
        2. 上下文保持：通过传递分析上下文信息确保连贯性
        3. 费用权衡：虽然避免了扩缩容费用，但多次调用仍有固定成本

        Args:
            analyzer: 分析器对象
            novel_data: 小说数据
            dimensions: 分析维度列表
            segment_size: 每段分析的维度数量
            api_delay: API调用延迟
            prompt_template: 提示词模板
            task_id: 任务ID
            session: 数据库会话

        Returns:
            分析结果字典
        """
        import time
        import concurrent.futures

        book_analysis_results = {}
        total_dimensions = len(dimensions)

        # 将维度分成多个段落，减少调用次数
        dimension_segments = []
        for i in range(0, total_dimensions, segment_size):
            segment = dimensions[i:i + segment_size]
            dimension_segments.append(segment)

        logger.info(f"[分段分析策略] 将{total_dimensions}个维度分成{len(dimension_segments)}段，每段最多{segment_size}个维度")
        logger.info(f"[降本增效] 预计减少{total_dimensions - len(dimension_segments)}次并发峰值，避免扩缩容费用")

        # 构建上下文信息，确保分析连贯性
        analysis_context = {
            "novel_title": novel_data.get("title", ""),
            "novel_content_length": len(novel_data.get("content", "")),
            "previous_segments": []
        }

        # 逐段分析，避免高并发
        for segment_index, dimension_segment in enumerate(dimension_segments):
            segment_start_time = time.time()

            # 更新任务状态
            test_tasks[task_id]["status"] = f"🔄 正在分析第{segment_index + 1}/{len(dimension_segments)}段维度（精简版分段策略）"
            progress = 20 + int(30 * (segment_index / len(dimension_segments)))
            test_tasks[task_id]["progress"] = progress

            logger.info(f"[分段分析策略] 开始分析第{segment_index + 1}段，包含维度: {dimension_segment}")

            # 段间延迟，避免扩缩容（考虑网络延迟影响）
            if segment_index > 0:
                # 根据前一段的处理时间动态调整延迟
                adaptive_delay = api_delay * 2  # 段间使用更长延迟
                logger.info(f"[分段分析策略] 段间延迟{adaptive_delay:.1f}秒，避免触发扩缩容机制")
                time.sleep(adaptive_delay)

            # 在每段内部使用小规模并行（最多3个并发）
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=min(segment_size, 3),
                thread_name_prefix=f"segment_{segment_index + 1}_analysis"
            ) as executor:

                # 创建当前段的分析任务
                future_to_dimension = {}

                for dim_index, dimension in enumerate(dimension_segment):
                    # 维度间延迟（减少网络延迟影响）
                    if dim_index > 0:
                        time.sleep(api_delay)
                        logger.info(f"[分段分析策略] 维度间延迟{api_delay}秒后分析: {dimension}")

                    # 构建包含上下文的分析请求
                    enhanced_content = novel_data["content"]
                    if analysis_context["previous_segments"]:
                        # 添加前序分析的关键信息作为上下文
                        context_summary = TestService._build_analysis_context(analysis_context["previous_segments"])
                        enhanced_content = f"[分析上下文]\n{context_summary}\n\n[待分析内容]\n{enhanced_content}"

                    future = executor.submit(
                        TestService._analyze_dimension_with_retry,
                        analyzer,
                        enhanced_content,
                        dimension,
                        novel_data["title"],
                        prompt_template,
                        0.5  # 精简版使用额外延迟
                    )
                    future_to_dimension[future] = dimension

                # 处理当前段的完成任务
                completed_in_segment = 0
                segment_results = {}

                for future in concurrent.futures.as_completed(future_to_dimension):
                    dimension = future_to_dimension[future]
                    completed_in_segment += 1

                    # 更新详细进度
                    test_tasks[task_id]["status"] = f"⚙️ 第{segment_index + 1}段: {dimension}（{completed_in_segment}/{len(dimension_segment)}）"

                    try:
                        # 获取分析结果
                        result_data = future.result()

                        # 保存分析结果
                        if result_data and "content" in result_data:
                            # 提取推理内容
                            reasoning_content = (
                                result_data.get("reasoning", "") or
                                result_data.get("reasoning_content", "") or
                                result_data.get("analysis_reasoning", "") or
                                ""
                            )

                            # 创建分析结果对象
                            analysis_result = AnalysisResult(
                                novel_id=novel_data["id"],
                                dimension=dimension,
                                content=result_data["content"],
                                reasoning_content=reasoning_content,
                                metadata={
                                    "source": "test_service_segmented",
                                    "segment_index": segment_index,
                                    "segment_size": segment_size,
                                    "cost_optimization": "分段分析避免扩缩容",
                                    "reasoning_content": reasoning_content
                                }
                            )
                            session.add(analysis_result)
                            session.commit()

                            # 保存到结果字典
                            book_analysis_results[dimension] = {
                                "content": result_data["content"],
                                "reasoning": reasoning_content,
                                "reasoning_content": reasoning_content
                            }

                            # 保存到段结果中，用于构建上下文
                            segment_results[dimension] = {
                                "content": result_data["content"][:200],  # 只保存前200字作为上下文
                                "dimension": dimension
                            }

                            logger.info(f"[分段分析策略] 成功分析维度 {dimension}")
                        else:
                            logger.warning(f"[分段分析策略] 维度 {dimension} 分析失败: 返回数据格式不正确")
                    except Exception as e:
                        logger.error(f"[分段分析策略] 分析维度 {dimension} 时出错: {str(e)}", exc_info=True)

            # 将当前段的结果添加到上下文中
            analysis_context["previous_segments"].append({
                "segment_index": segment_index,
                "dimensions": dimension_segment,
                "results": segment_results
            })

            segment_end_time = time.time()
            segment_duration = segment_end_time - segment_start_time
            logger.info(f"[分段分析策略] 第{segment_index + 1}段分析完成，耗时{segment_duration:.1f}秒")

            # 段后资源清理
            TestService._cleanup_segment_resources(session, segment_index)

        logger.info(f"[分段分析策略] 所有{len(dimension_segments)}段分析完成，共分析{len(book_analysis_results)}个维度")
        logger.info(f"[降本增效] 成功避免{total_dimensions}个维度同时并发，预计节省扩缩容费用")
        return book_analysis_results

    @staticmethod
    def _analyze_book_parallel(analyzer, novel_data: Dict, dimensions: List[str],
                              max_workers: int, api_delay: float, prompt_template: str,
                              task_id: str, session) -> Dict[str, Dict[str, str]]:
        """
        传统并行分析整本书（默认版使用）

        Args:
            analyzer: 分析器对象
            novel_data: 小说数据
            dimensions: 分析维度列表
            max_workers: 最大并行数
            api_delay: API调用延迟
            prompt_template: 提示词模板
            task_id: 任务ID
            session: 数据库会话

        Returns:
            分析结果字典
        """
        import time
        import concurrent.futures

        book_analysis_results = {}

        # 使用线程池并行处理多个维度
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="book_analysis") as executor:
            # 创建维度分析任务
            future_to_dimension = {}

            for i, dimension in enumerate(dimensions):
                # API调用延迟
                if i > 0:
                    time.sleep(api_delay)
                    logger.info(f"[传统并行分析] API调用延迟{api_delay}秒后分析维度: {dimension}")

                future = executor.submit(
                    TestService._analyze_dimension_with_retry,
                    analyzer,
                    novel_data["content"],
                    dimension,
                    novel_data["title"],
                    prompt_template,
                    None  # 默认版不使用额外延迟
                )
                future_to_dimension[future] = dimension

            # 处理完成的任务
            completed_count = 0
            for future in concurrent.futures.as_completed(future_to_dimension):
                dimension = future_to_dimension[future]
                completed_count += 1

                # 更新进度
                progress = 20 + int(30 * (completed_count / len(dimensions)))
                test_tasks[task_id]["status"] = f"已完成 {completed_count}/{len(dimensions)} 个维度分析（默认版）"
                test_tasks[task_id]["progress"] = progress

                try:
                    # 获取分析结果
                    result_data = future.result()

                    # 保存分析结果
                    if result_data and "content" in result_data:
                        # 提取推理内容
                        reasoning_content = (
                            result_data.get("reasoning", "") or
                            result_data.get("reasoning_content", "") or
                            result_data.get("analysis_reasoning", "") or
                            ""
                        )

                        # 创建分析结果对象
                        analysis_result = AnalysisResult(
                            novel_id=novel_data["id"],
                            dimension=dimension,
                            content=result_data["content"],
                            reasoning_content=reasoning_content,
                            metadata={
                                "source": "test_service_parallel",
                                "reasoning_content": reasoning_content
                            }
                        )
                        session.add(analysis_result)
                        session.commit()

                        # 保存到结果字典
                        book_analysis_results[dimension] = {
                            "content": result_data["content"],
                            "reasoning": reasoning_content,
                            "reasoning_content": reasoning_content
                        }

                        logger.info(f"[传统并行分析] 成功分析维度 {dimension}")
                    else:
                        logger.warning(f"[传统并行分析] 维度 {dimension} 分析失败: 返回数据格式不正确")
                except Exception as e:
                    logger.error(f"[传统并行分析] 分析维度 {dimension} 时出错: {str(e)}", exc_info=True)

        return book_analysis_results

    @staticmethod
    def _cleanup_analysis_resources(session, task_id: str, prompt_template: str):
        """
        分析完成后的资源清理（降本增效策略1：定期清理资源）

        确保全部分析完成后及时释放不再使用的资源，避免产生闲置费用

        Args:
            session: 数据库会话
            task_id: 任务ID
            prompt_template: 提示词模板
        """
        import gc
        import psutil
        import os
        import time

        try:
            # 记录清理前的资源状态
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB

            logger.info(f"[资源清理] 开始清理分析资源，清理前内存使用: {memory_before:.1f}MB")

            # 1. 清理数据库连接池中的闲置连接
            try:
                from src.db.connection import engine
                if hasattr(engine.pool, 'checkedin'):
                    checked_in_count = engine.pool.checkedin()
                    if checked_in_count > 5:  # 如果闲置连接超过5个
                        engine.pool.dispose()  # 释放连接池
                        logger.info(f"[资源清理] 释放了{checked_in_count}个闲置数据库连接")
            except Exception as e:
                logger.warning(f"[资源清理] 清理数据库连接时出错: {str(e)}")

            # 2. 强制垃圾回收
            collected = gc.collect()
            logger.info(f"[资源清理] 垃圾回收释放了{collected}个对象")

            # 3. 清理任务相关的临时数据
            if task_id in test_tasks:
                # 保留必要的状态信息，清理大型数据
                task = test_tasks[task_id]
                if "temp_data" in task:
                    del task["temp_data"]
                if "large_results" in task:
                    del task["large_results"]
                logger.info(f"[资源清理] 清理任务{task_id}的临时数据")

            # 4. 精简版模式下的额外清理
            if prompt_template == "simplified":
                # 清理分析缓存（精简版可以更激进地清理）
                try:
                    from src.api.deepseek_client import DeepSeekClient
                    if hasattr(DeepSeekClient, '_clear_analysis_cache'):
                        DeepSeekClient._clear_analysis_cache()
                        logger.info(f"[资源清理] 精简版模式：清理分析缓存")
                except Exception as e:
                    logger.warning(f"[资源清理] 清理分析缓存时出错: {str(e)}")

                # 清理线程池资源
                import threading
                active_threads = threading.active_count()
                if active_threads > 10:  # 如果活跃线程过多
                    logger.info(f"[资源清理] 精简版模式：检测到{active_threads}个活跃线程，建议检查线程泄漏")

            # 5. 清理系统缓存（如果可能）
            try:
                if hasattr(os, 'sync'):
                    os.sync()  # 同步文件系统缓存
            except Exception:
                pass

            # 记录清理后的资源状态
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_saved = memory_before - memory_after

            logger.info(f"[资源清理] 资源清理完成，清理后内存使用: {memory_after:.1f}MB，节省: {memory_saved:.1f}MB")

            # 更新任务状态
            if task_id in test_tasks:
                if prompt_template == "simplified":
                    test_tasks[task_id]["status"] = "💰 分析完成，已执行降本增效资源清理"
                else:
                    test_tasks[task_id]["status"] = "✅ 分析完成，已执行资源清理"

                # 记录资源清理信息
                test_tasks[task_id]["resource_cleanup"] = {
                    "memory_saved_mb": round(memory_saved, 1),
                    "objects_collected": collected,
                    "cleanup_time": time.time()
                }

        except Exception as e:
            logger.error(f"[资源清理] 资源清理过程中出错: {str(e)}", exc_info=True)

    @staticmethod
    def _cleanup_segment_resources(session, segment_index: int):
        """
        分段分析后的资源清理

        Args:
            session: 数据库会话
            segment_index: 段索引
        """
        import gc

        try:
            # 轻量级清理，避免影响性能
            if segment_index % 2 == 0:  # 每两段清理一次
                collected = gc.collect()
                logger.info(f"[段资源清理] 第{segment_index + 1}段后清理，回收{collected}个对象")

            # 提交数据库事务，释放锁
            session.commit()

        except Exception as e:
            logger.warning(f"[段资源清理] 第{segment_index + 1}段资源清理时出错: {str(e)}")

    @staticmethod
    def _build_analysis_context(previous_segments: List[Dict]) -> str:
        """
        构建分析上下文，确保分段分析的连贯性

        Args:
            previous_segments: 前序分析段的结果

        Returns:
            上下文摘要字符串
        """
        if not previous_segments:
            return ""

        context_parts = []
        for segment in previous_segments[-2:]:  # 只保留最近2段的上下文
            segment_info = f"第{segment['segment_index'] + 1}段分析了: {', '.join(segment['dimensions'])}"

            # 添加关键分析结果摘要
            if segment.get('results'):
                key_insights = []
                for dim, result in segment['results'].items():
                    if result.get('content'):
                        insight = f"{dim}: {result['content'][:50]}..."
                        key_insights.append(insight)

                if key_insights:
                    segment_info += f"\n关键发现: {'; '.join(key_insights[:3])}"  # 最多3个关键发现

            context_parts.append(segment_info)

        return "\n".join(context_parts)



    @staticmethod
    def _configure_reserved_instances(task_id: str, dimension_count: int):
        """
        配置预留实例优化（降本增效策略2：提前预留实例）

        在函数计算中配置预留实例，以应对突发流量。预留实例不会因流量波动而被释放，
        从而避免冷启动和频繁扩缩容带来的额外开销。

        Args:
            task_id: 任务ID
            dimension_count: 维度数量
        """
        try:
            import time

            # 根据维度数量计算所需的预留实例数
            if dimension_count <= 5:
                reserved_count = 1
            elif dimension_count <= 10:
                reserved_count = 2
            else:
                reserved_count = 3  # 最多预留3个实例

            logger.info(f"[预留实例优化] 根据{dimension_count}个维度，配置{reserved_count}个预留实例")

            # 模拟预留实例配置（实际环境中需要调用云服务API）
            reserved_config = {
                "instance_count": reserved_count,
                "instance_type": "lightweight",  # 轻量级实例，降低成本
                "auto_scale_enabled": False,  # 禁用自动扩缩容
                "keep_alive_duration": 300,  # 保持活跃5分钟
                "configured_at": time.time()
            }

            # 预热实例连接池
            TestService._warmup_instance_pool(reserved_count)

            # 更新任务状态
            if task_id in test_tasks:
                test_tasks[task_id]["reserved_instances"] = reserved_config
                test_tasks[task_id]["status"] = f"🔧 已配置{reserved_count}个预留实例，避免冷启动开销"

            logger.info(f"[预留实例优化] 预留实例配置完成：{reserved_config}")

        except Exception as e:
            logger.error(f"[预留实例优化] 配置预留实例时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _setup_async_processing(task_id: str, prompt_template: str):
        """
        设置异步调用优化（降本增效策略3：使用异步调用）

        对于耗时较长的任务，使用异步调用方式。异步调用可以将任务从主流程中剥离，
        避免阻塞主线程，同时减少对资源的即时占用。

        Args:
            task_id: 任务ID
            prompt_template: 提示词模板
        """
        try:
            import time

            # 配置异步处理参数 - 效率与成本平衡优化
            async_config = {
                "enabled": True,
                "queue_size": 50,  # 异步队列大小
                "timeout": 300,  # 超时时间（秒）
                "configured_at": time.time()
            }

            if prompt_template == "simplified":
                # 精简版：效率与成本平衡配置
                async_config.update({
                    "worker_threads": 5,  # 提升到5个线程，平衡效率与成本
                    "instance_concurrency": 8,  # 单实例并发度8（I/O密集型推荐5-10）
                    "batch_size": 3,  # 适中的批量大小
                    "priority": "balanced",  # 平衡优先级
                    "connection_pool_size": 15,  # 连接池大小
                    "connection_reuse": True,  # 启用连接复用
                    "warmup_enabled": True  # 启用预热
                })
                logger.info(f"[效率成本平衡] 精简版模式：5个工作线程，单实例并发度8，连接池15")
            else:
                # 默认版：性能优先配置
                async_config.update({
                    "worker_threads": 12,  # 默认版使用更多线程
                    "instance_concurrency": 10,  # 单实例并发度10
                    "batch_size": 5,  # 更大的批量大小
                    "priority": "high",  # 高优先级
                    "connection_pool_size": 30,  # 更大的连接池
                    "connection_reuse": True,
                    "warmup_enabled": True
                })
                logger.info(f"[性能优先] 默认版模式：12个工作线程，单实例并发度10，连接池30")

            # 初始化连接池管理
            TestService._initialize_connection_pool(task_id, async_config)

            # 配置单实例并发度
            TestService._configure_instance_concurrency(task_id, async_config)

            # 初始化异步任务队列
            TestService._initialize_async_queue(task_id, async_config)

            # 启动异步工作线程
            TestService._start_async_workers(task_id, async_config)

            # 更新任务状态
            if task_id in test_tasks:
                test_tasks[task_id]["async_processing"] = async_config
                test_tasks[task_id]["status"] = f"⚡ 已启用异步处理，{async_config['worker_threads']}个工作线程"

            logger.info(f"[异步处理优化] 异步处理配置完成：{async_config}")

        except Exception as e:
            logger.error(f"[异步处理优化] 设置异步处理时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _warmup_instance_pool(instance_count: int):
        """
        预热实例连接池

        Args:
            instance_count: 实例数量
        """
        try:
            import time
            import threading

            def warmup_instance(instance_id: int):
                """预热单个实例"""
                try:
                    # 模拟实例预热过程
                    time.sleep(0.1)  # 短暂延迟模拟预热
                    logger.info(f"[实例预热] 实例 {instance_id} 预热完成")
                except Exception as e:
                    logger.warning(f"[实例预热] 实例 {instance_id} 预热失败: {str(e)}")

            # 并行预热所有实例
            threads = []
            for i in range(instance_count):
                thread = threading.Thread(target=warmup_instance, args=(i + 1,))
                thread.start()
                threads.append(thread)

            # 等待所有预热完成
            for thread in threads:
                thread.join(timeout=5)  # 最多等待5秒

            logger.info(f"[实例预热] {instance_count}个实例预热完成")

        except Exception as e:
            logger.error(f"[实例预热] 预热实例池时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _initialize_async_queue(task_id: str, async_config: Dict):
        """
        初始化异步任务队列

        Args:
            task_id: 任务ID
            async_config: 异步配置
        """
        try:
            import queue
            import threading

            # 创建任务队列
            task_queue = queue.Queue(maxsize=async_config["queue_size"])
            result_queue = queue.Queue()

            # 存储队列引用
            if not hasattr(TestService, '_async_queues'):
                TestService._async_queues = {}

            TestService._async_queues[task_id] = {
                "task_queue": task_queue,
                "result_queue": result_queue,
                "lock": threading.Lock()
            }

            logger.info(f"[异步队列] 任务 {task_id} 的异步队列初始化完成，队列大小: {async_config['queue_size']}")

        except Exception as e:
            logger.error(f"[异步队列] 初始化异步队列时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _start_async_workers(task_id: str, async_config: Dict):
        """
        启动异步工作线程

        Args:
            task_id: 任务ID
            async_config: 异步配置
        """
        try:
            import threading
            import queue
            import time

            def async_worker(worker_id: int):
                """异步工作线程函数"""
                try:
                    if task_id not in TestService._async_queues:
                        return

                    task_queue = TestService._async_queues[task_id]["task_queue"]
                    result_queue = TestService._async_queues[task_id]["result_queue"]

                    logger.info(f"[异步工作线程] 工作线程 {worker_id} 启动")

                    while True:
                        try:
                            # 从队列获取任务（带超时）
                            task = task_queue.get(timeout=async_config["timeout"])

                            if task is None:  # 停止信号
                                break

                            # 处理任务
                            start_time = time.time()
                            result = TestService._process_async_task(task)
                            end_time = time.time()

                            # 将结果放入结果队列
                            result_queue.put({
                                "task_id": task.get("id"),
                                "result": result,
                                "processing_time": end_time - start_time,
                                "worker_id": worker_id
                            })

                            # 标记任务完成
                            task_queue.task_done()

                            logger.info(f"[异步工作线程] 工作线程 {worker_id} 完成任务 {task.get('id')}")

                        except queue.Empty:
                            # 队列为空，继续等待
                            continue
                        except Exception as e:
                            logger.error(f"[异步工作线程] 工作线程 {worker_id} 处理任务时出错: {str(e)}")
                            task_queue.task_done()

                except Exception as e:
                    logger.error(f"[异步工作线程] 工作线程 {worker_id} 启动失败: {str(e)}")

            # 启动工作线程
            if not hasattr(TestService, '_async_workers'):
                TestService._async_workers = {}

            TestService._async_workers[task_id] = []

            for i in range(async_config["worker_threads"]):
                worker = threading.Thread(
                    target=async_worker,
                    args=(i + 1,),
                    name=f"AsyncWorker-{task_id}-{i + 1}"
                )
                worker.daemon = True  # 守护线程
                worker.start()
                TestService._async_workers[task_id].append(worker)

            logger.info(f"[异步工作线程] 任务 {task_id} 启动了 {async_config['worker_threads']} 个异步工作线程")

        except Exception as e:
            logger.error(f"[异步工作线程] 启动异步工作线程时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _process_async_task(task: Dict) -> Dict:
        """
        处理异步任务

        Args:
            task: 任务字典

        Returns:
            处理结果
        """
        try:
            # 模拟异步任务处理
            import time

            task_type = task.get("type", "unknown")
            task_data = task.get("data", {})

            logger.info(f"[异步任务处理] 开始处理任务类型: {task_type}")

            # 根据任务类型进行不同的处理
            if task_type == "analysis":
                # 分析任务
                result = {
                    "status": "completed",
                    "content": f"异步分析完成: {task_data.get('dimension', 'unknown')}",
                    "processing_time": time.time() - task.get("created_at", time.time())
                }
            elif task_type == "cleanup":
                # 清理任务
                result = {
                    "status": "completed",
                    "content": "异步资源清理完成",
                    "processing_time": time.time() - task.get("created_at", time.time())
                }
            else:
                # 默认处理
                result = {
                    "status": "completed",
                    "content": f"异步任务处理完成: {task_type}",
                    "processing_time": time.time() - task.get("created_at", time.time())
                }

            logger.info(f"[异步任务处理] 任务处理完成: {task_type}")
            return result

        except Exception as e:
            logger.error(f"[异步任务处理] 处理异步任务时出错: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "error": str(e),
                "processing_time": 0
            }

    @staticmethod
    def _optimize_trigger_rules(task_id: str, prompt_template: str):
        """
        优化触发器规则（降本增效策略4：优化触发器规则）

        通过设置触发器规则来平滑流量：
        - 限流：限制触发器的并发调用速率，避免瞬间涌入大量请求
        - 批量处理：将多个事件合并为一个请求，减少调用次数

        Args:
            task_id: 任务ID
            prompt_template: 提示词模板
        """
        try:
            import time

            # 配置触发器优化规则
            trigger_config = {
                "rate_limit": {
                    "enabled": True,
                    "max_requests_per_second": 5 if prompt_template == "simplified" else 10,
                    "burst_limit": 10 if prompt_template == "simplified" else 20
                },
                "batch_processing": {
                    "enabled": True,
                    "batch_size": 3 if prompt_template == "simplified" else 5,
                    "batch_timeout": 30,  # 30秒超时
                    "max_wait_time": 60  # 最大等待时间
                },
                "flow_control": {
                    "enabled": True,
                    "smooth_factor": 0.8 if prompt_template == "simplified" else 0.6,
                    "backoff_strategy": "exponential"
                },
                "configured_at": time.time()
            }

            if prompt_template == "simplified":
                logger.info(f"[触发器优化] 精简版模式：配置保守触发器规则，限流{trigger_config['rate_limit']['max_requests_per_second']}req/s")
            else:
                logger.info(f"[触发器优化] 默认版模式：配置标准触发器规则，限流{trigger_config['rate_limit']['max_requests_per_second']}req/s")

            # 初始化批量处理队列
            TestService._initialize_batch_queue(task_id, trigger_config)

            # 启动流量平滑处理
            TestService._start_flow_smoothing(task_id, trigger_config)

            # 更新任务状态
            if task_id in test_tasks:
                test_tasks[task_id]["trigger_optimization"] = trigger_config
                test_tasks[task_id]["status"] = f"🚦 已优化触发器规则，限流{trigger_config['rate_limit']['max_requests_per_second']}req/s"

            logger.info(f"[触发器优化] 触发器规则优化完成：{trigger_config}")

        except Exception as e:
            logger.error(f"[触发器优化] 优化触发器规则时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _initialize_batch_queue(task_id: str, trigger_config: Dict):
        """
        初始化批量处理队列

        Args:
            task_id: 任务ID
            trigger_config: 触发器配置
        """
        try:
            import queue
            import threading
            import time

            # 从配置中获取批量处理参数
            batch_size = trigger_config.get("batch_processing", {}).get("batch_size", 10)
            batch_timeout = trigger_config.get("batch_processing", {}).get("batch_timeout", 5)

            # 创建批量处理队列
            batch_queue = queue.Queue(maxsize=batch_size * 2)

            # 存储批量队列引用
            if not hasattr(TestService, '_batch_queues'):
                TestService._batch_queues = {}

            TestService._batch_queues[task_id] = {
                "queue": batch_queue,
                "current_batch": [],
                "last_batch_time": time.time(),
                "lock": threading.Lock(),
                "batch_size": batch_size,
                "batch_timeout": batch_timeout
            }

            logger.info(f"[批量处理] 任务 {task_id} 的批量处理队列初始化完成，批量大小: {batch_size}，超时: {batch_timeout}秒")

        except Exception as e:
            logger.error(f"[批量处理] 初始化批量处理队列时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _start_flow_smoothing(task_id: str, trigger_config: Dict):
        """
        启动流量平滑处理

        Args:
            task_id: 任务ID
            trigger_config: 触发器配置
        """
        try:
            import threading
            import time

            def flow_smoother():
                """流量平滑处理函数"""
                try:
                    logger.info(f"[流量平滑] 任务 {task_id} 的流量平滑处理启动")

                    rate_limit = trigger_config["rate_limit"]["max_requests_per_second"]
                    interval = 1.0 / rate_limit  # 请求间隔

                    while True:
                        try:
                            # 检查是否有待处理的批量任务
                            if task_id in TestService._batch_queues:
                                batch_info = TestService._batch_queues[task_id]

                                with batch_info["lock"]:
                                    current_time = time.time()
                                    batch_timeout = trigger_config["batch_processing"]["batch_timeout"]

                                    # 检查是否需要处理当前批次
                                    should_process = (
                                        len(batch_info["current_batch"]) >= trigger_config["batch_processing"]["batch_size"] or
                                        (batch_info["current_batch"] and
                                         current_time - batch_info["last_batch_time"] >= batch_timeout)
                                    )

                                    if should_process and batch_info["current_batch"]:
                                        # 处理当前批次
                                        batch = batch_info["current_batch"].copy()
                                        batch_info["current_batch"].clear()
                                        batch_info["last_batch_time"] = current_time

                                        logger.info(f"[流量平滑] 处理批次，包含 {len(batch)} 个请求")

                                        # 这里可以添加实际的批量处理逻辑
                                        TestService._process_batch(batch, task_id)

                            # 按照限流速率等待
                            time.sleep(interval)

                        except Exception as e:
                            logger.error(f"[流量平滑] 流量平滑处理出错: {str(e)}")
                            time.sleep(1)  # 出错时等待1秒

                except Exception as e:
                    logger.error(f"[流量平滑] 流量平滑处理启动失败: {str(e)}")

            # 启动流量平滑线程
            if not hasattr(TestService, '_flow_smoothers'):
                TestService._flow_smoothers = {}

            smoother_thread = threading.Thread(
                target=flow_smoother,
                name=f"FlowSmoother-{task_id}"
            )
            smoother_thread.daemon = True
            smoother_thread.start()

            TestService._flow_smoothers[task_id] = smoother_thread

            logger.info(f"[流量平滑] 任务 {task_id} 的流量平滑处理启动完成")

        except Exception as e:
            logger.error(f"[流量平滑] 启动流量平滑处理时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _process_batch(batch: List[Dict], task_id: str):
        """
        处理批量任务

        Args:
            batch: 批量任务列表
            task_id: 任务ID
        """
        try:
            import time

            start_time = time.time()

            # 批量处理逻辑
            results = []
            for item in batch:
                # 模拟批量处理
                result = {
                    "item_id": item.get("id"),
                    "status": "processed",
                    "processed_at": time.time()
                }
                results.append(result)

            end_time = time.time()
            processing_time = end_time - start_time

            logger.info(f"[批量处理] 任务 {task_id} 批量处理完成，处理了 {len(batch)} 个项目，耗时 {processing_time:.2f}秒")

            # 更新任务状态
            if task_id in test_tasks:
                if "batch_processing_stats" not in test_tasks[task_id]:
                    test_tasks[task_id]["batch_processing_stats"] = {
                        "total_batches": 0,
                        "total_items": 0,
                        "total_time": 0
                    }

                stats = test_tasks[task_id]["batch_processing_stats"]
                stats["total_batches"] += 1
                stats["total_items"] += len(batch)
                stats["total_time"] += processing_time

        except Exception as e:
            logger.error(f"[批量处理] 处理批量任务时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _apply_serialization_bottleneck_optimization(task_id: str, prompt_template: str):
        """
        应用串行化瓶颈优化（在现有降本增效框架基础上）

        专门解决系统中的串行化瓶颈问题：
        1. 数据库操作串行化
        2. API调用延迟强制串行化
        3. 资源竞争导致的串行化
        4. 线程池瓶颈

        Args:
            task_id: 任务ID
            prompt_template: 提示词模板
        """
        try:
            from src.config.serialization_bottleneck_optimizer import SerializationBottleneckOptimizer

            # 创建串行化瓶颈优化器
            optimizer = SerializationBottleneckOptimizer(prompt_template=prompt_template)

            # 应用所有优化
            optimization_results = optimizer.apply_all_optimizations(task_id)

            if optimization_results.get("status") == "completed":
                # 更新任务状态
                if task_id in test_tasks:
                    test_tasks[task_id]["serialization_optimization"] = optimization_results
                    optimizations = optimization_results.get("optimizations_applied", [])

                    if prompt_template == "simplified":
                        test_tasks[task_id]["status"] = f"🔧 已应用串行化瓶颈优化（精简版）: {', '.join(optimizations)}"
                        test_tasks[task_id]["cost_optimization"] += " + 串行化瓶颈优化（数据库批处理+API管道+资源调度）"
                    else:
                        test_tasks[task_id]["status"] = f"🔧 已应用串行化瓶颈优化（默认版）: {', '.join(optimizations)}"

                logger.info(f"[串行化瓶颈优化] 任务{task_id}的串行化瓶颈优化完成，应用了: {optimizations}")
            else:
                logger.error(f"[串行化瓶颈优化] 任务{task_id}的优化失败: {optimization_results.get('error')}")

        except Exception as e:
            logger.error(f"[串行化瓶颈优化] 应用串行化瓶颈优化时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _initialize_connection_pool(task_id: str, async_config: Dict):
        """
        初始化连接池管理（效率与成本平衡优化）

        解决连接数持续上升、任务耗时增加等问题，通过对象池复用SDK对象

        Args:
            task_id: 任务ID
            async_config: 异步配置
        """
        try:
            import threading
            import queue
            import time

            pool_size = async_config.get("connection_pool_size", 15)
            connection_reuse = async_config.get("connection_reuse", True)

            logger.info(f"[连接池管理] 初始化连接池，大小: {pool_size}，复用: {connection_reuse}")

            # 创建连接池
            connection_pool = {
                "pool": queue.Queue(maxsize=pool_size),
                "active_connections": {},
                "pool_stats": {
                    "created": 0,
                    "reused": 0,
                    "released": 0,
                    "max_active": 0
                },
                "lock": threading.Lock(),
                "created_at": time.time()
            }

            # 预创建连接对象
            for i in range(min(pool_size // 2, 5)):  # 预创建一半连接，最多5个
                connection_obj = TestService._create_connection_object(i)
                connection_pool["pool"].put(connection_obj)
                connection_pool["pool_stats"]["created"] += 1

            # 存储连接池引用
            if not hasattr(TestService, '_connection_pools'):
                TestService._connection_pools = {}

            TestService._connection_pools[task_id] = connection_pool

            logger.info(f"[连接池管理] 任务 {task_id} 连接池初始化完成，预创建 {min(pool_size // 2, 5)} 个连接")

        except Exception as e:
            logger.error(f"[连接池管理] 初始化连接池时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _configure_instance_concurrency(task_id: str, async_config: Dict):
        """
        配置单实例并发度优化（效率与成本平衡的核心）

        通过调整单实例并发度，在保证效率的同时降低资源占用和费用：
        - 单实例多并发：多个请求共享一个实例，显著节省费用
        - I/O密集型任务推荐并发度5-10

        Args:
            task_id: 任务ID
            async_config: 异步配置
        """
        try:
            import time

            instance_concurrency = async_config.get("instance_concurrency", 8)
            worker_threads = async_config.get("worker_threads", 5)

            # 计算最优配置
            concurrency_config = {
                "instance_concurrency": instance_concurrency,
                "max_instances": max(2, worker_threads // instance_concurrency + 1),
                "request_timeout": 60,  # 请求超时时间
                "cold_start_optimization": True,
                "billing_optimization": {
                    "shared_instance_billing": True,  # 共享实例计费
                    "execution_time_calculation": "actual_occupancy",  # 按实际占用时间计费
                    "estimated_cost_reduction": f"{((instance_concurrency - 1) / instance_concurrency * 100):.1f}%"
                },
                "flow_control": {
                    "max_concurrent_requests": 100 * instance_concurrency,  # 单地域最大并发请求数
                    "burst_capacity": instance_concurrency * 2,
                    "throttling_enabled": True
                },
                "configured_at": time.time()
            }

            logger.info(f"[单实例并发] 配置并发度: {instance_concurrency}，最大实例数: {concurrency_config['max_instances']}")
            logger.info(f"[成本优化] 预计费用减少: {concurrency_config['billing_optimization']['estimated_cost_reduction']}")
            logger.info(f"[流控配置] 最大并发请求数: {concurrency_config['flow_control']['max_concurrent_requests']}")

            # 启动并发度监控
            TestService._start_concurrency_monitoring(task_id, concurrency_config)

            # 更新任务状态
            if task_id in test_tasks:
                test_tasks[task_id]["instance_concurrency"] = concurrency_config
                test_tasks[task_id]["status"] = f"🔧 已配置单实例并发度{instance_concurrency}，预计费用减少{concurrency_config['billing_optimization']['estimated_cost_reduction']}"

            logger.info(f"[单实例并发] 任务 {task_id} 并发度配置完成")

        except Exception as e:
            logger.error(f"[单实例并发] 配置单实例并发度时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _create_connection_object(connection_id: int) -> Dict:
        """
        创建连接对象

        Args:
            connection_id: 连接ID

        Returns:
            连接对象字典
        """
        try:
            import time
            import uuid

            connection_obj = {
                "id": connection_id,
                "uuid": str(uuid.uuid4()),
                "created_at": time.time(),
                "last_used": time.time(),
                "usage_count": 0,
                "status": "available",
                "websocket_ready": False,
                "sdk_object": None  # 这里应该是实际的SDK对象
            }

            # 模拟SDK对象初始化
            TestService._initialize_sdk_object(connection_obj)

            logger.info(f"[连接对象] 创建连接对象 {connection_id}，UUID: {connection_obj['uuid'][:8]}")
            return connection_obj

        except Exception as e:
            logger.error(f"[连接对象] 创建连接对象时出错: {str(e)}", exc_info=True)
            return {"id": connection_id, "status": "error", "error": str(e)}

    @staticmethod
    def _initialize_sdk_object(connection_obj: Dict):
        """
        初始化SDK对象（模拟WebSocket连接建立）

        Args:
            connection_obj: 连接对象
        """
        try:
            import time

            # 模拟WebSocket连接建立过程
            time.sleep(0.1)  # 模拟连接建立延迟

            connection_obj["websocket_ready"] = True
            connection_obj["sdk_object"] = {
                "type": "mock_sdk",
                "connection_id": connection_obj["id"],
                "ready": True
            }

            logger.info(f"[SDK初始化] 连接 {connection_obj['id']} SDK对象初始化完成")

        except Exception as e:
            logger.error(f"[SDK初始化] 初始化SDK对象时出错: {str(e)}", exc_info=True)
            connection_obj["websocket_ready"] = False

    @staticmethod
    def _start_concurrency_monitoring(task_id: str, concurrency_config: Dict):
        """
        启动并发度监控

        Args:
            task_id: 任务ID
            concurrency_config: 并发配置
        """
        try:
            import threading
            import time

            def concurrency_monitor():
                """并发度监控函数"""
                try:
                    logger.info(f"[并发监控] 任务 {task_id} 并发度监控启动")

                    monitor_interval = 30  # 30秒监控间隔

                    while True:
                        try:
                            # 检查连接池状态
                            if task_id in TestService._connection_pools:
                                pool = TestService._connection_pools[task_id]

                                with pool["lock"]:
                                    active_count = len(pool["active_connections"])
                                    pool_size = pool["pool"].qsize()
                                    stats = pool["pool_stats"]

                                    # 更新最大活跃连接数
                                    stats["max_active"] = max(stats["max_active"], active_count)

                                    logger.info(f"[并发监控] 活跃连接: {active_count}, 池中连接: {pool_size}, "
                                              f"创建: {stats['created']}, 复用: {stats['reused']}, 释放: {stats['released']}")

                                    # 检查是否需要扩展连接池
                                    if pool_size < 2 and active_count > concurrency_config["instance_concurrency"] * 0.8:
                                        TestService._expand_connection_pool(task_id, 2)

                            time.sleep(monitor_interval)

                        except Exception as e:
                            logger.error(f"[并发监控] 监控过程出错: {str(e)}")
                            time.sleep(monitor_interval)

                except Exception as e:
                    logger.error(f"[并发监控] 并发度监控启动失败: {str(e)}")

            # 启动监控线程
            if not hasattr(TestService, '_concurrency_monitors'):
                TestService._concurrency_monitors = {}

            monitor_thread = threading.Thread(
                target=concurrency_monitor,
                name=f"ConcurrencyMonitor-{task_id}"
            )
            monitor_thread.daemon = True
            monitor_thread.start()

            TestService._concurrency_monitors[task_id] = monitor_thread

            logger.info(f"[并发监控] 任务 {task_id} 并发度监控启动完成")

        except Exception as e:
            logger.error(f"[并发监控] 启动并发度监控时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _expand_connection_pool(task_id: str, expand_count: int):
        """
        扩展连接池

        Args:
            task_id: 任务ID
            expand_count: 扩展数量
        """
        try:
            if task_id not in TestService._connection_pools:
                return

            pool = TestService._connection_pools[task_id]

            with pool["lock"]:
                current_created = pool["pool_stats"]["created"]

                for i in range(expand_count):
                    connection_obj = TestService._create_connection_object(current_created + i)
                    pool["pool"].put(connection_obj)
                    pool["pool_stats"]["created"] += 1

                logger.info(f"[连接池扩展] 任务 {task_id} 连接池扩展 {expand_count} 个连接")

        except Exception as e:
            logger.error(f"[连接池扩展] 扩展连接池时出错: {str(e)}", exc_info=True)

    @staticmethod
    def get_connection_from_pool(task_id: str) -> Dict:
        """
        从连接池获取连接（支持连接复用）

        Args:
            task_id: 任务ID

        Returns:
            连接对象
        """
        try:
            import queue
            import time

            if task_id not in TestService._connection_pools:
                logger.warning(f"[连接获取] 任务 {task_id} 的连接池不存在")
                return None

            pool = TestService._connection_pools[task_id]

            with pool["lock"]:
                try:
                    # 尝试从池中获取连接
                    connection_obj = pool["pool"].get_nowait()

                    # 更新连接状态
                    connection_obj["status"] = "active"
                    connection_obj["last_used"] = time.time()
                    connection_obj["usage_count"] += 1

                    # 添加到活跃连接
                    pool["active_connections"][connection_obj["uuid"]] = connection_obj
                    pool["pool_stats"]["reused"] += 1

                    logger.info(f"[连接获取] 复用连接 {connection_obj['id']}，使用次数: {connection_obj['usage_count']}")
                    return connection_obj

                except queue.Empty:
                    # 池中无可用连接，创建新连接
                    connection_id = pool["pool_stats"]["created"]
                    connection_obj = TestService._create_connection_object(connection_id)

                    connection_obj["status"] = "active"
                    connection_obj["last_used"] = time.time()
                    connection_obj["usage_count"] = 1

                    pool["active_connections"][connection_obj["uuid"]] = connection_obj
                    pool["pool_stats"]["created"] += 1

                    logger.info(f"[连接获取] 创建新连接 {connection_obj['id']}")
                    return connection_obj

        except Exception as e:
            logger.error(f"[连接获取] 获取连接时出错: {str(e)}", exc_info=True)
            return None

    @staticmethod
    def release_connection_to_pool(task_id: str, connection_obj: Dict):
        """
        释放连接回连接池

        Args:
            task_id: 任务ID
            connection_obj: 连接对象
        """
        try:
            import queue
            import time

            if task_id not in TestService._connection_pools or not connection_obj:
                return

            pool = TestService._connection_pools[task_id]

            with pool["lock"]:
                # 从活跃连接中移除
                if connection_obj["uuid"] in pool["active_connections"]:
                    del pool["active_connections"][connection_obj["uuid"]]

                # 检查连接是否还可用
                if connection_obj.get("websocket_ready", False) and connection_obj.get("status") != "error":
                    # 重置连接状态
                    connection_obj["status"] = "available"
                    connection_obj["last_used"] = time.time()

                    try:
                        # 放回连接池
                        pool["pool"].put_nowait(connection_obj)
                        pool["pool_stats"]["released"] += 1

                        logger.info(f"[连接释放] 连接 {connection_obj['id']} 已释放回池，使用次数: {connection_obj['usage_count']}")
                    except queue.Full:
                        # 连接池已满，关闭连接
                        TestService._close_connection(connection_obj)
                        logger.info(f"[连接释放] 连接池已满，关闭连接 {connection_obj['id']}")
                else:
                    # 连接不可用，直接关闭
                    TestService._close_connection(connection_obj)
                    logger.info(f"[连接释放] 连接 {connection_obj['id']} 不可用，已关闭")

        except Exception as e:
            logger.error(f"[连接释放] 释放连接时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _close_connection(connection_obj: Dict):
        """
        关闭连接

        Args:
            connection_obj: 连接对象
        """
        try:
            # 模拟关闭WebSocket连接
            if connection_obj.get("sdk_object"):
                connection_obj["sdk_object"] = None

            connection_obj["websocket_ready"] = False
            connection_obj["status"] = "closed"

            logger.info(f"[连接关闭] 连接 {connection_obj['id']} 已关闭")

        except Exception as e:
            logger.error(f"[连接关闭] 关闭连接时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _configure_writing_protection(task_id: str, prompt_template: str) -> Dict:
        """
        配置写作功能保护，确保写作不受降本增效影响

        写作是最重要的功能，必须保证：
        1. 完全不限制Token数量
        2. 使用最高优先级配置
        3. 最大化模型性能
        4. 确保AI完全理解和执行指令

        Args:
            task_id: 任务ID
            prompt_template: 提示词模板

        Returns:
            写作保护配置
        """
        try:
            logger.info(f"[写作保护] 开始配置写作功能保护，任务ID: {task_id}")

            # 写作功能保护配置
            protection_config = {
                "function_type": "writing",
                "priority": "highest",
                "token_limit": "unlimited",  # 写作功能完全不限制Token
                "model_performance": "maximum",
                "instruction_understanding": "enhanced",  # 增强指令理解
                "quality_assurance": "strict",
                "cost_optimization": "disabled_for_writing",  # 写作功能禁用成本优化
                "configured_at": time.time()
            }

            # 根据模板调整配置
            if prompt_template == "simplified":
                protection_config.update({
                    "mode": "simplified_but_unlimited",
                    "description": "精简版模式但写作功能完全不限制",
                    "token_strategy": "unlimited_for_writing",
                    "ai_instruction_enhancement": True,  # 启用AI指令增强
                    "multi_layer_instruction": True,     # 启用多层指令确认
                    "instruction_verification": True    # 启用指令验证
                })
                logger.info(f"[写作保护] 精简版模式：写作功能完全不限制，启用AI指令增强")
            else:
                protection_config.update({
                    "mode": "default_unlimited",
                    "description": "默认版模式写作功能完全不限制",
                    "token_strategy": "unlimited_for_writing",
                    "ai_instruction_enhancement": False,  # 默认版不需要额外增强
                    "multi_layer_instruction": False,
                    "instruction_verification": False
                })
                logger.info(f"[写作保护] 默认版模式：写作功能完全不限制")

            # 应用写作保护策略
            TestService._apply_writing_protection_strategies(task_id, protection_config)

            # 更新任务状态
            if task_id in test_tasks:
                test_tasks[task_id]["writing_protection"] = protection_config
                test_tasks[task_id]["protection_status"] = "✅ 写作功能完全保护，不受任何限制"

            logger.info(f"[写作保护] 写作功能保护配置完成：{protection_config}")
            return protection_config

        except Exception as e:
            logger.error(f"[写作保护] 配置写作保护时出错: {str(e)}", exc_info=True)
            # 返回默认保护配置
            return {
                "function_type": "writing",
                "priority": "highest",
                "token_limit": "unlimited",
                "protection_status": "default_protection"
            }

    @staticmethod
    def _apply_writing_protection_strategies(task_id: str, protection_config: Dict):
        """
        应用写作保护策略

        Args:
            task_id: 任务ID
            protection_config: 保护配置
        """
        try:
            logger.info(f"[写作保护策略] 开始应用写作保护策略")

            # 1. 禁用写作相关的所有限制
            if protection_config.get("token_limit") == "unlimited":
                logger.info(f"[写作保护策略] ✅ 写作功能Token完全不限制")

            # 2. 设置最高优先级
            if protection_config.get("priority") == "highest":
                logger.info(f"[写作保护策略] ✅ 写作功能设置为最高优先级")

            # 3. 启用AI指令增强（仅精简版）
            if protection_config.get("ai_instruction_enhancement", False):
                TestService._setup_ai_instruction_enhancement(task_id)
                logger.info(f"[写作保护策略] ✅ 启用AI指令理解增强")

            # 4. 确保写作质量
            if protection_config.get("quality_assurance") == "strict":
                logger.info(f"[写作保护策略] ✅ 启用严格质量保证")

            logger.info(f"[写作保护策略] 写作保护策略应用完成")

        except Exception as e:
            logger.error(f"[写作保护策略] 应用写作保护策略时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _setup_ai_instruction_enhancement(task_id: str):
        """
        设置AI指令理解增强，解决AI理解执行不到位的问题

        通过多种策略确保AI完全理解和执行所有指令：
        1. 多层指令确认机制
        2. 关键指令重复强调
        3. 指令执行验证
        4. 分段指令处理

        Args:
            task_id: 任务ID
        """
        try:
            logger.info(f"[AI指令增强] 开始设置AI指令理解增强")

            # AI指令增强配置
            enhancement_config = {
                "multi_layer_confirmation": True,    # 多层确认
                "key_instruction_emphasis": True,    # 关键指令强调
                "instruction_verification": True,    # 指令验证
                "segmented_instruction": True,       # 分段指令
                "execution_checklist": True,        # 执行检查清单
                "feedback_loop": True,              # 反馈循环
                "instruction_priority": "critical"   # 指令优先级
            }

            # 更新任务配置
            if task_id in test_tasks:
                test_tasks[task_id]["ai_instruction_enhancement"] = enhancement_config
                test_tasks[task_id]["instruction_status"] = "🧠 AI指令理解增强已启用"

            logger.info(f"[AI指令增强] AI指令理解增强设置完成：{enhancement_config}")

        except Exception as e:
            logger.error(f"[AI指令增强] 设置AI指令增强时出错: {str(e)}", exc_info=True)

    @staticmethod
    def _create_comprehensive_instruction_checklist() -> str:
        """
        创建全面的AI指令理解检查清单，确保AI深刻理解并执行所有重要的写作提示词

        Returns:
            详细的指令检查清单
        """
        try:
            checklist = """
## 🧠 AI指令深度理解与执行检查清单（全面版）

### 📋 第一层：章节生成框架规划（8个步骤必须全部执行）
请确认您已深刻理解并将严格执行以下框架规划步骤：

**第一步：故事主线创新规划**
✅ 1.1 完全脱离原文情节，确保故事主线与原文完全不同
✅ 1.2 自由创造故事类型，基于创造力构思与原文截然不同的故事方向
✅ 1.3 独立思考核心冲突，设计与原文形成鲜明对比的矛盾冲突
✅ 1.4 自主规划情节发展，运用创作智慧设计独特的故事发展路径
✅ 1.5 保持章节连贯性，与前一章节的新故事线保持连贯

**第二步：场景环境重构规划**
✅ 2.1 完全更换故事背景，创造与原文截然不同的时间、地点、环境设定
✅ 2.2 自由构思场景设定，根据新故事需要创造独特的场景环境
✅ 2.3 场景功能设计，让每个场景都能自然服务于新故事情节
✅ 2.4 创新场景转换，设计符合新故事逻辑的场景过渡方式
✅ 2.5 环境描写创新，学习原文描写技巧但用来描述全新环境

**第三步：人物创新规划**
✅ 3.1 创造全新人物，设计与原文完全不同的人物角色
✅ 3.2 自由设定人物身份，根据新故事自主决定人物身份、职业、背景
✅ 3.3 人物命名创新，学习网络小说命名法，创造符合网文风格的人物名称
✅ 3.4 独创人物关系网络，设计与原文完全不同的人物关系结构
✅ 3.5 人物性格创新，学习原文刻画技巧但创造全新的性格特点
✅ 3.6 保持人物设定一致，与前一章节的人物设定保持一致
✅ 3.7 互动方式创新，学习原文对话风格但应用到新人物关系中

**第四步：维度创新性重组规划（12个技巧必须全部应用）**
✅ 4.1 语言风格传承：学习原文表达习惯、语言节奏、修辞特色，应用到全新内容
✅ 4.2 结构特色重组：学习原文章节架构、段落组织方式，应用到新故事框架
✅ 4.3 节奏控制创新：掌握原文节奏变化规律，应用到不同情节发展中
✅ 4.4 人物关系重构：学习原文互动模式，应用到全新人物关系网络中
✅ 4.5 世界构建革新：学习原文背景设定思路，应用到全新故事世界中
✅ 4.6 情感表达转化：学习原文情感渲染技巧，应用到不同情感内容中
✅ 4.7 开篇技巧应用：学习原文开场方式，应用到全新故事开头，确保开篇吸睛
✅ 4.8 高潮设计借鉴：学习原文高潮设计，应用到不同冲突和高潮中
✅ 4.9 风格特色融合：学习原文独特风格，融入到全新故事表达中
✅ 4.10 句式技巧运用：学习原文句式多样性，运用到新内容表达中
✅ 4.11 视角处理创新：学习原文叙述视角，应用到新故事叙述中
✅ 4.12 段落流畅度保持：学习原文段落转换，应用到新内容组织中

**第五步：创新性结构与节奏规划**
✅ 5.1 新故事的起承转合，为全新故事情节设计完整结构
✅ 5.2 节奏变化创新，学习原文节奏控制技巧，应用到新情节发展中
✅ 5.3 高潮设计重构，设计与原文不同但同样精彩的高潮部分
✅ 5.4 结构层次创新，保持原文结构美感，但承载全新内容
✅ 5.5 新故事的连贯性，确保本章节与前一章节的新故事线保持连贯

**第六步：字数分配与控制规划**
✅ 6.1 目标字数控制，约2000字，容忍范围1600-2400字
✅ 6.2 结构分配，开头15%、中间发展60%、结尾25%
✅ 6.3 实时追踪，在写作过程中使用注释标记字数进度
✅ 6.4 风格比例，学习原文的内容分配比例，应用到新内容中

**第七步：创新性语言风格规划**
✅ 7.1 语言风格传承，学习原文表达习惯和语言特色
✅ 7.2 叙述视角创新，选择适合新故事的叙述视角
✅ 7.3 对话与描写平衡，学习原文比例，用于新内容
✅ 7.4 句式技巧应用，运用原文句式变化技巧
✅ 7.5 风格一致性，与前一章节的语言风格保持一致
✅ 7.6 短句优先原则，严格学习原文短句风格，优先使用简单句和短句，避免复杂从句结构
✅ 7.7 生活化词汇要求，使用简短有力的生活化词汇，严禁华丽辞藻和文艺腔调
✅ 7.8 时代元素融入，适当加入2024年的流行元素
✅ 7.9 反AI化表达，严禁使用AI化表达方式
✅ 7.10 句式连贯性要求，对话和叙述之间必须自然过渡
✅ 7.11 逻辑性和合理性强化，任何新元素都要有合理出现原因
✅ 7.12 通俗生活化表达，使用简单易懂的日常用语，避免书面语和专业术语
✅ 7.13 逻辑性和因果关系强化，完整的因果链条

**第八步：创新性连贯规划**
✅ 8.1 新故事的连贯性，确保新故事情节的逻辑连贯
✅ 8.2 人物一致性，保持新人物的名称和性格一致
✅ 8.3 风格连贯性，在语言风格上保持与原文的相似性
✅ 8.4 情感发展连贯，确保新故事的情感发展合理
✅ 8.5 章节间衔接，确保与前一章节的新故事线自然衔接
✅ 8.6 质量检验，检查是否达到"学我者生"的创新标准

### 📋 第二层：创新性写作执行指南（核心原则必须全部遵守）

**核心原则：脱胎换骨，风格传承**
✅ 情节完全创新：绝对禁止模仿原文情节发展路径，构建完全不同的故事主线
✅ 场景环境重构：完全更换故事发生的时间、地点、背景
✅ 人物关系创新：创造全新的人物名称和身份设定
✅ 冲突设置重构：设计与原文完全不同的矛盾冲突

**具体执行要求**
✅ 字数控制：目标2000字，容忍范围1600-2400字
✅ 章节编号：必须为"# 第X章 [全新标题]"
✅ 人物命名：创造有特色但不过于奇怪的新人名
✅ 开头设计：与原文有明显差异但同样吸睛的开场方式
✅ 情节主线：与原文完全不同的故事发展路径
✅ 语言风格：保持原文的语言特色但内容全新
✅ 时代背景：可以适当融入2024年的时代元素

### 📋 第三层：严格禁止事项（11大类禁止事项必须全部遵守）

**1. 禁止情节模仿**：严禁使用原文的任何情节发展脉络
**2. 禁止场景复制**：严禁使用原文的具体场景和事件
**3. 禁止人名重复**：严禁使用原文中的任何人物名称
**4. 禁止直接引用**：严禁直接引用原文的任何段落或句子
**5. 禁止科幻元素**：严禁加入未来科技、赛博朋克等内容
**6. 禁止AI化表达**：严禁使用AI化的表达方式
**7. 禁止华丽辞藻**：严禁使用华丽辞藻、文艺腔调、书面语，必须使用简短有力的生活化词汇
**8. 禁止长句复句**：严禁使用复杂从句结构，必须优先使用短句，学习原文句式特点
**9. 逻辑性和合理性要求**：任何事件的出现都必须有合理的原因和解释
**10. 通俗生活化要求**：避免使用专业术语和生僻词汇
**11. 逻辑性和因果关系强化要求**：任何事物出现都要有明确原因

### 📋 第四层：质量检验标准（5个标准必须全部达到）

✅ 风格相似度：语言表达、节奏控制、情感渲染等方面与原文相似
✅ 内容创新度：情节、人物、场景、冲突等方面与原文完全不同
✅ 逻辑连贯性：新故事的发展逻辑清晰合理
✅ 字数准确性：符合目标字数范围
✅ 章节连贯性：与前后章节保持人物和情节的一致性

### 🚨 最终确认声明
请在开始写作前，逐项确认以上所有指令已完全理解：
- 我已理解并将严格执行章节生成框架规划的8个步骤
- 我已理解并将完全遵守创新性写作执行指南的所有要求
- 我已理解并将严格遵守11大类禁止事项
- 我已理解并将努力达到5个质量检验标准
- 我承诺创作出"学我者生"的高质量原创内容

只有确认理解所有指令后，才能开始创作。
"""

            return checklist

        except Exception as e:
            logger.error(f"[指令检查清单] 创建指令检查清单时出错: {str(e)}", exc_info=True)
            return "## AI指令理解检查清单创建失败，请使用默认指令"
